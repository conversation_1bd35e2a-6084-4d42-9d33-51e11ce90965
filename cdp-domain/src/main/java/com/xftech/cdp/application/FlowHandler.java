/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.application;

import com.xftech.cdp.domain.strategy.model.enums.DispatchTaskBizTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.FlowTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyFlowStatusEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.model.strategy.DispatchTaskExtBO;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.flow.StrategyFlowNodeService;
import com.xftech.cdp.domain.strategy.service.flow.StrategyFlowService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo;
import com.xftech.cdp.infra.utils.DateUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version $ FlowHandler, v 0.1 2023/12/21 17:26 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class FlowHandler {

    private final StrategyRepository strategyRepository;

    private final StrategyHandler strategyHandler;
    private final StrategyFlowService strategyFlowService;
    private final StrategyFlowNodeService strategyFlowNodeService;

    // 画布状态修改
    // 画布状态的变更 --->  已发布 -> 执行中;
    // 画布状态的变更 --->  (已发布,执行中,暂停) --> 过期;
    public void flowStatusChanged() {
        Date now = new Date();
        // 拉取可被变化的任务状态;
        List<Integer> flowStatusList = StrategyFlowStatusEnum.getCanChangedStatus();
        List<StrategyFlowDo> flowDoList = strategyFlowService.selectList(flowStatusList);
        for (StrategyFlowDo flowDo : flowDoList) {
            try {
                if (now.after(flowDo.getValidityEnd())) {
                    strategyFlowService.updateStatus(flowDo.getId(), flowDo.getStatus().intValue(),
                            StrategyFlowStatusEnum.ENDED.getCode());
                    continue;
                }
                if (Objects.equals(StrategyFlowStatusEnum.INIT.getCode(),
                        flowDo.getStatus().intValue())) {
                    if (flowDo.getValidityBegin().before(now) && flowDo.getValidityEnd().after(now)) {
                        strategyFlowService.updateStatus(flowDo.getId(), flowDo.getStatus().intValue(),
                                StrategyFlowStatusEnum.EXECUTING.getCode());
                    }
                    continue;
                }
                if (Objects.equals(StrategyFlowStatusEnum.EXECUTING.getCode(),
                        flowDo.getStatus().intValue())) {
                    if (flowDo.getValidityBegin().after(now)) {
                        strategyFlowService.updateStatus(flowDo.getId(), flowDo.getStatus().intValue(),
                                StrategyFlowStatusEnum.INIT.getCode());
                    }
                }
            } catch (Exception ex) {
                log.error("flowStatusChanged error, flowId = {}", flowDo.getId(), ex);
            }
        }
    }

    // 策略任务生成器
    // 遍历有效的画布 -> 画布节点关联的有效策略 -> 策略营销的渠道 -> 生成任务
    public void flowTaskProducer() {
        // 拉取可被变化的任务状态;
        List<Integer> flowStatusList = StrategyFlowStatusEnum.getExecutingStatus();
        List<StrategyFlowDo> flowDoList = strategyFlowService.selectList(flowStatusList);
        LocalDateTime now = LocalDateTime.now();
        // 只筛选离线策略
        flowDoList = flowDoList.stream().filter(x -> now.isAfter(DateUtil.convert(x.getValidityBegin()))
                        && now.isBefore(DateUtil.convert(x.getValidityEnd())))
                .filter(x -> FlowTypeEnum.isOffline(x.getFlowType()))
                .collect(Collectors.toList());
        log.info("DispatchTask任务生成 --> 待处理的有效的画布列表Id:{}", flowDoList.stream().map(StrategyFlowDo::getFlowNo)
                .collect(Collectors.toList()));

        byte bizType = (byte) DispatchTaskBizTypeEnum.FLOW.getCode();
        for (StrategyFlowDo flowDo : flowDoList) {
            List<StrategyFlowNodeDo> nodeDos = strategyFlowNodeService.select(flowDo.getFlowNo());
            log.info("画布任务生成, 画布id:{}, --> \n涉及的策略节点组:{}", flowDo.getFlowNo(), JsonUtil.toJson(nodeDos));
            for (StrategyFlowNodeDo node : nodeDos) {
                DispatchTaskExtBO.FlowExt flowExt = new DispatchTaskExtBO.FlowExt(flowDo.getFlowNo(), false, StrategyMarketChannelEnum.BLANK);
                StrategyDo strategyDo = strategyRepository.selectById(node.getStrategyId());
                if (StringUtils.isEmpty(node.getParentId())) {
                    // 根节点
                    flowExt.setRootNode(true);
                }
                strategyHandler.createStrategyDispatchTasks(strategyDo.getId(), strategyDo.getSendFrequency(), bizType, now, flowExt, null);
            }
        }
    }

    // 画布任务执行器
    public void flowTaskConsumer(int shardTotal, int shardIndex,
                                 int bizType) {
        strategyHandler.strategyDispatchTaskConsumer(shardTotal, shardIndex, bizType);
    }
}