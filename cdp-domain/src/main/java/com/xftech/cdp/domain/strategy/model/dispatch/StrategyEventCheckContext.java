package com.xftech.cdp.domain.strategy.model.dispatch;

import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketSubEventDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Data
public class StrategyEventCheckContext {

    private BizEventVO bizEventVO;

    private StrategyDo strategyDo;

    private StrategyMarketEventDo strategyMarketEventDo;

    private Map<Integer, List<StrategyMarketSubEventDo>> marketSubEventMap;

    public StrategyEventCheckContext() {
    }

    public StrategyEventCheckContext(BizEventVO bizEventVO) {
        this.bizEventVO = bizEventVO;
    }
}
