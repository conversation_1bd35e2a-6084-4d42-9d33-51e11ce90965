package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PushCallbackStatusEnum {

    FAILED(1, "失败"),
    UNKNOWN(2, "未知"),
    SUCCESS(3, "成功"),
    ;
    private Integer code;
    private String desc;

    public static PushCallbackStatusEnum getEnum(Integer code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(x.code, code))
                .findFirst().orElse(null);
    }


    public static Integer getDispatchStatus(Integer code) {
        if (SUCCESS.equals(getEnum(code))) {
            return 1;
        } else if (FAILED.equals(getEnum(code))) {
            return 0;
        }
        return null;
    }
}
