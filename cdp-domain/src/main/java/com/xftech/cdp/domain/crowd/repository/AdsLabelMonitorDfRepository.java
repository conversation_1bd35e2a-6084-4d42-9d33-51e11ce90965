package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @authoc Aven
 * @since 2023/2/14
 */

@Component
public class AdsLabelMonitorDfRepository {

    /**
     * @param dataDate
     * @param tableName
     * @return
     */
    @Deprecated
    public Boolean selectExistByDataDate(LocalDateTime dataDate, String tableName) {
        Map<String, Object> param = new HashMap<>();
        param.put("onlineFlag", 1);
        param.put("dataDate", dataDate);
        param.put("tableName", tableName);
        Long count = DBUtil.selectOne("ads", "adsLabelMonitor.selectExistByDataDate", param);
        return count > 0;
    }
}
