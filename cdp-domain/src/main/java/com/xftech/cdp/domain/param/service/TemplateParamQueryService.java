package com.xftech.cdp.domain.param.service;

import com.xftech.cdp.domain.dispatch.dto.DispatchDto;
import com.xftech.cdp.feign.model.PushUserData;
import com.xftech.cdp.feign.model.requset.SendPushRequest;
import com.xftech.cdp.infra.client.sms.model.SmsBatchWithParamArgs;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.rocketmq.dto.AiUserData;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/16 15:18
 */
public interface TemplateParamQueryService {

    /**
     * 获取用户短信参数
     *
     * @param reach 策略上下文
     * @param app   app
     * @param batch 手机号
     * @return 用户短信参数 left-模板是否带参，middle-过滤后的用户集合，right-过滤后的用户集合的模板参数
     */
    Triple<Boolean, List<CrowdDetailDo>, List<SmsBatchWithParamArgs.Sms>> smsParamBatchQuery(DispatchDto reach, String app, List<CrowdDetailDo> batch);


    Triple<Boolean, List<CrowdDetailDo>, List<PushUserData>> pushParamBatchQuery(DispatchDto dispatchDto, String app, List<CrowdDetailDo> crowdDetailList);

    Triple<Boolean, List<CrowdDetailDo>, List<AiUserData>> aiParamBatchQuery(DispatchDto dispatchDto, String app, List<CrowdDetailDo> crowdDetailList);
}
