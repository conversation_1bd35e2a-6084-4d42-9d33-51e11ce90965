package com.xftech.cdp.domain.strategy.service.dispatch.offline;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/24 16:58
 */
public interface StrategyDispatchForSmsService {
    /**
     * 策略批次更新
     *
     * @param crowdPushBatchDo   下发批次记录
     */
    void smsStrategyPushBatchUpdate(CrowdPushBatchDo crowdPushBatchDo);

    /**
     * 策略批次更新
     * @param crowdPushBatchDo 下发批次记录
     */
    void couponStrategyPushBatchUpdate(CrowdPushBatchDo crowdPushBatchDo);
}
