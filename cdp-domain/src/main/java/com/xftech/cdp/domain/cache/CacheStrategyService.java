package com.xftech.cdp.domain.cache;

import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;

import java.util.List;

/**
 * 策略表操作
 */
public interface CacheStrategyService {

    /**
     * 根据主键id查询某条策略
     *
     * @param strategyId 策略id
     * @return id对应的记录
     */
    StrategyDo selectById(Long strategyId);

    StrategyDo selectByIdNoCache(Long strategyId);

    /**
     * 根据主键id删除某条策略
     *
     * @param strategyId 策略主键id
     * @return 是否删除成功标识
     */
//    boolean delete(Long strategyId);

    /**
     * 根据主键id更新某条策略记录
     *
     * @param updateStrategy 策略对象
     * @return 是否更新成功标识
     */
    boolean updateById(StrategyDo updateStrategy);

    void batchUpdate(List<StrategyDo> list);

    boolean insert(StrategyDo strategyDo);

    /**
     * 刷新T0（事件策略）状态为执行中的执行列表
     */
    void refreshT0ExecutingStrategy();

    StrategyDo refreshStrategy(Long strategyId, StrategyDo strategyDo);

    void delCacheStrategy(Long strategyId);

}
