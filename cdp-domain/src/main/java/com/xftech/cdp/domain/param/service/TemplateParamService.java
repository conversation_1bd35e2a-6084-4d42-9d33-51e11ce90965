package com.xftech.cdp.domain.param.service;

import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.param.TemplateParamCreateReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamDetailReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamListReq;
import com.xftech.cdp.api.dto.resp.param.TemplateParamListResp;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;

import java.util.List;
import java.util.Map;

public interface TemplateParamService {

    /**
     * 新建模板参数
     *
     * @param templateParamCreateReq 请求参数
     * @return 是否新建成功
     */
    boolean create(TemplateParamCreateReq templateParamCreateReq);

    /**
     * 更新模板参数
     *
     * @param templateParamCreateReq 请求参数
     * @return 是否更新成功
     */
    boolean update(TemplateParamCreateReq templateParamCreateReq);

    /**
     * 分页获取模板参数列表
     *
     * @param templateParamListReq 请求参数
     * @return 当前页模板参数列表
     */
    PageResultResponse<TemplateParamListResp> list(TemplateParamListReq templateParamListReq);

    /**
     * 获取模板参数详情
     *
     * @param templateParamDetailReq 请求参数
     * @return 流控规则详情
     */
    TemplateParamListResp getDetail(TemplateParamDetailReq templateParamDetailReq);

    /**
     * 模板参数校验
     *
     * @param marketChannel 渠道
     * @param template      模板内容
     */
    Pair<Boolean, Integer> templateParamCheck(StrategyMarketChannelEnum marketChannel, String app, String template);

    /**
     * 获取模板
     *
     * @param marketChannel 渠道
     * @param app           app
     * @param templateId    模板ID
     * @return 模板
     */
    String getTemplate(StrategyMarketChannelEnum marketChannel, String app, String templateId);

    /**
     * 获取模板中的参数
     *
     * @param template 模板
     * @return 参数集合
     */
    List<String> getTemplateParam(String template);

    /**
     * 模板参数匹配
     *
     * @param tempContent   模板及模板参数 Left-渠道，Middle-模板，Right-参数集合
     * @param crowdDetailDo 当前用户
     * @return 返回所有用户的目前参数
     */
    Map<Long, Map<String, Object>> templateParamMatch(Pair<Integer, String> tempContent, CrowdDetailDo crowdDetailDo, Long strategyId);

    /**
     * 获取短信模板参数
     *
     * @param crowdDetail 用户明细
     * @param templateId  模板ID
     * @return 短信模板参数
     */
    Map<String, Object> getSmsTempParam(CrowdDetailDo crowdDetail, Long strategyId, String templateId, BizEventVO bizEvent);


    /**
     * 模板参数匹配
     *
     * @param tempContent     模板及模板参数 Left-渠道，Middle-模板，Right-参数集合
     * @param crowdDetailList 当前用户
     * @return 返回所有用户的目前参数
     */
    Map<Long, Map<String, Object>> templateParamBatchMatch(Pair<Integer, String> tempContent, List<CrowdDetailDo> crowdDetailList, Long strategyId, String app);

    /**
     * 批量获取短信模板参数
     *
     * @param strategyId      策略ID
     * @param app             app
     * @param templateId      模板ID
     * @param crowdDetailList 用户集合
     * @return 短信模板参数
     */
    Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchSmsTempParam(Long strategyId, String app, String templateId, List<CrowdDetailDo> crowdDetailList);

    // 验证短信参数
    boolean checkTemplateContent(String app, String templateId, Map dataMap, boolean removeFlag, Long strategyId);
    boolean checkPushTemplateContent(String app, String templateId, Map dataMap, boolean removeFlag, Long strategyId);

    String testConfig(String type, List<String> name) throws InterruptedException;

    Map<String, Object> getPushTempParam(CrowdDetailDo crowdDetail, Long strategyId, String strategyMarketChannelTemplateId, BizEventVO bizEvent);

    Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchPushTempParam(Long strategyId, String app, String strategyMarketChannelTemplateId, List<CrowdDetailDo> batch);

    Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchAiTempParam(Long strategyId, String app, AiProntoChannelDto aiProntoChannelDto, List<CrowdDetailDo> batch);

    Map<String, Object> getAiTempParam(CrowdDetailDo crowdDetail, Long strategyId, AiProntoChannelDto aiProntoChannelDto);
}
