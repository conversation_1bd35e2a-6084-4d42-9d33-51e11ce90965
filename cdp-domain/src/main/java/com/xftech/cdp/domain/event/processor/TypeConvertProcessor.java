package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.ConvertConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import com.xftech.cdp.domain.event.model.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 类型转换数据处理器
 * <AUTHOR>
 * @version $ TypeConvertProcessor, v 0.1 2024/11/18 21:36 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.CONVERT)
public class TypeConvertProcessor extends AbstractDataProcessor {
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        ConvertConfig convertConfig = (ConvertConfig) fieldConfig;
        Object originValue = values.get(detail.getTargetField());
        DataTypeEnum targetType = DataTypeEnum.getDataType(convertConfig.getTargetType());
        if(targetType == null){
            log.error("TypeConvertProcessor dataType miss match.detail={},fieldConfig={}",detail,fieldConfig);
            return null;
        }

        Object processedValue = null;
        if(StringUtils.isNotEmpty(convertConfig.getDefaultValue())){
            Object defaultValue = Convert.convert(targetType.getClazz(),convertConfig.getDefaultValue());
            processedValue = Convert.convert(targetType.getClazz(),originValue,defaultValue);
        }else {
            processedValue = Convert.convert(targetType.getClazz(),originValue);
        }

        return new Field(detail.getTargetField(),processedValue);
    }
}
