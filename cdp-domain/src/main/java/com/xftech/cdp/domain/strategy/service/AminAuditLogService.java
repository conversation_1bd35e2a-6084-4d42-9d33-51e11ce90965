/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.xftech.cdp.domain.strategy.repository.AdminAuditLogRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.AdminAuditLogDo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $ AminAuditLogService, v 0.1 2024/1/2 20:27 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class AminAuditLogService {
   private final AdminAuditLogRepository adminAuditLogRepository;

    @Async("adminTaskExecutor")
    public void insert(AdminAuditLogDo adminAuditLogDo) {
       adminAuditLogRepository.insert(adminAuditLogDo);
   }

   @Async("adminTaskExecutor")
   public void insert(String opType, String content, String opName, String mobile){
       AdminAuditLogDo adminAuditLogDo = new AdminAuditLogDo();
       adminAuditLogDo.setOpType(opType);
       adminAuditLogDo.setOpContent(content);
       adminAuditLogDo.setOpMobile(mobile);
       adminAuditLogDo.setOpName(opName);
       this.insert(adminAuditLogDo);
   }
}