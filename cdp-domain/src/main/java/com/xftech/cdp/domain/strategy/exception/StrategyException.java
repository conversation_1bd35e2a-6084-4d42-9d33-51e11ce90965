package com.xftech.cdp.domain.strategy.exception;

import com.xftech.cdp.infra.exception.BizException;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR> wanghu
 * @since : 2022/7/26 14:38:55
 */
@Getter
@Setter
public class StrategyException extends BizException {

    private int code;

    public StrategyException(String message) {
        super(message);
    }

    public StrategyException(int code, String message) {
        super(message);
        this.code = code;
    }

    public StrategyException(String message, Throwable cause) {
        super(message, cause);
    }

    public StrategyException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

    public StrategyException(Throwable cause) {
        super(cause);
    }

}
