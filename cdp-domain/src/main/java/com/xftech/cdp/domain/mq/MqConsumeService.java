package com.xftech.cdp.domain.mq;

import com.xftech.cdp.api.dto.req.external.PushReportReq;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.rabbitmq.vo.CouponCallbackVO;
import com.xftech.cdp.infra.rabbitmq.vo.SmsReportVO;
import com.xftech.cdp.infra.rocketmq.dto.AiCallBackMessageVO;
import org.springframework.amqp.core.Message;

import java.util.List;

public interface MqConsumeService {
    void smsReportProcess(List<SmsReportVO> smsReportVOList);

    void pushReportProcess(PushReportReq pushReportReq);

    void couponCallbackProcess(List<CouponCallbackVO> couponCallbackVOList);

    void bizEventProcess(String messageId, BizEventMessageVO bizEventMessageVO);

    void bizEventDelayProcess(BizEventVO bizEventVO);

    void bizEventDispatchProcess(Message message);

    void bizEventDecisionResultProcess(List<BizEventVO> bizEventVOList);

    void aiCallbackProcess(AiCallBackMessageVO aiCallBackMessageVO);
}