package com.xftech.cdp.domain.touch.model;

/**
 * 触达类型枚举
 * 用于标识不同的触达方式
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public enum TouchType {
    
    /**
     * T0普通触达
     * 对应：StrategyEventDispatchServiceImpl.execSend()
     */
    T0_NORMAL("T0_NORMAL", "T0普通触达", "实时事件触发的普通触达"),
    
    /**
     * T0引擎触达
     * 对应：StrategyEventDispatchServiceImpl.marketingSend() (T0场景)
     */
    T0_ENGINE("T0_ENGINE", "T0引擎触达", "实时事件触发的引擎决策触达"),
    
    /**
     * 离线普通触达
     * 对应：AbstractStrategyDispatchService.dispatchHandler()
     */
    OFFLINE_NORMAL("OFFLINE_NORMAL", "离线普通触达", "基于人群包的离线普通触达"),
    
    /**
     * 离线引擎触达
     * 对应：StrategyEventDispatchServiceImpl.marketingSend() (离线场景)
     */
    OFFLINE_ENGINE("OFFLINE_ENGINE", "离线引擎触达", "基于人群包的离线引擎决策触达");
    
    private final String code;
    private final String name;
    private final String description;
    
    TouchType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TouchType fromCode(String code) {
        for (TouchType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的触达类型代码: " + code);
    }
    
    /**
     * 判断是否为T0触达
     */
    public boolean isT0Touch() {
        return this == T0_NORMAL || this == T0_ENGINE;
    }
    
    /**
     * 判断是否为离线触达
     */
    public boolean isOfflineTouch() {
        return this == OFFLINE_NORMAL || this == OFFLINE_ENGINE;
    }
    
    /**
     * 判断是否为引擎触达
     */
    public boolean isEngineTouch() {
        return this == T0_ENGINE || this == OFFLINE_ENGINE;
    }
    
    /**
     * 判断是否为普通触达
     */
    public boolean isNormalTouch() {
        return this == T0_NORMAL || this == OFFLINE_NORMAL;
    }
}
