package com.xftech.cdp.domain.strategy.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Component
public class StrategyInstantLabelRepository {

    /**
     * 根据主键id查询
     *
     * @param id id
     * @return id对应的记录
     */
    public StrategyInstantLabelDo selectById(Long id) {
        return DBUtil.selectOne("strategyInstantLabelMapper.selectByPrimaryKey", id);
    }

    /**
     * 插入
     *
     * @param param 对象
     * @return 是否插入成功标识
     */
    public boolean insert(StrategyInstantLabelDo param) {
        return DBUtil.insert("strategyInstantLabelMapper.insertSelective", param) > 0;
    }

    /**
     * 根据主键id更新
     *
     * @param param 对象
     * @return 是否更新成功标识
     */
    public boolean updateById(StrategyInstantLabelDo param) {
        return DBUtil.update("strategyInstantLabelMapper.updateByPrimaryKeySelective", param) > 0;
    }

    /**
     * 根据标签名字查询策略实时标签
     *
     * @param labelName 标签名字
     * @return 策略实时标签
     */
    public StrategyInstantLabelDo getByLabelNameAndLabelType(String labelName, Integer labelType, Integer strategyType, Integer optional) {
        Map<String, Object> param = new HashMap<>();
        param.put("labelName", labelName);
        param.put("labelType", labelType);
        param.put("strategyType", labelType.equals(StrategyInstantLabelTypeEnum.SMS_PARAM.getType()) ? null : strategyType);
        if (optional != null) {
            param.put("optional", optional);
        }
        return DBUtil.selectOne("strategyInstantLabelMapper.getByLabelNameAndLabelType", param);
    }

    /**
     * 根据标签名称查询标签配置请求
     *
     * @param labelNameList 标签名称集合
     * @return 标签配置请求
     */
    public List<StrategyInstantLabelDo> getByLabelNameList(List<String> labelNameList) {
        return DBUtil.selectList("strategyInstantLabelMapper.getByLabelNameList", labelNameList);
    }

    public List<StrategyInstantLabelDo> selectCacheInfo() {
        return DBUtil.selectList("strategyInstantLabelMapper.selectCacheInfo", null);
    }

    public List<StrategyInstantLabelDo> selectEventLabelByExcludeTypeAndBusinessType(List<Integer> excludeType, String businessType, Integer strategyType) {
        Map<String, Object> param = new HashMap<>();
        param.put("excludeType", excludeType);
        param.put("businessType", businessType);
        param.put("strategyType", strategyType);
        return DBUtil.selectList("strategyInstantLabelMapper.selectEventLabelByExcludeTypeAndBusinessType", param);
    }

    public List<StrategyInstantLabelDo> selectByLabelTypeAndBusinessType(Integer labelType, String businessType, Integer strategyType, Integer strategyModel) {
        Map<String, Object> param = new HashMap<>();
        param.put("labelType", labelType);
        param.put("businessType", businessType);
        param.put("strategyType", strategyType);
        if (strategyModel != null) {
            param.put("strategyModel", strategyModel);
        }
        return DBUtil.selectList("strategyInstantLabelMapper.selectByLabelTypeAndBusinessType", param);
    }
}
