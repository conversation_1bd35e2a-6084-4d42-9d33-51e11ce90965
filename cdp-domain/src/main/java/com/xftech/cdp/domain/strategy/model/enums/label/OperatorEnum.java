package com.xftech.cdp.domain.strategy.model.enums.label;

import com.google.common.collect.Maps;
import com.xftech.cdp.domain.strategy.exception.StrategyException;

import java.util.Map;


/**
 * 符号枚举
 */

public enum OperatorEnum {
    GE(">=", "大于等于",">="),
    LE("<=", "小于等于","<="),
    EQ("==", "等于","="),
    GT(">", "大于",">"),
    LT("<", "小于","<"),
    NEQ("!=", "不等于","!="),
    CONTAIN("contain", "属于","contain"),
    NOTCONTAIN("notcontain", "不属于","notcontain"),
    HASVAL("has", "有值","notnull"),
    NOHASVAL("no has", "没值","isnull"),
    NULL("null", "空","isnull"),
    NONULL("no null", "不为空","notnull"),
    BETWEEN("between", "之间","between"),
    LIKE("like", "类似","like"),
    NOTLIKE("notlike", "不类似","not like"),
    ALL("all","全部","===");
    private String description;
    private String code;
    private String dbCode;

    private static Map<String, OperatorEnum> dbCodeMap;

    OperatorEnum(String code, String description, String dbCode) {
        this.code = code;
        this.description = description;
        this.dbCode = dbCode;
    }


    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getDbCode(){
        return dbCode;
    }

    public static Map<String, OperatorEnum> getDbCodeMap() {
        return dbCodeMap;
    }


    public static OperatorEnum getInstance(String operateType) {
        for (OperatorEnum operatorEnum : OperatorEnum.values()) {
            if (operatorEnum.name().equalsIgnoreCase(operateType)) {
                return operatorEnum;
            }
        }
        throw new StrategyException("操作符类型异常");
    }

    static  {
        dbCodeMap = Maps.newHashMap();
        for (OperatorEnum value : OperatorEnum.values()) {
            dbCodeMap.put(value.getCode().trim(), value);
        }
    }
}
