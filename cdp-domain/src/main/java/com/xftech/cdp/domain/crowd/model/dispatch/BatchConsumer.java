package com.xftech.cdp.domain.crowd.model.dispatch;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 */
public class BatchConsumer<T> {
    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */

    private static final int BATCH_COUNT = 300;
    private final int batchCount;

    private final Consumer<List<T>> consumer;

    private List<T> list;

    public BatchConsumer(int batchCount, Consumer<List<T>> consumer) {
        this.batchCount = batchCount;
        this.consumer = consumer;
        this.list = new ArrayList<>(batchCount);
    }

    public BatchConsumer(Consumer<List<T>> consumer) {
        this.batchCount = BATCH_COUNT;
        this.consumer = consumer;
        this.list = new ArrayList<>(batchCount);
    }

    public void invoke(T t) {
        list.add(t);
        if (list.size() >= batchCount) {
            consumer.accept(list);
        }
    }

    public void finallyList() {
        if (!list.isEmpty()) {
            consumer.accept(list);
            list = null;
        }
    }


    public void invoke(List<T> t) {
        list.addAll(t);
        if (list.size() < batchCount) {
            return;
        } else {
            consumer.accept(list);
            list.clear();
        }
    }
}
