/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.dispatch.offline.impl;

import com.xftech.cdp.domain.dispatch.BatchDispatchService;
import com.xftech.cdp.domain.strategy.model.dispatch.StrategyContext;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchForIncreaseAmountService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.config.StrategyConfig;
import com.xftech.cdp.infra.constant.StrategyDispatchConstants;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StrategyDispatchForIncreaseAmountServiceImpl, v 0.1 2024/3/13 14:38 yye.xu Exp $
 */
@Slf4j
@AllArgsConstructor
@Service(StrategyDispatchConstants.INCREASE_AMOUNT_SERVICE)
public class StrategyDispatchForIncreaseAmountServiceImpl extends AbstractStrategyDispatchService implements StrategyDispatchService, StrategyDispatchForIncreaseAmountService {
    private StrategyConfig strategyConfig;
    private BatchDispatchService batchDispatchService;

    @Override
    protected Integer setBatchSize() {
        return strategyConfig.getIncreaseAmtBatchSize();
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> dispatchHandler(StrategyContext strategyContext, String app, String innerApp, List<CrowdDetailDo> batch, List<T> templateParam) {
        log.info("开始进行提额下发, 策略id:{}, 渠道id:{}, 本次数量:{}", strategyContext.getStrategyDo().getId(), strategyContext.getStrategyMarketChannelDo().getId(), batch.size());
        return batchDispatchService.sendBatchIncreaseAmt(convertToDispatchDto(strategyContext), app, innerApp, batch);
    }

    @Override
    protected <T> ImmutablePair<Integer, CrowdPushBatchDo> retryDispatchHandler(StrategyContext context, String tableNameNo, String app, String innerApp, List<CrowdDetailDo> detailList, List<T> templateParam) {
        log.info("开始进行提额下发重试, 策略id:{}, 渠道id:{}, 本次数量:{}", context.getStrategyDo().getId(), context.getStrategyMarketChannelDo().getId(), detailList.size());
        return batchDispatchService.sendBatchIncreaseAmt(convertToDispatchDto(context), app, innerApp, detailList);
    }
}