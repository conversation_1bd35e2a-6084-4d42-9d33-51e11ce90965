package com.xftech.cdp.domain.strategy.repository;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DecisionRecordDo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DecisionRecordRepository {

//    public boolean insert(DecisionRecordDo record) {
//        return DBUtil.insert("decisionRecordMapper.insertDecisionRecord", record) > 0;
//    }

    public boolean insertBatch(List<DecisionRecordDo> decisionRecordDoList) {
        return DBUtil.insertBatchWithoutTx("decisionRecordMapper.insertDecisionRecord", decisionRecordDoList) > 0;
    }

    public List<Long> queryDecisionRecordStrategyIds(String tableNameNo, String startDate, String endDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tableName", getTableName(tableNameNo));
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        return DBUtil.selectList("decisionRecordMapper.queryDecisionRecordStrategyIds", paramMap);
    }

    public Integer countDecisionStrategy(String tableNameNo, Long strategyId, String startDate, String endDate) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tableName", getTableName(tableNameNo));
        paramMap.put("strategyId", strategyId);
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        return DBUtil.selectOne("decisionRecordMapper.countDecisionStrategy", paramMap);
    }

    public Integer countCurrentDecisionByGroupId(String tableNameNo, Long strategyId, Long strategyGroupId, LocalDateTime startTime, LocalDateTime endTime) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tableName", getTableName(tableNameNo));
        paramMap.put("strategyId", strategyId);
        paramMap.put("strategyGroupId", strategyGroupId);
        paramMap.put("timeStart", startTime);
        paramMap.put("timeEnd", endTime);
        return DBUtil.selectOne("decisionRecordMapper.countCurrentDecisionByGroupId", paramMap);
    }

    public Integer countDecisionUserFlowData(String tableNameNo, Long strategyId, String startDate, String endDate, Integer failCode) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tableName", getTableName(tableNameNo));
        paramMap.put("strategyId", strategyId);
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        paramMap.put("failCode", failCode);
        return DBUtil.selectOne("decisionRecordMapper.countDecisionUserFlowData", paramMap);
    }

    public Integer countDecisionResultUserNum(String tableNameNo, Long strategyId, String startDate, String endDate, Integer decisionResult) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("tableName", getTableName(tableNameNo));
        paramMap.put("strategyId", strategyId);
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        paramMap.put("decisionResult", decisionResult);
        return DBUtil.selectOne("decisionRecordMapper.countDecisionResultUserNum", paramMap);
    }

    /**
     * 根据表序号生成表名
     *
     * @param tableNameNo 表序号
     * @return 表名
     */
    private String getTableName(String tableNameNo) {
        return "decision_record_" + tableNameNo;
    }

}