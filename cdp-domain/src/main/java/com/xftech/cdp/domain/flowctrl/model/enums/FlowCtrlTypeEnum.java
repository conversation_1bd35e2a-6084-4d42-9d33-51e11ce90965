package com.xftech.cdp.domain.flowctrl.model.enums;

import com.xftech.cdp.infra.exception.BizException;
import lombok.Getter;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 流控规则类型
 *
 * @<NAME_EMAIL>
 * @date 2023/3/24 17:08
 */
@Getter
public enum FlowCtrlTypeEnum {
    /**
     * 渠道
     */
    CHANNEL(2, 1, "渠道"),

    /**
     * 策略
     */
    STRATEGY(1, 2, "策略"),
    /**
     * 多策略共享
     */
    MULTI_STRATEGY(3, 3, "多策略共享"),
    /**
     * 业务线
     */
    BIZ_LINE(4, 4, "业务线");


    private final Integer type;
    private final Integer sort;
    private final String desc;

    FlowCtrlTypeEnum(Integer type, Integer sort, String desc) {
        this.type = type;
        this.sort = sort;
        this.desc = desc;
    }

    public static FlowCtrlTypeEnum getInstance(Integer type) {
        for (FlowCtrlTypeEnum flowCtrlTypeEnum : FlowCtrlTypeEnum.values()) {
            if (Objects.equals(flowCtrlTypeEnum.getType(), type)) {
                return flowCtrlTypeEnum;
            }
        }
        throw new BizException(String.format("流控规则类型异常，状态：%s", type));
    }

    public static List<FlowCtrlTypeEnum> getList() {
        return Arrays.stream(FlowCtrlTypeEnum.values())
                .sorted(Comparator.comparing(FlowCtrlTypeEnum::getSort))
                .collect(Collectors.toList());
    }
}
