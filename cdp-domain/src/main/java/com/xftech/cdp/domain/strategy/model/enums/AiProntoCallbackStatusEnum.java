/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ AiProntoCallbackStatusEnum, v 0.1 2024/8/26 15:44 lingang.han Exp $
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AiProntoCallbackStatusEnum {
    FILTER("FILTER", "被过滤"),
    CALLED("CALLED", "已拨打"),
    FAILURE("FAILURE", "推送AI中台失败"),

    ;
    private String code;
    private String desc;

    public static AiProntoCallbackStatusEnum getEnum(String code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(x.code, code))
                .findFirst().orElse(null);
    }
}