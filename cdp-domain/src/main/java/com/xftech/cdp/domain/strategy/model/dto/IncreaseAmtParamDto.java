/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.model.dto;

import com.xftech.cdp.infra.utils.DateUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * @version $ IncreaseAmtParamDto, v 0.1 2024/3/14 14:10 yye.xu Exp $
 */

@Data
@Slf4j
public class IncreaseAmtParamDto {
    /**
     * 提额类型 TODO使用枚举重写
     */
    private String increaseType;
    private int amount;
    private String startTime;
    private String endTime;

    public boolean isValid() {
        if (StringUtils.equalsAny(increaseType, IncreaseAmtDto.IncreaseType.PERSONAL_API_FST_LOGIN_TEMP, IncreaseAmtDto.IncreaseType.LOAN_UPTO_FULLAMT)) {
            return true;
        }
        boolean ret = amount > 0 && StringUtils.isNotEmpty(startTime) && StringUtils.isNotEmpty(endTime)
                && startTime.contains("00:00:00") && endTime.contains("00:00:00");
        if (!ret) {
            return false;
        }
        try {
            Date start = DateUtils.parseDate(startTime, DateUtil.NOMAL_DATE_FORMAT);
            Date end = DateUtils.parseDate(endTime, DateUtil.NOMAL_DATE_FORMAT);
            return end.after(start);
        } catch (Exception ex) {
            log.error("error", ex);
        }
        return false;
    }
}