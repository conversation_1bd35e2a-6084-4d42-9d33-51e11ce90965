package com.xftech.cdp.domain.event.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.xftech.cdp.domain.event.FieldConfigParseFactory;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.dto.MqEventFieldMappingConfig;
import com.xftech.cdp.domain.mqevent.MqEventFieldMappingConfigRepository;
import com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 消息事件配置信息服务
 * <AUTHOR>
 * @version $ MqEventFieldConfigService, v 0.1 2024/11/13 16:54 snail Exp $
 */
@Service
public class MqEventFieldConfigService {

    @Autowired
    private MqEventFieldMappingConfigRepository mappingConfigRepository;

    //TODO:need add local cache.
    public MqEventFieldMappingConfig getMqEventFieldMappingConfig(String topic, String consumer, String tag){
        MqEventFieldMappingConfigDo mappingConfigDo = mappingConfigRepository.selectByTopicAndConsumer(topic,consumer,tag);
        if(Objects.isNull(mappingConfigDo)){
            return null;
        }

        return convert(mappingConfigDo);
    }

    /** 配置信息转换，将配置信息反序列化为对应的实体信息 */
    private MqEventFieldMappingConfig convert(MqEventFieldMappingConfigDo mappingConfigDo){
        MqEventFieldMappingConfig result = new MqEventFieldMappingConfig();
        BeanUtils.copyProperties(mappingConfigDo,result);
        result.setFieldList(parseFieldDetail(mappingConfigDo.getFieldMapping()));

        return result;
    }

    /** 解析某个字段映射的信息 */
    private List<FieldDetail> parseFieldDetail(String fieldMapping){
        if(StringUtils.isEmpty(fieldMapping)){
            return Collections.EMPTY_LIST;
        }
        List<FieldDetail> result = new ArrayList<>();

        JSONArray array = JSONArray.parseArray(fieldMapping);
        Iterator it = array.iterator();
        while (it.hasNext()){
            JSONObject object = (JSONObject) it.next();
            FieldDetail detail = JSON.parseObject(object.toJSONString(), FieldDetail.class);
            if(!CollectionUtils.isEmpty(detail.getProcessList())){
                detail.setProcessList(parseFieldConfig(object));
            }
            result.add(detail);
        }

        return result;
    }

    /** 解析某个字段的处理器信息 */
    private List<FieldConfig> parseFieldConfig(JSONObject fieldDetail){
        JSONArray fieldConfigArray = fieldDetail.getJSONArray(EventConfigConstants.FIELD_CONFIG_KEY);
        Iterator it = fieldConfigArray.iterator();

        List<FieldConfig> result = new ArrayList<>();
        while (it.hasNext()){
            JSONObject filedConfigItem = (JSONObject) it.next();
            String fieldConfigProcess = filedConfigItem.getString(EventConfigConstants.FIELD_CONFIG_PROCESSOR_KEY);
            FieldConfig fieldConfig = FieldConfigParseFactory.parse(fieldConfigProcess,filedConfigItem.toJSONString());
            result.add(fieldConfig);
        }

        return result;
    }
}
