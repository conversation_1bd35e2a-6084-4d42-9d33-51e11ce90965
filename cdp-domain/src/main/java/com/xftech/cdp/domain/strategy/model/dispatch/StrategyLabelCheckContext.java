package com.xftech.cdp.domain.strategy.model.dispatch;

import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventConditionDo;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @<NAME_EMAIL>
 */
@Data
public class StrategyLabelCheckContext {
    /**
     * 事件消息
     */
    private BizEventVO bizEventVO;
    /**
     * 策略标签配置
     */
    private Map<String, List<StrategyMarketEventConditionDo>> marketEventConditionMap;
    /**
     * 用户各个标签值
     */
    private Map<Long, Map<String, Object>> labelValueMap;

    public StrategyLabelCheckContext() {
    }

    public StrategyLabelCheckContext(BizEventVO bizEventVO) {
        this.bizEventVO = bizEventVO;
    }
}
