/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service;

import com.dianping.cat.proxy.Tracer;
import com.xftech.cdp.domain.strategy.model.enums.DispatchTaskStatusEnum;
import com.xftech.cdp.domain.strategy.repository.DispatchTaskRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.DispatchTaskDo;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ DispatchTaskService, v 0.1 2023/11/2 21:42 yye.xu Exp $
 */


@Service
@AllArgsConstructor
public class DispatchTaskService {
    private DispatchTaskRepository dispatchTaskRepository;

    public List<DispatchTaskDo> selectTodoList(Byte bizType, int dateValue, Date dispatchTime, List<Integer> statusList) {
        return dispatchTaskRepository.selectTodoList(bizType, dateValue, dispatchTime, statusList);
    }

    public List<DispatchTaskDo> selectList(String bizId, String associationId, Byte bizType, Integer dateValue, List<Integer> statusList) {
        return dispatchTaskRepository.selectList(bizId, associationId, bizType, dateValue, statusList);
    }

    public List<DispatchTaskDo> selectLastExecutingTask(String bizId, int fromStatus) {
        int dateValue = DateUtil.dayOfInt(DateUtil.convert(LocalDateTime.now()));
        return dispatchTaskRepository.selectLastExecutingTask(bizId, fromStatus, dateValue);
    }

    public void add(DispatchTaskDo dispatchTaskDo) {
        dispatchTaskRepository.insertSelective(dispatchTaskDo);
    }

    public void updateDispatchTime(Long id, Date dispatchTime, Date nextDispatchTime) {
        dispatchTaskRepository.updateDispatchTime(id, dispatchTime, nextDispatchTime);
    }

    public int updateTaskStatus(long id, int fromStatus, int toStatus) {
       return dispatchTaskRepository.updateTaskStatus(id, fromStatus, toStatus);
    }

    public int updateTaskStatus(long id, int fromStatus, int toStatus, String execRetMsg) {
        return dispatchTaskRepository.updateTaskFinish(id, fromStatus, toStatus, execRetMsg);
    }

    public int updateTaskStatus(long id, int fromStatus, int toStatus, int retryTimes, Date nextTime, String execRetMsg) {
        return dispatchTaskRepository.updateTaskStatus(id, fromStatus, toStatus, retryTimes, nextTime, execRetMsg);
    }

    public int updateTaskFinish(DispatchTaskDo dispatchTaskDo, String execRetMsg) {
        if (dispatchTaskDo == null) {
            return 0;
        }
        Tracer.logEvent("DispatchTaskFinish", dispatchTaskDo.getBizId());
        String retMsg = (StringUtils.isNotEmpty(dispatchTaskDo.getExecRetMsg()) ? dispatchTaskDo.getExecRetMsg() + ";" : "")
                + execRetMsg;
        return updateTaskStatus(dispatchTaskDo.getId(), DispatchTaskStatusEnum.EXECUTING.getCode(),
                DispatchTaskStatusEnum.SUCCEED.getCode(), retMsg);
    }

    public int updateTaskFailed(DispatchTaskDo dispatchTaskDo, String msg) {
        if (dispatchTaskDo == null) {
            return 0;
        }
        Tracer.logEvent("DispatchTaskFailed", dispatchTaskDo.getBizId());
        String execRetMsg = (StringUtils.isNotEmpty(dispatchTaskDo.getExecRetMsg()) ? dispatchTaskDo.getExecRetMsg() + ";" : "")
                + msg;
        return updateTaskStatus(dispatchTaskDo.getId(), DispatchTaskStatusEnum.EXECUTING.getCode(),
                DispatchTaskStatusEnum.FAILED.getCode(),
                execRetMsg);
    }

    public void updateDispatchTaskReset(DispatchTaskDo dispatchTaskDo, String msg) {
        if (dispatchTaskDo == null) {
            return;
        }
        Tracer.logEvent("DispatchTaskReset", dispatchTaskDo.getBizId());
        String execRetMsg = (StringUtils.isNotEmpty(dispatchTaskDo.getExecRetMsg()) ? dispatchTaskDo.getExecRetMsg() + ";" : "")
                + msg;
        updateTaskStatus(dispatchTaskDo.getId(), DispatchTaskStatusEnum.EXECUTING.getCode(),
                DispatchTaskStatusEnum.INIT.getCode(), execRetMsg);
    }

    public void updateDispatchTaskFailedRetry(DispatchTaskDo dispatchTaskDo, String msg) {
        if (dispatchTaskDo == null) {
            return;
        }
        Tracer.logEvent("DispatchTaskFailedRetry", dispatchTaskDo.getBizId());
        String execRetMsg = (StringUtils.isNotEmpty(dispatchTaskDo.getExecRetMsg()) ? dispatchTaskDo.getExecRetMsg() + ";" : "")
                + msg;
        dispatchTaskDo.setRetryTimes(dispatchTaskDo.getRetryTimes() + 1);
        dispatchTaskDo.setNextDispatchTime(DateUtils.addMinutes(new Date(), 15));
        updateTaskStatus(dispatchTaskDo.getId(), DispatchTaskStatusEnum.EXECUTING.getCode(),
                DispatchTaskStatusEnum.INIT.getCode(), dispatchTaskDo.getRetryTimes(), dispatchTaskDo.getNextDispatchTime(), execRetMsg);
    }
}