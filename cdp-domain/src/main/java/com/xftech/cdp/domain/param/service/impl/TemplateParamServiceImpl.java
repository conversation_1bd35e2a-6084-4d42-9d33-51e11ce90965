package com.xftech.cdp.domain.param.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.ConfigService;
import com.dianping.cat.proxy.Tracer;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.req.param.TemplateParamCreateReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamDetailReq;
import com.xftech.cdp.api.dto.req.param.TemplateParamListReq;
import com.xftech.cdp.api.dto.resp.param.TemplateParamListResp;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheSmsService;
import com.xftech.cdp.domain.flowctrl.model.enums.StrategyTypeEnum;
import com.xftech.cdp.domain.param.repository.TemplateParamRepository;
import com.xftech.cdp.domain.param.service.ReplaceStrategy;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.domain.strategy.model.dto.AiProntoChannelDto;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.vo.BatchAdsLabelVO;
import com.xftech.cdp.feign.PushFeignClient;
import com.xftech.cdp.feign.model.PushTemplateDetail;
import com.xftech.cdp.feign.model.requset.PushBaseRequest;
import com.xftech.cdp.feign.model.requset.TemplateDetailRequest;
import com.xftech.cdp.feign.model.response.PushResponse;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItem;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.*;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.param.po.TemplateParamDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.OperateLogObjectIdUtils;
import com.xftech.cdp.infra.utils.SsoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class TemplateParamServiceImpl implements TemplateParamService {

    @Autowired
    private CacheSmsService cacheSmsService;
    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;
    @Autowired
    private TemplateParamRepository templateParamRepository;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private Config config;
    @Autowired
    private CrowdConfig crowdConfig;
    @Autowired
    private HttpCustomConfig httpCustomConfig;
    @Autowired
    private StrategyConfig strategyConfig;
    @Autowired
    private TempParamReplacer tempParamReplacer;
    @Autowired
    private UserCenterClient userCenterClient;
    @Autowired
    private PushFeignClient pushFeignClient;


    /**
     * 创建模板参数
     *
     * @param templateParamCreateReq 请求参数
     * @return 是否保存成功
     */
    @Override
    public boolean create(TemplateParamCreateReq templateParamCreateReq) {
        TemplateParamDo existDo = templateParamRepository.selectByName(templateParamCreateReq.getName());
        if (Objects.nonNull(existDo)) {
            throw new BizException("参数名为:" + templateParamCreateReq.getName() + "已存在");
        }
        TemplateParamDo existKey = templateParamRepository.selectByKey(templateParamCreateReq.getParamKey());
        if (Objects.nonNull(existKey)) {
            throw new BizException("key值:" + templateParamCreateReq.getParamKey() + "已存在");
        }
        TemplateParamDo templateParamDo = new TemplateParamDo();
        templateParamDo.setName(templateParamCreateReq.getName());
        templateParamDo.setDescription(templateParamCreateReq.getDescription());
        templateParamDo.setParamKey(templateParamCreateReq.getParamKey());
        templateParamDo.setDemandSide(templateParamCreateReq.getDemandSide());
        templateParamDo.setCreatedOp(SsoUtil.get().getName());
        templateParamDo.setUpdatedOp(SsoUtil.get().getName());
        boolean result = templateParamRepository.insert(templateParamDo);
        OperateLogObjectIdUtils.set(Collections.singletonList(templateParamDo.getId()));
        return result;
    }

    /**
     * 更新模板参数
     *
     * @param templateParamCreateReq 请求参数
     * @return 是否更新成功
     */
    @Override
    public boolean update(TemplateParamCreateReq templateParamCreateReq) {
        TemplateParamDo templateParamDo = templateParamRepository.selectById(templateParamCreateReq.getId());
        if (templateParamDo == null) {
            throw new BizException("参数编号为:" + templateParamCreateReq.getId() + "不存在");
        }
        TemplateParamDo existDo = templateParamRepository.selectByName(templateParamCreateReq.getName());
        if (Objects.nonNull(existDo) && !Objects.equals(existDo.getId(), templateParamCreateReq.getId())) {
            throw new BizException("参数名为:" + templateParamCreateReq.getName() + "已存在");
        }
        TemplateParamDo existKey = templateParamRepository.selectByKey(templateParamCreateReq.getParamKey());
        if (Objects.nonNull(existKey) && !Objects.equals(existKey.getId(), templateParamCreateReq.getId())) {
            throw new BizException("key值:" + templateParamCreateReq.getParamKey() + "已存在");
        }
        templateParamDo.setName(templateParamCreateReq.getName());
        templateParamDo.setDescription(templateParamCreateReq.getDescription());
        templateParamDo.setParamKey(templateParamCreateReq.getParamKey());
        templateParamDo.setDemandSide(templateParamCreateReq.getDemandSide());
        templateParamDo.setUpdatedOp(SsoUtil.get().getName());
        OperateLogObjectIdUtils.set(Collections.singletonList(templateParamDo.getId()));
        return templateParamRepository.updateById(templateParamDo);
    }

    @Override
    public PageResultResponse<TemplateParamListResp> list(TemplateParamListReq templateParamListReq) {
        Page<TemplateParamDo> records = templateParamRepository.selectByPage(templateParamListReq);
        List<TemplateParamDo> list = records.getList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<TemplateParamListResp> templateParamListRespList = list.stream().map(item -> {
            TemplateParamListResp templateParamListResp = new TemplateParamListResp();
            templateParamListResp.setId(item.getId());
            templateParamListResp.setName(item.getName());
            templateParamListResp.setDescription(item.getDescription());
            templateParamListResp.setParamKey(item.getParamKey());
            templateParamListResp.setDemandSide(item.getDemandSide());
            templateParamListResp.setCreateTime(item.getCreatedTime());
            return templateParamListResp;
        }).collect(Collectors.toList());
        return new PageResultResponse<>(templateParamListRespList, records.getBeginNum(), records.getFetchNum(), records.getTotalNum());
    }

    @Override
    public TemplateParamListResp getDetail(TemplateParamDetailReq templateParamDetailReq) {
        TemplateParamDo templateParamDo = templateParamRepository.selectById(templateParamDetailReq.getId());
        if (templateParamDo == null) {
            throw new BizException("参数编号为:" + templateParamDetailReq.getId() + "不存在");
        }
        TemplateParamListResp templateParamListResp = new TemplateParamListResp();
        templateParamListResp.setName(templateParamDo.getName());
        templateParamListResp.setParamKey(templateParamDo.getParamKey());
        templateParamListResp.setDescription(templateParamDo.getDescription());
        templateParamListResp.setDemandSide(templateParamDo.getDemandSide());
        templateParamListResp.setCreateTime(templateParamDo.getCreatedTime());
        return templateParamListResp;
    }

    /**
     * 模板参数校验
     * <p>
     * 1. 先判断模板中是否包含参数
     * - 不包含参数，检查通过
     * 2. 识别模板中的参数个数
     * 3. 查询麻雀模板参数管理表中的参数
     * 4. 使用麻雀模板参数管理表中的参数循环匹配模板中的参数
     * - 匹配到的参数个数等于识别到参数个数时，检查通过，提前终止循环匹配
     * - 匹配到的参数个数不等于识别到参数个数时，报错处理【参数配置有误，请检查后重试！】
     *
     * @param marketChannel 渠道
     * @param template      模板编号
     */
    @Override
    public Pair<Boolean, Integer> templateParamCheck(StrategyMarketChannelEnum marketChannel, String app, String template) {
        if (StringUtils.isBlank(template)) {
            throw new StrategyException("模板不能为空");
        }

        int paramCount = countParam(template, "#");
        log.info("渠道：{}，该模板参数个数：{}", marketChannel, paramCount);

        if (paramCount == 0) {
            log.info("不存在参数，无需验证");
            return Pair.of(null, paramCount);
        }

        int matchCount = 0;
        List<TemplateParamDo> paramList = templateParamRepository.getAll();
        for (TemplateParamDo param : paramList) {
            matchCount += countParam(template, param.getParamKey());
            if (Objects.equals(paramCount, matchCount)) {
                log.info("该模板参数验证通过！！！");
                return Pair.of(Boolean.TRUE, paramCount);
            }
        }
        log.info("该模板参数验证不通过，模板包含参数个数：{}，匹配到参数个数：{}", paramCount, matchCount);
        return Pair.of(Boolean.FALSE, paramCount);
    }

    /**
     * 统计模板中参数的数量
     *
     * @param template 模板
     * @param ch       参数前缀
     * @return 数量
     */
    public static int countParam(String template, String ch) {
        Matcher matcher = Pattern.compile(String.valueOf(ch)).matcher(template);
        int res = 0;
        while (matcher.find()) {
            if (StringUtils.contains(ch, "#")) {
                res++;
            }
        }
        return res;
    }

    /**
     * 获取模板
     *
     * @param marketChannel 渠道
     * @param app           app
     * @param templateId    模板ID
     * @return 模板
     */
    @Override
    public String getTemplate(StrategyMarketChannelEnum marketChannel, String app, String templateId) {
        String template = null;
        switch (marketChannel) {
            case SMS:
                SmsItem smsItem = cacheSmsService.queryTemplateDetail(templateId, app);
                if (Objects.nonNull(smsItem)) {
                    template = smsItem.getTemplate();
                }
                break;
            case PUSH:
                PushBaseRequest<TemplateDetailRequest> request = new PushBaseRequest<>();
                TemplateDetailRequest detailRequest = new TemplateDetailRequest();
                detailRequest.setTemplateId(templateId);
                detailRequest.setApp(app);
                request.setArgs(detailRequest);
                request.setTimestamp(String.valueOf(Instant.now().getEpochSecond()));
                PushResponse<PushTemplateDetail> pushTemplateDetailPushResponse = pushFeignClient.loadPushTemplateDetail(request);
                log.info("loadPushTemplateDetail request:{},response:{}", request, pushTemplateDetailPushResponse);
                if (Objects.isNull(pushTemplateDetailPushResponse) || !pushTemplateDetailPushResponse.isSuccess() || Objects.isNull(pushTemplateDetailPushResponse.getResponse())) {
                    throw new StrategyException("查询push模板详情接口失败");
                }
                template = JSON.toJSONString(pushTemplateDetailPushResponse.getResponse());

                break;
            case VOICE:

                break;
            case SALE_TICKET:

                break;
            default:
                throw new StrategyException("渠道类型异常");
        }
        log.info("渠道：{}，App：{}，模板：{}", marketChannel.getCode(), app, template);
        return template;
    }

    /**
     * 获取模板中的参数
     *
     * @param template 模板
     * @return 参数集合
     */
    @Override
    public List<String> getTemplateParam(String template) {
        List<TemplateParamDo> paramList = templateParamRepository.getAll();
        return paramList.stream()
                .map(TemplateParamDo::getParamKey)
                .filter(key -> StringUtils.contains(template, key))
                .map(item -> CharSequenceUtil.subAfter(item, "#", true))
                .filter(StringUtils::isNotBlank)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 模板参数匹配
     *
     * @param tempContent   模板及模板参数 Left-渠道，Middle-模板，Right-参数集合
     * @param crowdDetailDo 当前用户
     * @return 返回所有用户的目前参数
     */
    @Override
    public Map<Long, Map<String, Object>> templateParamMatch(Pair<Integer, String> tempContent, CrowdDetailDo crowdDetailDo, Long strategyId) {
        List<String> templateParam;
        if (Objects.equals(StrategyMarketChannelEnum.AI_PRONTO.getCode(), tempContent.getLeft())) {
            templateParam = Arrays.asList(tempContent.getRight().split(","));
        } else {
            templateParam = this.getTemplateParam(tempContent.getRight());
        }
        log.info("模板参数匹配，参数：{}", templateParam);

        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setApp(crowdDetailDo.getApp());
        bizEventVO.setMobile(crowdDetailDo.getMobile());
        bizEventVO.setAppUserId(crowdDetailDo.getUserId());
        bizEventVO.setStrategyId(strategyId);
        return adsStrategyLabelService.query(bizEventVO, templateParam, StrategyInstantLabelTypeEnum.SMS_PARAM);
    }

    @Override
    public Map<Long, Map<String, Object>> templateParamBatchMatch(Pair<Integer, String> tempContent, List<CrowdDetailDo> crowdDetailList, Long strategyId, String app) {
        List<String> templateParam;
        if (Objects.equals(StrategyMarketChannelEnum.AI_PRONTO.getCode(), tempContent.getLeft())) {
            templateParam = Arrays.asList(tempContent.getRight().split(","));
        } else {
            templateParam = this.getTemplateParam(tempContent.getRight());
        }
        log.info("模板参数匹配,参数:{}", templateParam);

        BatchAdsLabelVO adsLabelVO = new BatchAdsLabelVO();
        adsLabelVO.setStrategyId(strategyId);
        adsLabelVO.setApp(app);
        adsLabelVO.setStartTime(null);
        List<BatchAdsLabelVO.UserInfo> userInfoList = crowdDetailList.stream().map(crowdDetailDo -> {
            BatchAdsLabelVO.UserInfo userInfo = new BatchAdsLabelVO.UserInfo();
            userInfo.setMobile(crowdDetailDo.getMobile());
            userInfo.setAppUserId(crowdDetailDo.getUserId());
            userInfo.setRegisterTime(crowdDetailDo.getRegisterTime());
            return userInfo;
        }).collect(Collectors.toList());
        adsLabelVO.setUserInfoList(userInfoList);
        return adsStrategyLabelService.queryBatch(adsLabelVO, templateParam, StrategyInstantLabelTypeEnum.SMS_PARAM, StrategyTypeEnum.OFFLINE_STRATEGY);
    }

    /**
     * 获取短信模板参数
     *
     * @param crowdDetail 用户明细
     * @param strategyId  策略ID
     * @param templateId  模板ID
     * @return 短信模板参数
     */
    @Override
    public Map<String, Object> getSmsTempParam(CrowdDetailDo crowdDetail, Long strategyId, String templateId, BizEventVO bizEventVO) {
        Map<String, Object> tempParam = new HashMap<>();
        Map<String, Object> up = new HashMap<>();
        String template = this.getTemplate(StrategyMarketChannelEnum.SMS, crowdDetail.getApp(), templateId);
        log.info("短信模板：{}", template);
        if (StringUtils.isEmpty(template)) {
            Tracer.logEvent("NoSmsTemplate", String.format("%s:%s:%s", strategyId, templateId, crowdDetail.getApp()));
            log.warn("无短信模板内容, 策略id:{}, templateId：{}, app:{}", strategyId, template, crowdDetail.getApp());
        }

        Pair<Boolean, Integer> paramCheck = this.templateParamCheck(StrategyMarketChannelEnum.SMS, crowdDetail.getApp(), template);
        if (null == paramCheck.getLeft()) {
            log.info("该短信模板不存在参数");
            return tempParam;
        }

        if (Boolean.FALSE.equals(paramCheck.getLeft())) {
            throw new StrategyException("参数配置有误，请检查后重试");
        }
        //非ads参数查询
        Map<String, Supplier<ReplaceStrategy>> mp = tempParamReplacer.getReplacementMap();
        boolean templateContainsMpKey = mp.keySet().stream().anyMatch(template::contains);
        if (templateContainsMpKey) {
            UserInfoResp user = userCenterClient.getUserByUserId(crowdDetail.getUserId());
            up.putAll(tempParamReplacer.replaceElements(template, user));
            template = mp.keySet().stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        List<String> bizEventParamList = tempParamReplacer.getBizEventParamList();
        boolean bizEventParamFlag = bizEventParamList.stream().anyMatch(template::contains);
        if (bizEventParamFlag) {
            up.putAll(tempParamReplacer.bizEventReplaceElements(template, bizEventVO));
            template = bizEventParamList.stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        List<String> templateParam = getTemplateParam(template);
        if (!CollectionUtils.isEmpty(templateParam)) {
            Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.SMS.getCode(), template);
            tempParam = this.templateParamMatch(tempContent, crowdDetail, strategyId).get(crowdDetail.getUserId());
        }
        tempParam.putAll(up);
        adsStrategyLabelService.filterLabelMinValue(tempParam, crowdDetail.getUserId(), strategyId);
        if (tempParam.keySet().size() < paramCheck.getRight()) {
            throw new StrategyException("模板参数不全，终止发送");
        }
        tempParam.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        log.info("短信模板参数查询结果，用户ID：{}，标签结果：{}，tempParamReplacer replaceElements 自定义结果：{}", crowdDetail.getUserId(), JSON.toJSONString(tempParam),JsonUtil.toJson(up));
        return tempParam;
    }

    /**
     * 获取短信模板参数
     *
     * @param crowdDetailList 用户明细
     * @param strategyId      策略ID
     * @param templateId      模板ID
     * @return 短信模板参数
     */
    @Override
    public Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchSmsTempParam(Long strategyId, String app, String templateId, List<CrowdDetailDo> crowdDetailList) {
        Map<Long, Map<String, Object>> tempParam = new HashMap<>();
        Map<Long, Map<String, Object>> cus = new HashMap<>();
        String template = this.getTemplate(StrategyMarketChannelEnum.SMS, app, templateId);
        log.info("短信模板：{}", template);
        if (StringUtils.isEmpty(template)) {
            log.warn("模板不存在,strategyId:{},templateId:{},app:{}", strategyId, templateId, app);
            Tracer.logEvent("NoSmsTemplate", String.format("%s:%s:%s", strategyId, templateId, app));
        }
        Pair<Boolean, Integer> paramCheck = this.templateParamCheck(StrategyMarketChannelEnum.SMS, app, template);
        if (null == paramCheck.getLeft()) {
            log.info("该短信模板不存在参数");
            return Triple.of(Boolean.FALSE, 0, tempParam);
        }

        if (Boolean.FALSE.equals(paramCheck.getLeft())) {
            throw new StrategyException("参数配置有误，请检查后重试");
        }
        //非ads参数查询
        Map<String, Supplier<ReplaceStrategy>> mp = tempParamReplacer.getReplacementMap();
        boolean templateContainsMpKey = mp.keySet().stream().anyMatch(template::contains);
        if (templateContainsMpKey) {
            List<Long> uids = crowdDetailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList());
            List<List<Long>> batches = ListUtils.partition(uids, 200);
            for (List<Long> batch : batches) {
                List<UserInfoResp> respDtos = userCenterClient.queryBatchUserByUserNo(batch);
                for (UserInfoResp r : respDtos) {
                    try {
                        Map<String, Object> up = tempParamReplacer.replaceElements(template, r);
                        cus.put(r.getCreditUserId(), up);
                    } catch (Exception e) {
                        log.error("user data is null,user:{}", JsonUtil.toJson(r), e);
                    }

                }
            }
            template = mp.keySet().stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        List<String> templateParam = getTemplateParam(template);
        if (!CollectionUtils.isEmpty(templateParam)) {
            Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.SMS.getCode(), template);
            tempParam = this.templateParamBatchMatch(tempContent, crowdDetailList, strategyId, app);
        }
        log.info("短信模板参数查询结果，标签结果：{}，tempParamReplacer batchreplaceElements：{} ", JSON.toJSONString(tempParam),JsonUtil.toJson(tempParam));
        tempParam=mergeMaps(tempParam,cus);
        tempParam.forEach((appUserId, paramValueMap) -> {
            paramValueMap.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        });
        return Triple.of(Boolean.TRUE, paramCheck.getRight(), tempParam);
    }

    public  Map<Long, Map<String, Object>> mergeMaps(Map<Long, Map<String, Object>> map1, Map<Long, Map<String, Object>> map2) {
        return Stream.of(map1, map2)
                .filter(Objects::nonNull)
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existingValue, newValue) -> {
                            existingValue.putAll(newValue);
                            return existingValue;
                        },
                        HashMap::new
                ));
    }

    @Override
    public boolean checkTemplateContent(String app, String templateId, Map dataMap, boolean removeFlag, Long strategyId) {
        String template = getTemplate(StrategyMarketChannelEnum.SMS, app, templateId);
        if (StringUtils.isEmpty(template)) { // 同时补充策略ID
            log.error("无短信模板内容, 模板id:{}, app:{}, 策略id:{}", templateId, app, strategyId, NoneException.catTemplateParamError());
            return false;
        }
        if (template.contains("#")) {
            if (CollectionUtils.isEmpty(dataMap)) {
                if (removeFlag) {
                    log.error("短信内容参数验证不通过, 模板id:{}, 短信内容:{}, 策略id:{}", templateId, template, strategyId);
                } else {
                    log.error("短信内容参数验证不通过, 模板id:{}, 短信内容:{}, 策略id:{}", templateId, template, strategyId, NoneException.catTemplateParamError());
                }
                return false;
            }
            for (Object k : dataMap.keySet()) {
                template = template.replace("#" + k, String.valueOf(dataMap.get(k)));
            }
            if (template.contains("#")) {
                log.error("短信内容参数验证不通过, 模板id:{}, 短信内容:{},短信模板参数:{}, 策略id:{}", templateId, template, dataMap, strategyId, NoneException.catTemplateParamError());
                return false;
            }
        }
        return true;
    }
    @Override
    public boolean checkPushTemplateContent(String app, String templateId, Map dataMap, boolean removeFlag, Long strategyId) {
        try {
            String template = getTemplate(StrategyMarketChannelEnum.PUSH, app, templateId);
            if (StringUtils.isEmpty(template)) { // 同时补充策略ID
                log.error("无PUSH模板内容, 模板id:{}, app:{}, 策略id:{}", templateId, app, strategyId, NoneException.catTemplateParamError());
                return false;
            }
            if (template.contains("#")) {
                if (CollectionUtils.isEmpty(dataMap)) {
                    if (removeFlag) {
                        log.error("PUSH内容参数验证不通过, 模板id:{}, 短信内容:{}, 策略id:{}", templateId, template, strategyId);
                    } else {
                        log.error("PUSH内容参数验证不通过, 模板id:{}, 短信内容:{}, 策略id:{}", templateId, template, strategyId, NoneException.catTemplateParamError());
                    }
                    return false;
                }
                for (Object k : dataMap.keySet()) {
                    template = template.replace("#" + k, String.valueOf(dataMap.get(k)));
                }
                if (template.contains("#")) {
                    log.error("PUSH内容参数验证不通过, 模板id:{}, PUSH内容:{},PUSH模板参数:{}, 策略id:{}", templateId, template, dataMap, strategyId, NoneException.catTemplateParamError());
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("检查PUSH模板内容时发生异常, 模板id:{}, app:{}, 策略id:{}", templateId, app, strategyId, e);
            return false;
        }
    }
    @Override
    public String testConfig(String type, List<String> name) throws InterruptedException {
        //type=1 ,根据配置key list获取配置值，  type=2,返回各个配置类的一些数据
        StringBuilder sb = new StringBuilder();
        if ("1".equals(type)) {
            name.forEach(n -> {
                sb.append("key=").append(n).append("value=").append(JsonUtil.toJson(ConfigService.getAppConfig().getProperty(n, null))).append(";  ");
            });
        } else {
            /*sb.append("key=xf.model.platform.prediction").append("value=").append(JsonUtil.toJson(appConfigService.getXfModelPlatformPredictionUrl())).append(";  ");
            sb.append("key=adb.realTime.variable.strategy.gray").append("value=").append(JsonUtil.toJson(appConfigService.getAdbStrategyGrayConfig())).append(";  ");
            sb.append("key=singleDispatchFlc.lock.ignore").append("value=").append(JsonUtil.toJson(appConfigService.getSingleIgnoreDispatchFlcLockSwitch())).append(";  ");
            sb.append("key=dispatch.ignore.errmsg").append("value=").append(JsonUtil.toJson(appConfigService.getIgnoreDispatchErrorMsgAlarm(StrategyMarketChannelEnum.SMS))).append(";  ");
            sb.append("key=spring.application.name").append("value=").append(JsonUtil.toJson(config.getApplicationName())).append(";  ");
            sb.append("key=sms.query.report.day").append("value=").append(JsonUtil.toJson(config.getSmsQueryReportDay())).append(";  ");
            sb.append("key=api.ext.sign.enable").append("value=").append(JsonUtil.toJson(config.getApplicationName())).append(";  ");
            sb.append("key=cdp.crowd.alarmUrl").append("value=").append(JsonUtil.toJson(crowdConfig.getAlarmUrl())).append(";  ");
            sb.append("key=cdp.crowd.batchSaveSize").append("value=").append(JsonUtil.toJson(crowdConfig.getBatchSaveSize() )).append(";  ");
            sb.append("key=crowd.refresh.timeout.atMobile").append("value=").append(JsonUtil.toJson(crowdConfig.getCrowdRefreshTimeoutAtMobiles() )).append(";  ");
            RequestConfig cc = httpCustomConfig.getCustomRequestConfig();
            sb.append("customRequestConfig config=").append(cc.getConnectTimeout()).append(cc.getSocketTimeout()).append(cc.getConnectionRequestTimeout());
            sb.append("key=strategy.realtime.timeout.retryTimes").append("value=").append(JsonUtil.toJson(strategyConfig.getRetryTimes()));
            sb.append("sleep 2 min================================================");
            Thread.sleep(10000);
            sb.append("key=xf.model.platform.prediction").append("value=").append(JsonUtil.toJson(appConfigService.getXfModelPlatformPredictionUrl())).append(";  ");
            sb.append("key=adb.realTime.variable.strategy.gray").append("value=").append(JsonUtil.toJson(appConfigService.getAdbStrategyGrayConfig())).append(";  ");
            sb.append("key=singleDispatchFlc.lock.ignore").append("value=").append(JsonUtil.toJson(appConfigService.getSingleIgnoreDispatchFlcLockSwitch())).append(";  ");
            sb.append("key=dispatch.ignore.errmsg").append("value=").append(JsonUtil.toJson(appConfigService.getIgnoreDispatchErrorMsgAlarm(StrategyMarketChannelEnum.SMS))).append(";  ");
            sb.append("key=spring.application.name").append("value=").append(JsonUtil.toJson(config.getApplicationName())).append(";  ");
            sb.append("key=sms.query.report.day").append("value=").append(JsonUtil.toJson(config.getSmsQueryReportDay())).append(";  ");
            sb.append("key=api.ext.sign.enable").append("value=").append(JsonUtil.toJson(config.getApplicationName())).append(";  ");
            sb.append("key=cdp.crowd.alarmUrl").append("value=").append(JsonUtil.toJson(crowdConfig.getAlarmUrl())).append(";  ");
            sb.append("key=cdp.crowd.batchSaveSize").append("value=").append(JsonUtil.toJson(crowdConfig.getBatchSaveSize() )).append(";  ");
            sb.append("key=crowd.refresh.timeout.atMobile").append("value=").append(JsonUtil.toJson(crowdConfig.getCrowdRefreshTimeoutAtMobiles() )).append(";  ");
            RequestConfig cc2 = httpCustomConfig.getCustomRequestConfig();
            sb.append("customRequestConfig config=").append(cc.getConnectTimeout()).append(cc.getSocketTimeout()).append(cc2.getConnectionRequestTimeout());
            sb.append("key=strategy.realtime.timeout.retryTimes").append("value=").append(JsonUtil.toJson(strategyConfig.getRetryTimes()));*/


        }
        return sb.toString();
    }

    @Override
    public Map<String, Object> getPushTempParam(CrowdDetailDo crowdDetail, Long strategyId, String templateId, BizEventVO bizEvent) {
        Map<String, Object> tempParam = new HashMap<>();
        Map<String, String> up = new HashMap<>();
        String template = this.getTemplate(StrategyMarketChannelEnum.PUSH, crowdDetail.getApp(), templateId);
        if (StringUtils.isEmpty(template)) {
            Tracer.logEvent("NoPushTemplate", String.format("%s:%s:%s", strategyId, templateId, crowdDetail.getApp()));
            log.warn("无push模板内容, 策略id:{}, templateId：{}, app:{}", strategyId, template, crowdDetail.getApp());
        }

        Pair<Boolean, Integer> paramCheck = this.templateParamCheck(StrategyMarketChannelEnum.PUSH, crowdDetail.getApp(), template);
        if (null == paramCheck.getLeft()) {
            log.info("该push模板不存在参数");
            return tempParam;
        }

        if (Boolean.FALSE.equals(paramCheck.getLeft())) {
            throw new StrategyException("参数配置有误，请检查后重试");
        }
        //非ads参数查询
        Map<String, Supplier<ReplaceStrategy>> mp = tempParamReplacer.getReplacementMap();
        boolean templateContainsMpKey = mp.keySet().stream().anyMatch(template::contains);
        if (templateContainsMpKey) {
            UserInfoResp user = userCenterClient.getUserByUserId(crowdDetail.getUserId());
            up.putAll(tempParamReplacer.replaceElements(template, user).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue()))));
            template = mp.keySet().stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        List<String> bizEventParamList = tempParamReplacer.getBizEventParamList();
        boolean bizEventParamFlag = bizEventParamList.stream().anyMatch(template::contains);
        if (bizEventParamFlag) {
            up.putAll(tempParamReplacer.bizEventReplaceElements(template, bizEvent).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue()))));
            template = bizEventParamList.stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        List<String> templateParam = getTemplateParam(template);
        if (!CollectionUtils.isEmpty(templateParam)) {
            Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.SMS.getCode(), template);
            tempParam = this.templateParamMatch(tempContent, crowdDetail, strategyId).get(crowdDetail.getUserId()).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue())));
        }
        tempParam.putAll(up);
        adsStrategyLabelService.filterLabelMinValue(tempParam, crowdDetail.getUserId(), strategyId);
        if (tempParam.keySet().size() < paramCheck.getRight()) {
            throw new StrategyException("模板参数不全，终止发送");
        }
        tempParam.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        log.info("push模板参数查询结果，用户ID:{}，标签结果:{}，tempParamReplacer replaceElements 自定义结果：{}", crowdDetail.getUserId(), JSON.toJSONString(tempParam), JsonUtil.toJson(up));
        return tempParam;
    }

    @Override
    public Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchPushTempParam(Long strategyId, String app, String templateId, List<CrowdDetailDo> crowdDetailList) {
        Map<Long, Map<String, Object>> tempParam = new HashMap<>();
        String template = this.getTemplate(StrategyMarketChannelEnum.PUSH, app, templateId);
        log.info("push template：{}", template);
        if (StringUtils.isEmpty(template)) {
            log.warn(" push 模板不存在,strategyId:{},templateId:{},app:{}", strategyId, templateId, app);
            Tracer.logEvent("NoPushTemplate", String.format("%s:%s:%s", strategyId, templateId, app));
        }
        Pair<Boolean, Integer> paramCheck = this.templateParamCheck(StrategyMarketChannelEnum.PUSH, app, template);
        if (null == paramCheck.getLeft()) {
            log.info("该push模板不存在参数");
            return Triple.of(Boolean.FALSE, 0, tempParam);
        }

        if (Boolean.FALSE.equals(paramCheck.getLeft())) {
            throw new StrategyException("参数配置有误，请检查后重试");
        }
        //非ads参数查询
        Map<String, Supplier<ReplaceStrategy>> mp = tempParamReplacer.getReplacementMap();
        boolean templateContainsMpKey = mp.keySet().stream().anyMatch(template::contains);
        if (templateContainsMpKey) {
            List<Long> uids = crowdDetailList.stream().map(CrowdDetailDo::getUserId).collect(Collectors.toList());
            List<List<Long>> batches = ListUtils.partition(uids, 200);
            for (List<Long> batch : batches) {
                List<UserInfoResp> respDtos = userCenterClient.queryBatchUserByUserNo(batch);
                for (UserInfoResp r : respDtos) {
                    try {
                        Map<String, Object> up = tempParamReplacer.replaceElements(template, r).entrySet().stream()
                                .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue())));
                        tempParam.put(r.getCreditUserId(), up);
                    }catch (Exception e){
                        log.error("user data is null,user:{}",JsonUtil.toJson(r),e);
                    }

                }
            }
            template = mp.keySet().stream().reduce(template, (result, key) -> result.replace(key, ""));
        }
        Map<Long, Map<String, Object>> newMap = new HashMap<>();
        List<String> templateParam = getTemplateParam(template);
        if (!CollectionUtils.isEmpty(templateParam)) {
            Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.SMS.getCode(), template);
            newMap = this.templateParamBatchMatch(tempContent, crowdDetailList, strategyId, app);
        }
        if (!CollectionUtils.isEmpty(newMap)) {
            tempParam = mergeMaps(tempParam, newMap);
        }
        log.info("push模板参数查询结果,标签结果:{},tempParamReplacer batchreplaceElements:{} ", JSON.toJSONString(tempParam),JsonUtil.toJson(tempParam));
        tempParam.forEach((appUserId, paramValueMap) -> {
            paramValueMap.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        });
        return Triple.of(Boolean.TRUE, paramCheck.getRight(), tempParam);
    }

    @Override
    public Triple<Boolean, Integer, Map<Long, Map<String, Object>>> getBatchAiTempParam(Long strategyId, String app, AiProntoChannelDto aiProntoChannelDto, List<CrowdDetailDo> batch) {
        List<String> aiProntoParams = aiProntoChannelDto.getAiProntoParams();
        Map<Long, Map<String, Object>> tempParam = new HashMap<>();
        if (CollectionUtils.isEmpty(aiProntoParams)) {
            log.info("该策略ai配置不存在参数");
            return Triple.of(Boolean.FALSE, 0, tempParam);
        }
        Map<Long, Map<String, Object>> newMap;
        Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.AI_PRONTO.getCode(), String.join(",", aiProntoParams));
        newMap = this.templateParamBatchMatch(tempContent, batch, strategyId, app);

        if (!CollectionUtils.isEmpty(newMap)) {
            tempParam = mergeMaps(tempParam, newMap);
        }
        log.info("ai模板参数查询结果,标签结果:{},tempParamReplacer batchreplaceElements:{} ", JSON.toJSONString(tempParam), JsonUtil.toJson(tempParam));
        tempParam.forEach((appUserId, paramValueMap) -> {
            paramValueMap.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        });
        return Triple.of(Boolean.TRUE, aiProntoParams.size(), tempParam);
    }

    @Override
    public Map<String, Object> getAiTempParam(CrowdDetailDo crowdDetail, Long strategyId, AiProntoChannelDto aiProntoChannelDto) {
        Map<String, Object> tempParam = new HashMap<>();
        Map<String, String> up = new HashMap<>();

        List<String> aiProntoParams = aiProntoChannelDto.getAiProntoParams();
        if (CollectionUtils.isEmpty(aiProntoParams)) {
            log.info("该策略ai配置不存在参数");
            return tempParam;
        }
        if (!CollectionUtils.isEmpty(aiProntoParams)) {
            Pair<Integer, String> tempContent = Pair.of(StrategyMarketChannelEnum.AI_PRONTO.getCode(), String.join(",",aiProntoParams));
            tempParam = this.templateParamMatch(tempContent, crowdDetail, strategyId).get(crowdDetail.getUserId()).entrySet().stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, entry -> String.valueOf(entry.getValue())));
        }
        tempParam.putAll(up);
        adsStrategyLabelService.filterLabelMinValue(tempParam, crowdDetail.getUserId(), strategyId);
        if (tempParam.keySet().size() < aiProntoParams.size()) {
            throw new StrategyException("模板参数不全，终止发送");
        }
        tempParam.replaceAll((k, v) -> Objects.isNull(v) ? "" : String.valueOf(v));
        log.info("ai参数查询结果，用户ID:{}，标签结果:{}，tempParamReplacer replaceElements 自定义结果：{}", crowdDetail.getUserId(), JSON.toJSONString(tempParam), JsonUtil.toJson(up));
        return tempParam;
    }
}