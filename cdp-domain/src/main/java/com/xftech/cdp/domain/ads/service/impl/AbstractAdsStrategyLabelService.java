package com.xftech.cdp.domain.ads.service.impl;

import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.alibaba.fastjson2.JSON;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.strategy.model.enums.InterfaceTypeEnum;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.vo.VariableLabelParamVO;
import com.xftech.cdp.infra.client.ads.model.req.label.AdbRealTimeReq;
import com.xftech.cdp.infra.client.ads.model.req.label.AdsLabelReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdbRealTimeResp;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.constant.Constants;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.thread.DispatchVariableExecutor;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;

import cn.hutool.core.date.DateUtil;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

/**
 * @<NAME_EMAIL>
 * @date 2023/6/9 14:40
 */
@Slf4j
public class AbstractAdsStrategyLabelService {

    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private AppConfigService appConfigService;
    @Autowired
    private ModelPlatformService modelPlatformService;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private UserCenterClient userCenterClient;

    public final String TRANSFORM="old_customer_fxk_transform_xyf01";
    public final String ISOLD="user_is_old_cust";

    public CrowdDetailDo convertCrowdOne(CrowdDetailDo crowdDetailDo, Integer userConvert) {
        List<CrowdDetailDo> list = Stream.of(crowdDetailDo).collect(Collectors.toList());
        List<CrowdDetailDo> res = convertCrowdList(list, userConvert);
        return CollectionUtils.isEmpty(res) ? crowdDetailDo : res.get(0);
    }

    public List<CrowdDetailDo> convertCrowdList(List<CrowdDetailDo> crowdDetailDos, Integer userConvert) {
        //fxk老客转xyf01下发
        if (!CollectionUtils.isEmpty(crowdDetailDos) && Objects.equals(1, userConvert) && appConfigService.getUserConvertSwitch()) {
            crowdDetailDos.forEach(c -> {
                try {
                    if (Objects.equals("fxk", c.getApp())) {
                        Object userConvertId = getUserConvertId(c.getUserId(), TRANSFORM);
                        if (isValidUserConvertId(userConvertId)) {
                            Long xyfId = Long.parseLong(userConvertId.toString());
                            Object isXyf = getUserConvertId(xyfId, ISOLD);
                            if (isValidUserConvertId(isXyf)) {
                                // 调用cis接口
                                UserInfoResp user = userCenterClient.getUserByUserId(xyfId);
                                if (Objects.nonNull(user)) {
                                    log.info("convertCrowdList, oldUserId={}, res={}", c.getUserId(), JsonUtil.toJson(user));
                                    c.setUserId(user.getCreditUserId());
                                    c.setApp(user.getApp());
                                    c.setInnerApp(user.getInnerApp());
                                    c.setMobile(user.getMobile());
                                    c.setRegisterTime(DateUtil.parseLocalDateTime(user.getCreatedTime()));
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("convertCrowdList error,req={}",JsonUtil.toJson(c), e);
                }
            });
        }
        return crowdDetailDos;
    }

    private boolean isValidUserConvertId(Object userConvertId) {
        return userConvertId != null && NumberUtils.isNumber(userConvertId.toString()) && Long.parseLong(userConvertId.toString()) > 0;
    }

    /**
     * 获取用户转换id
     *
     * @param userId
     * @param varCodes
     * @return
     */
    public Object getUserConvertId(Long userId, String varCodes) {
        try {
            String requestId = serialNumberUtil.batchNum();
            AdbRealTimeReq realTimeReq = buildAdbRealTimeRequest(requestId, userId, varCodes);

            AdbRealTimeResp adbRealTimeResp = modelPlatformService.getAdbRealTime(realTimeReq);
            if (adbRealTimeResp != null && adbRealTimeResp.isSuccess()) {
                String variablesResponse = adbRealTimeResp.getVariablesResponse();
                if (StringUtils.isNotBlank(variablesResponse)) {
                    Map<String, Object> ret = JsonUtil.parse(variablesResponse, Map.class);
                    if (ret != null) {
                        return ret.get(varCodes);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error in getUserConvertId", e);
        }

        return null;
    }

    private AdbRealTimeReq buildAdbRealTimeRequest(String requestId, Long userId, String varCodes) {
        AdbRealTimeReq realTimeReq = new AdbRealTimeReq();
        realTimeReq.setRequestId(requestId);
        realTimeReq.setRequestGroup(Constants.APP_NAME);
        realTimeReq.setVarCodes(Lists.newArrayList(varCodes));

        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("requestId", requestId);
        inputParams.put("app_user_id", userId);
        inputParams.put("timestamp", new Date().getTime());
        realTimeReq.setInputParams(inputParams);

        return realTimeReq;
    }

    /**
     * 批量调用变量中心
     * @param userInfoList
     * @return
     */
    public List<AdsLabelResp> queryBatchAdsWrapper(List<VariableLabelParamVO> userInfoList) {
        List<AdsLabelResp> futureResult = new ArrayList<>();
        List<Future> tasks = new ArrayList<>();
        log.info("testLabel-线程池开始批量调用变量中心, total:{}", userInfoList.size());
        userInfoList.forEach(item -> {
            Future<List<AdsLabelResp>> task = DispatchVariableExecutor.getPool().submit(() -> getAdsLabelValue(item));
            tasks.add(task);
        });
        tasks.forEach(t -> {
            try {
                List<AdsLabelResp> param = (List<AdsLabelResp>) t.get();
                if (!CollectionUtils.isEmpty(param)) {
                    futureResult.addAll(param);
                }
            } catch (Exception e) {
                log.error("dispatchVariableExecutor result error", e);
            }
        });
        Map<String, List<AdsLabelResp.Param>> listMap = new HashMap<>();
        for (AdsLabelResp adsLabelResp : futureResult) {
            if (CollectionUtils.isEmpty(adsLabelResp.getParams())) {
                if (adsLabelResp.getParams() == null) {
                    adsLabelResp.setParams(new ArrayList<>(0));
                }
            }
            if (listMap.containsKey(adsLabelResp.getLabel())) {
                listMap.get(adsLabelResp.getLabel()).addAll(adsLabelResp.getParams());
            } else {
                listMap.put(adsLabelResp.getLabel(), new ArrayList<>(adsLabelResp.getParams()));
            }
        }
        log.info("testLabel-线程池批量调用变量中心结果,results:{}", JsonUtil.toJson(listMap));
        if (CollectionUtils.isEmpty(listMap)) {
            return new ArrayList<>(0);
        }
        return listMap.entrySet().stream().map(x -> new AdsLabelResp(x.getKey(), x.getValue())).collect(Collectors.toList());
    }

    /**
     * 调用变量中心
     * @param item
     * @return
     */
    public List<AdsLabelResp> getAdsLabelValue(VariableLabelParamVO item) {
        try {
            AdsLabelResp.Param param = new AdsLabelResp.Param();
            param.setAppUserId(item.getAppUserId());
            param.setMobile(item.getMobile());

            String requestId = serialNumberUtil.batchNum();
            AdbRealTimeReq realTimeReq = new AdbRealTimeReq();
            realTimeReq.setVarCodes(Lists.newArrayList(item.getLabelName()));
            realTimeReq.setRequestId(requestId);
            realTimeReq.setRequestGroup(Constants.APP_NAME);
            Map<String, Object> inputParams = new HashMap<>();
            inputParams.put("app_user_id", item.getAppUserId());
            if (item.getStartTime() != null) {
                inputParams.put("startTime", item.getStartTime());
            }
            inputParams.put("timestamp", new Date().getTime());
            realTimeReq.setInputParams(inputParams);

            log.info("离线策略开始调用变量中心, userId={}, realTimeReq={}", item.getAppUserId(), JsonUtil.toJson(realTimeReq));
            AdbRealTimeResp adbRealTimeResp = modelPlatformService.getAdbRealTime(realTimeReq);
            List<AdsLabelResp> listBaseAdsResponse = new ArrayList<>();
            if (adbRealTimeResp != null && adbRealTimeResp.isSuccess()) {
                List<AdsLabelResp> variables = adbRealTimeResp.convertVariables(item.getLabelName(), item.getAppUserId(), item.getMobile());
                if (!CollectionUtils.isEmpty(variables)) {
                    variables.forEach(x->{
                        if (!CollectionUtils.isEmpty(x.getParams())) {
                            // 排除null值
                            x.setParams(x.getParams().stream().filter(v -> StringUtils.isNotEmpty(v.getResult()) && v.getResult() != null)
                                    .collect(Collectors.toList()));
                        }
                    });
                    listBaseAdsResponse.addAll(variables);
                }
            }
            return listBaseAdsResponse;
        } catch (Exception e) {
            log.error("Error in getAdsLabelValue", e);
        }
        return null;
    }


    public BaseAdsResponse<List<AdsLabelResp>> queryAdsWrapper(StrategyInstantLabelTypeEnum queryType, Long strategyId, Long appUserId, String mobile, List<AdsLabelReq> reqs) {
        BaseAdsResponse<List<AdsLabelResp>> response = new BaseAdsResponse<>();
        log.info("AbstractAdsStrategyLabelService 查询标签 userid={}, 策略id={}, 标签={}", appUserId, strategyId, JsonUtil.toJson(reqs));

        List<AdsLabelResp> listBaseAdsResponse = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reqs)) {
            String requestId = serialNumberUtil.batchNum();
            AdbRealTimeReq realTimeReq = new AdbRealTimeReq();
            realTimeReq.setRequestId(requestId);
            realTimeReq.setRequestGroup("xyf-cdp");
            Map<String, Object> inputParams = new HashMap<>();
            inputParams.put("requestId", requestId);
            inputParams.put("app_user_id", appUserId);
            inputParams.put("timestamp", new Date().getTime());

            List<String> varCodes = new ArrayList<>();
            for (AdsLabelReq item : reqs) {
                varCodes.add(item.getLabel());
                Tracer.logEvent(String.format("adbRealTime_%s", item.getLabel()), String.valueOf(strategyId));
                Tracer.logEvent(String.format("adbRealTime_type_%s", queryType.getType()), item.getLabel());
                if (!CollectionUtils.isEmpty(item.getParams())) {
                    for (Map map : item.getParams()) {
                        inputParams.putAll(map);
                    }
                }
            }
            realTimeReq.setVarCodes(Lists.newArrayList(varCodes));
            realTimeReq.setInputParams(inputParams);
            AdbRealTimeResp adbRealTimeResp = modelPlatformService.getAdbRealTime(realTimeReq, strategyId);
            if (adbRealTimeResp != null && adbRealTimeResp.isSuccess()) {
                List<AdsLabelResp> variables = adbRealTimeResp.convertVariables(varCodes, appUserId, mobile);
                if (!CollectionUtils.isEmpty(variables)) {
                    listBaseAdsResponse.addAll(variables);
                }
            } else {
                alarmOtherEx(InterfaceTypeEnum.REALTIME, queryType, strategyId, appUserId, null);
                throw new BizException(response.getCode(), String.format("ADB数仓响应异常，code：%s，msg：%s", response.getCode(), response.getMsg()));
            }
        }
        response.setCode(200);
        response.setMsg("success");
        if (CollectionUtils.isEmpty(listBaseAdsResponse)) {
            listBaseAdsResponse = null;
        }
        response.setPayload(listBaseAdsResponse);
        log.info("AbstractAdsStrategyLabelService 查询标签 userid={}, 策略id={}, 查询结果={}", appUserId, strategyId, JsonUtil.toJson(listBaseAdsResponse));
        return response;
    }


    /**
     *
     * @param queryType
     * @param appUserId
     * @param mobile
     * @param strategyIdList
     * @param reqs
     * @return strategyId到标签结果的映射Map
     */
    public BaseAdsResponse<List<AdsLabelResp>> queryAdsWrapperBatch(StrategyInstantLabelTypeEnum queryType,
                                                                    Long appUserId, String mobile,
                                                                    Collection<Long> strategyIdList,
                                                                    Collection<AdsLabelReq> reqs) {
        String strategyIdListLog = JsonUtil.toJson(strategyIdList);
        BaseAdsResponse<List<AdsLabelResp>> response = new BaseAdsResponse<>();
        log.info("AbstractAdsStrategyLabelService 批量查询标签 userid={}, 策略id={}, 所有标签={}", appUserId, strategyIdListLog, JsonUtil.toJson(reqs));

        List<AdsLabelResp> listBaseAdsResponse = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reqs)) {
            String requestId = serialNumberUtil.batchNum();
            AdbRealTimeReq realTimeReq = new AdbRealTimeReq();
            realTimeReq.setRequestId(requestId);
            realTimeReq.setRequestGroup("xyf-cdp");
            Map<String, Object> inputParams = new HashMap<>();
            inputParams.put("requestId", requestId);
            inputParams.put("app_user_id", appUserId);
            inputParams.put("timestamp", new Date().getTime());

            List<String> varCodes = new ArrayList<>();
            for (AdsLabelReq item : reqs) {
                varCodes.add(item.getLabel());
                Tracer.logEvent(String.format("adbRealTime_batch_%s", item.getLabel()), strategyIdListLog);
                Tracer.logEvent(String.format("adbRealTime_batch_type_%s", queryType.getType()), item.getLabel());
                if (!CollectionUtils.isEmpty(item.getParams())) {
                    for (Map map : item.getParams()) {
                        inputParams.putAll(map);
                    }
                }
            }
            realTimeReq.setVarCodes(Lists.newArrayList(varCodes));
            realTimeReq.setInputParams(inputParams);

            log.info("AbstractAdsStrategyLabelService 批量查询标签 userId={}, 策略id={}, 请求参数={}", appUserId, strategyIdListLog, JsonUtil.toJson(realTimeReq));
            AdbRealTimeResp adbRealTimeResp = modelPlatformService.getAdbRealTime(realTimeReq);
            if (adbRealTimeResp != null && adbRealTimeResp.isSuccess()) {
                List<AdsLabelResp> variables = adbRealTimeResp.convertVariables(varCodes, appUserId, mobile);
                if (!CollectionUtils.isEmpty(variables)) {
                    listBaseAdsResponse.addAll(variables);
                }
            } else {
                alarmOtherExBatch(InterfaceTypeEnum.REALTIME, queryType, strategyIdList, appUserId, null);
                throw new BizException(response.getCode(), String.format("批量查询标签, response=%s", JSON.toJSONString(response)));
            }
        }

        response.setCode(200);
        response.setMsg("success");
        if (CollectionUtils.isEmpty(listBaseAdsResponse)) {
            listBaseAdsResponse = null;
        }
        response.setPayload(listBaseAdsResponse);
        log.info("AbstractAdsStrategyLabelService 批量查询标签 userid={}, 策略id={}, 查询结果={}", appUserId, strategyIdListLog, JsonUtil.toJson(listBaseAdsResponse));
        return response;
    }

    /**
     * 超时告警
     */
    protected void alarmTimeOut(InterfaceTypeEnum strategyType, StrategyInstantLabelTypeEnum queryType, Long strategyId, Long appUserId, Integer userNum) {
        String content = "查询实时接口超时，可能造成处理堆积，请研发关注";
        if (strategyType == InterfaceTypeEnum.BATCH) {
            content = String.format(queryType == StrategyInstantLabelTypeEnum.LABEL ? "查询批量实时接口超时，影响%s个用户无法营销" : "获取短信实时参数批量接口超时，影响%s个用户无法营销", userNum);
        }
        alarm(strategyId, strategyType == InterfaceTypeEnum.REALTIME ? appUserId : null, content);
    }

    /**
     * 其它异常告警
     */
    protected void alarmOtherEx(InterfaceTypeEnum strategyType, StrategyInstantLabelTypeEnum queryType, Long strategyId, Long appUserId, Integer userNum) {
        String content = "查询实时接口异常，请研发关注";
        if (strategyType == InterfaceTypeEnum.BATCH) {
            content = String.format(queryType == StrategyInstantLabelTypeEnum.LABEL ? "查询批量实时接口异常，影响%s个用户无法营销" : "获取短信实时参数批量接口异常，影响%s个用户无法营销", userNum);
        }
        alarm(strategyId, strategyType == InterfaceTypeEnum.REALTIME ? appUserId : null, String.format(content, userNum));
    }

    protected void alarmOtherExBatch(InterfaceTypeEnum strategyType, StrategyInstantLabelTypeEnum queryType, Collection<Long> strategyIdList, Long appUserId, Integer userNum) {
        String content = "查询实时接口异常，请研发关注";
        if (strategyType == InterfaceTypeEnum.BATCH) {
            content = String.format(queryType == StrategyInstantLabelTypeEnum.LABEL ? "查询批量实时接口异常，影响%s个用户无法营销" : "获取短信实时参数批量接口异常，影响%s个用户无法营销", userNum);
        }
        alarmBatch(strategyIdList, strategyType == InterfaceTypeEnum.REALTIME ? appUserId : null, String.format(content, userNum));
    }

    private void alarm(Long strategyId, Long appUserId, String tips) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        DingTalkUtil.dingTalk(dingTalkConfig, strategyDo, appUserId, tips);
    }

    private void alarmBatch(Collection<Long> strategyIdList, Long appUserId, String tips) {
        List<StrategyDo> strategyDoList = new ArrayList<>(strategyIdList.size());
        for (Long strategyId : strategyIdList) {
            StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
            if (Objects.nonNull(strategyDo)) {
                strategyDoList.add(strategyDo);
            }
        }
        DingTalkUtil.dingTalkStrategyBatch(dingTalkConfig, strategyDoList, appUserId, tips);
    }

    /**
     * 程序异常告警
     */
    protected void alarmProgramEx(Long strategyId) {
        StrategyDo strategyDo = cacheStrategyService.selectById(strategyId);
        DingTalkUtil.dingTalk(dingTalkConfig.getAlarmUrl(), strategyDo);
    }


}