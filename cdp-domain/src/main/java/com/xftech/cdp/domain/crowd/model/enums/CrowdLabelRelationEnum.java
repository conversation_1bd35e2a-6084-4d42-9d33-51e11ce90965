package com.xftech.cdp.domain.crowd.model.enums;

import com.xftech.cdp.domain.crowd.exception.CrowdException;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/2/10
 */
@AllArgsConstructor
@Getter
public enum CrowdLabelRelationEnum {

    NONE(0, "无", " "),
    AND(1, "且", " and "),

    OR(2, "或", " or ");

    private final int code;

    private final String description;

    private final String value;


    public static CrowdLabelRelationEnum ofCode(int code) {
        for (CrowdLabelRelationEnum crowdLabelRelationEnum : values()) {
            if (crowdLabelRelationEnum.code == code) {
                return crowdLabelRelationEnum;
            }
        }
        throw new CrowdException(String.format("不存在该关联属性: %s", code));
    }
}
