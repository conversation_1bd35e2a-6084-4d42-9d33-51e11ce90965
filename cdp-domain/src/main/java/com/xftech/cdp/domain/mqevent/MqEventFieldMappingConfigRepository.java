package com.xftech.cdp.domain.mqevent;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.infra.repository.cdp.mqevent.MqEventFieldMappingConfigDo;

import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.Map;

@Repository
public class MqEventFieldMappingConfigRepository {

    public int insert(MqEventFieldMappingConfigDo record) {
        return DBUtil.insert("MqEventFieldMappingConfig.insert", record);
    }

    public int insertSelective(MqEventFieldMappingConfigDo record) {
        return DBUtil.insert("MqEventFieldMappingConfig.insertSelective", record);
    }

    public MqEventFieldMappingConfigDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("MqEventFieldMappingConfig.selectByPrimaryKey", id);
    }

    public MqEventFieldMappingConfigDo selectByTopicAndConsumer(String topic, String consumer, String tag){
        Map<String,Object> params = new HashMap<>();
        params.put("topic",topic);
        params.put("consumer",consumer);
        params.put("tag",tag);

        return DBUtil.selectOne("MqEventFieldMappingConfig.selectByTopicAndConsumer", params);
    }

    public boolean updateByPrimaryKey(MqEventFieldMappingConfigDo record) {
        return DBUtil.update("MqEventFieldMappingConfig.updateByPrimaryKey", record) > 0;
    }

    public boolean updateByPrimaryKeySelective(MqEventFieldMappingConfigDo record) {
        return DBUtil.update("MqEventFieldMappingConfig.updateByPrimaryKeySelective", record) > 0;
    }

    public boolean deleteByPrimaryKey(Long id) {
        return DBUtil.update("MqEventFieldMappingConfig.deleteByPrimaryKey", id) > 0;
    }

}