/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.DecideReq;
import com.xftech.cdp.api.dto.req.external.CallingSourceEnum;
import com.xftech.cdp.api.dto.resp.DecideResp;
import com.xftech.cdp.distribute.crowd.repository.CrowdInfoRepository;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;
import com.xftech.cdp.distribute.crowd.service.CrowdInfoService;
import com.xftech.cdp.domain.ads.service.AdsStrategyLabelService;
import com.xftech.cdp.domain.cache.CacheCrowdPackService;
import com.xftech.cdp.domain.cache.CacheStrategyGroupService;
import com.xftech.cdp.domain.cache.CacheStrategyMarketEventService;
import com.xftech.cdp.domain.cache.CacheStrategyService;
import com.xftech.cdp.domain.crowd.model.dispatch.CrowdContext;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.repository.CrowdPackRepository;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.model.enums.*;
import com.xftech.cdp.domain.strategy.model.enums.label.ValueTypeEnum;
import com.xftech.cdp.domain.strategy.repository.DecisionRecordRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketChannelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.*;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.UserCenterClient;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.client.usercenter.model.UserAbNumResp;
import com.xftech.cdp.infra.client.usercenter.model.UserInfoResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.exception.BizException;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;
import com.xftech.cdp.infra.rabbitmq.vo.HitResult;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.*;
import com.xftech.cdp.infra.thread.DecideExecExecutor;
import com.xftech.cdp.infra.utils.*;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import static com.xftech.cdp.infra.config.ApolloKeyConstants.INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK;

/**
 * <AUTHOR>
 * @version $ DecideServiceImpl, v 0.1 2024/6/24 15:25 benlin.wang Exp $
 */

@Slf4j
@Service
public class DecideServiceImpl implements DecideService {
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private CacheStrategyService cacheStrategyService;
    @Autowired
    private StrategyMarketSubEventService strategyMarketSubEventService;
    @Autowired
    private EventMetaDataService eventMetaDataService;
    @Autowired
    private DecisionRecordRepository decisionRecordRepository;
    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;
    @Autowired
    private AdsStrategyLabelService adsStrategyLabelService;
    @Autowired
    private StrategyMarketEventConditionService strategyMarketEventConditionService;
    @Autowired
    private CrowdPackRepository crowdPackRepository;
    @Autowired
    private CacheCrowdPackService cacheCrowdPackService;
    @Autowired
    private CrowdPackService crowdPackService;
    @Autowired
    private UserCenterClient userCenterClient;
    @Autowired
    private CisService cisService;
    @Autowired
    private CacheStrategyGroupService cacheStrategyGroupService;
    @Autowired
    private CacheStrategyMarketEventService cacheStrategyMarketEventService;
    @Autowired
    private StrategyMarketChannelRepository strategyMarketChannelRepository;
    @Autowired
    private StrategyGroupService strategyGroupService;
    @Autowired
    private SerialNumberUtil serialNumberUtil;
    @Autowired
    private DecideLogService decideLogService;
    @Autowired
    private RandomNumService randomNumService;
    @Autowired
    private CrowdInfoRepository crowdInfoRepository;
    @Autowired
    private CrowdInfoService crowdInfoService;

    private volatile Map<Long, StrategyDo> id2StrategyMap;
    private volatile Map<Long, StrategyMarketChannelDo> id2MarketChannelMap;

    @PostConstruct
    public void setupCacheRefresh() {

//        Executors.newSingleThreadScheduledExecutor(r -> {
//            Thread thread = new Thread(r, "refresh-decide-service-cache");
//            thread.setDaemon(true);
//            return thread;
//        }).scheduleAtFixedRate(this::refreshCache, 8L, 60L, TimeUnit.SECONDS);
    }

    private void refreshCache() {
        log.info("*******************************决策服务缓存数据开始加载*********************************************");
        id2StrategyMap = new HashMap<>();
        id2MarketChannelMap = new HashMap<>();

        try {
            List<StrategyDo> strategyDos = strategyRepository.selectAll();
            Map<Long, StrategyDo> strategyDoMap = new HashMap<>(strategyDos.size());
            for (StrategyDo strategyDo : strategyDos) {
                strategyDoMap.put(strategyDo.getId(), strategyDo);
            }
            this.id2StrategyMap = strategyDoMap;

            List<Long> strategyIds = new ArrayList<>(strategyDoMap.size());
            strategyDoMap.forEach((id, strategyDo) -> strategyIds.add(id));
            List<StrategyMarketChannelDo> strategyMarketChannelDoList =
                    strategyMarketChannelRepository.selectByStrategyIdList(strategyIds);
            Map<Long, StrategyMarketChannelDo> marketChannelMap = new HashMap<>(strategyMarketChannelDoList.size());
            for (StrategyMarketChannelDo marketChannelDo : strategyMarketChannelDoList) {
                marketChannelMap.put(marketChannelDo.getId(), marketChannelDo);
            }
            this.id2MarketChannelMap = marketChannelMap;
        } catch (Exception e) {
            log.error("[决策服务] 初始化缓存报错", e);
        }
    }

    @Override
    public List<DecideResp> decide(DecideReq decideReq) {
        List<Long> strategyIdList = decideReq.getStrategyIdList();

        CallingSourceEnum callingSourceEnum = decideReq.getCallingSource();
        Map<Long, StrategyDo> strategyMap = null;
        if (callingSourceEnum.equals(CallingSourceEnum.Overloan)) {
            strategyMap = new HashMap<>(strategyIdList.size());
            for (Long id : strategyIdList) {
                StrategyDo strategyDo = cacheStrategyService.selectById(id);
                if (Objects.isNull(strategyDo)) {
                    log.warn("[决策服务] [贷超] {} 没有对应id的策略", id);
                    continue;
                }
                if (strategyDo.getBusinessType().equals(decideReq.getBusinessType()) &&
                        strategyDo.getCallingSource().equals(decideReq.getCallingSource().getCode())) {
                    strategyMap.put(id, strategyDo);
                }
            }
        } else {
            List<StrategyDo> strategyDoList =
                    strategyRepository.selectByCallingSourceAndBusinessType(callingSourceEnum, decideReq.getBusinessType());
            strategyMap = new HashMap<>(strategyDoList.size());
            for (StrategyDo strategyDo : strategyDoList) {
                strategyMap.put(strategyDo.getId(), strategyDo);
            }
        }
        LogUtil.logDebug("DecideServiceImpl decide userId={} strategyMap={}", decideReq.getUserId(), JSONObject.toJSONString(strategyMap));

        if (CollectionUtils.isEmpty(strategyMap)) {
            throw new BizException("没有相关的候选策略");
        }


        List<DecideResp> respList = new ArrayList<>(strategyMap.size());
        List<DecideContext> decideContexts = new ArrayList<>(strategyMap.size());
        StrategyMarketChannelEnum channelEnum = getChannelByCallingSource(decideReq);

        CISUserInfo cisUserInfo = getCisUserInfo(decideReq);
//        cisUserInfo.setRegisterTime(LocalDateTime.now().minusHours(3));
        LogUtil.logDebug("DecideServiceImpl decide userId={} cisUserInfo={}", decideReq.getUserId(), JSONObject.toJSONString(cisUserInfo));

        List<Future<?>> taskList = new ArrayList<>(strategyMap.size());
        Map<Future<?>, DecideContext> contextMap = new HashMap<>(strategyMap.size());
        /*
         * 第一次以策略为维度的并行校验，主要包括：
         *  1. 策略自身的状态
         *  2. 校验营销事件
         *  3. 是否命中人群包
         */
        for (Map.Entry<Long, StrategyDo> entry : strategyMap.entrySet()) {
            DecideContext ctx = new DecideContext();
            ctx.setDecideReq(decideReq);
            decideContexts.add(ctx);

            Future<?> task = DecideExecExecutor.getPool().submit(() -> {
                StrategyDo strategyDo = entry.getValue();

                if (Objects.nonNull(strategyDo)) {
                    ctx.setUserId(decideReq.getUserId());
                    ctx.setCallingSourceEnum(callingSourceEnum);
                    ctx.setStrategyDo(strategyDo);
                    ctx.setChannelEnum(channelEnum);
                    ctx.setCisUserInfo(cisUserInfo);
                    ctx.setApp(decideReq.getApp());
                    ctx.setInnerApp(decideReq.getInnerApp());
                    //决策结果枚举。决策过程中只有失败时才会设置为其他值。
                    //如果到决策结束时，该值还是NONE，说明通过了所有检查。
                    ctx.setDecisionResult(DecisionResultEnum.NONE);

                    checkStrategyBeforeLabel(ctx, decideReq);
                } else {
                    ctx.setDecisionResult(DecisionResultEnum.EVENT_STRATEGY_NOT_EXIST);
                }
            });
            taskList.add(task);
            contextMap.put(task, ctx);
        }
        LogUtil.logDebug("DecideServiceImpl decide userId={} contextMap-1={}", decideReq.getUserId(), JSONObject.toJSONString(contextMap));

        //将通过上一阶段校验的context收集起来，准备下一阶段的校验
        List<DecideContext> nextContexts = new ArrayList<>();
        for (Future<?> task : taskList) {
            DecideContext ctx = contextMap.getOrDefault(task, null);
            try {
                task.get();
                boolean isHit = ctx.getDecisionResult().equals(DecisionResultEnum.NONE);
                if (isHit) {
                    nextContexts.add(ctx);
                }
            } catch (Exception e) {
                log.error("[决策接口] 异常, context: {} ", JsonUtil.toJson(ctx), e);
            }
        }


        //将多个策略所需的标签聚合去访问大数据接口。
        //降低对大数据接口的访问频次。
        if ( !nextContexts.isEmpty()) {
            fetchLabelsBatchByStrategy(nextContexts);
        }

        //再次并行处理后续的校验逻辑，包括：
        //  标签值是否match
        //  策略分组是否命中
        taskList.clear();
        contextMap.clear();
        for (DecideContext nextContext : nextContexts) {
            Future<?> task = DecideExecExecutor.getPool().submit(() -> {
                if (!checkLabelsByStrategy(nextContext)) {
                    return;
                }
                checkStrategyGroup(nextContext);
            });
            taskList.add(task);
            contextMap.put(task, nextContext);
        }
        LogUtil.logDebug("DecideServiceImpl decide userId={} contextMap-2={}", decideReq.getUserId(), JSONObject.toJSONString(contextMap));

        for (Future<?> task : taskList) {
            DecideContext ctx = contextMap.getOrDefault(task, null);
            try {
                task.get();
                boolean isHit = ctx.getDecisionResult().equals(DecisionResultEnum.NONE);

                DecideResp decideResp = new DecideResp();
                respList.add(decideResp);

                decideResp.setIsHit(isHit);
                decideResp.setStrategyId(ctx.getStrategyDo().getId());
                if (isHit) {
                    setResult(ctx, decideResp);
                }
            } catch (Exception e) {
                ctx.setDecisionResult(DecisionResultEnum.SERVER_INNER_ERROR);
                log.error("[决策接口] 异常, context: {} ", JsonUtil.toJson(ctx), e);
            }
        }

        //异步将日志落盘
        decideLogService.saveLogs(decideReq, decideContexts);

        return respList;
    }

    private static void setResult(DecideContext ctx, DecideResp resp) {
        StrategyGroupDo strategyGroup = ctx.getStrategyGroup();
        Map<String, Object> m = new HashMap<>();
        String extInfo = strategyGroup.getExtInfo();
        m = JsonUtil.parse(extInfo, m.getClass());
        CallingSourceEnum callingSourceEnum = ctx.getCallingSourceEnum();
        if (Objects.nonNull(m)) {
            if (callingSourceEnum.equals(CallingSourceEnum.ApiHold)) {
                if (m.containsKey("hold_duration")) {
                    try {
                        int duration = Integer.parseInt(m.get("hold_duration").toString());
                        Map<String, Object> result = new HashMap<>();
                        result.put("hold_duration", duration);
                        resp.setResult(result);
                    } catch (Exception e) {
                        log.error("[API开单] 解析duration出错, 策略id:{},分组id:{},ext_info:{}",
                                ctx.getStrategyDo().getId(),
                                strategyGroup.getId(),
                                m,
                                e);
                    }
                }
            }
        } else {
            if (callingSourceEnum.equals(CallingSourceEnum.ApiHold)) {
                log.error("[决策接口] 解析ext_info出错, 策略id:{},分组id:{},ext_info:{}",
                        ctx.getStrategyDo().getId(),
                        strategyGroup.getId(),
                        extInfo);
            }
        }
    }

    private CISUserInfo getCisUserInfo(DecideReq decideReq) {
        BaseCisResp<RegisterInfoByUserNo.RespDto> respDtoBaseCisResp = cisService.queryRegisterInfoByUserNo(decideReq.getUserId());
        CISUserInfo cisUserInfo = new CISUserInfo();
        if (respDtoBaseCisResp != null && respDtoBaseCisResp.getData() != null) {
            String registerTimeStr = respDtoBaseCisResp.getData().getRegisterTime();
            if (StringUtils.isNotEmpty(registerTimeStr)) {
                Date dt = DateUtil.convert(registerTimeStr);
                cisUserInfo.setRegisterTime(DateUtil.convert(dt));
            }
        }
        return cisUserInfo;
    }

    private static StrategyMarketChannelEnum getChannelByCallingSource(DecideReq decideReq) {
        StrategyMarketChannelEnum channelEnum;
        switch (decideReq.getCallingSource()) {
            case Overloan:
                channelEnum = StrategyMarketChannelEnum.LOAN_OVERLOAD;
                break;
            case ApiHold:
                channelEnum = StrategyMarketChannelEnum.API_HOLD;
                break;
            default:
                channelEnum = StrategyMarketChannelEnum.BLANK;
                break;
        }
        return channelEnum;
    }

    private void checkStrategyBeforeLabel(DecideContext ctx, DecideReq decideReq) {
        LocalDateTime now = LocalDateTime.now();
        if ( !checkStrategyValid(ctx, now))
            return;

        if ( !checkMarketEvent(ctx) )
            return;

        if ( !checkCrowdPacks(ctx, decideReq)) {
            setNotHitCrowdDecision(ctx);
            return;
        }
    }

    private void checkStrategyGroup(DecideContext ctx) {
        DecideReq decideReq = ctx.getDecideReq();
        StrategyDo strategyDo = ctx.getStrategyDo();
        List<StrategyGroupDo> strategyGroupDos = cacheStrategyGroupService.selectListByStrategyId(strategyDo.getId());
        StrategyAbTestEnum abTestEnum = StrategyAbTestEnum.getInstance(strategyDo.getAbTest());
        if (abTestEnum.equals(StrategyAbTestEnum.NO)) {
            StrategyGroupDo groupA = strategyGroupDos.get(0);
            ctx.setStrategyGroup(groupA);
            if ( !groupA.getIsExecutable().equals(1)) {
                setNotExecutableDecision(ctx);
            }
        } else {
            StrategyGroupDo abGroup = findABGroup(strategyDo, decideReq.getUserId(), strategyGroupDos);
            if (abGroup != null) {
                ctx.setStrategyGroup(abGroup);
                if ( !abGroup.getIsExecutable().equals(1)) {
                    setNotExecutableDecision(ctx);
                }
            } else {
                setNotHitABGroupDecision(ctx);
            }
        }
//        System.out.println("checkStrategyGroup_end");
//        System.out.println(JsonUtil.toJson(ctx));
    }

    private boolean checkMarketEvent(DecideContext ctx) {
        List<StrategyMarketEventDo> smeList = cacheStrategyMarketEventService.getByStrategyId(ctx.getStrategyDo().getId());
        for (StrategyMarketEventDo marketEvent : smeList) {
            Map<Integer, List<StrategyMarketSubEventDo>> marketSubEventMap =
                    strategyMarketSubEventService.getByEventId(marketEvent.getId());
            List<StrategyMarketSubEventDo> independentSubEventList = marketSubEventMap.get(1);
            if ( CollectionUtils.isEmpty(independentSubEventList)) {
                continue;
            }

            String expression = independentSubEventList.stream()
                    .map(StrategyMarketSubEventDo::getExpression)
                    .collect(Collectors.joining(" && "));
            Map<String, Object> paramMap = new HashMap<>();
            independentSubEventList.stream()
                    .map(StrategyMarketSubEventDo::getEventName)
                    .forEach(eventName -> {
                        EventMetaDataDo eventMetaData = eventMetaDataService.selectByEventName(eventName);
                        ValueTypeEnum valueTypeEnum = ValueTypeEnum.getInstance(eventMetaData.getLabelValueType());
                        String propertyName = eventName;
                        if (propertyName.equals("apiHoldInnerApp")) {
                            propertyName =  "innerApp";
                        }
                        Object fieldValue = ReflectGetFieldUtil.getFieldValue(ctx, propertyName);
                        Object value = valueTypeEnum.normalizeValue(fieldValue);
                        paramMap.put(eventName, value);
                    });
            Boolean result = AviatorUtil.compute(expression, paramMap);
            if ( !result) {
                ctx.setDecisionResult(DecisionResultEnum.MARKET_SUB_FAIL);
                return false;
            }
        }
        return true;
    }

    /**
     * 判断策略是否还在有效期，以及状态是否为active，都是的情况下，返回true；否则，返回false
     * @param ctx
     * @param now
     * @return
     */
    private static boolean checkStrategyValid(DecideContext ctx, LocalDateTime now) {
        StrategyDo strategyDo = ctx.getStrategyDo();

        if (strategyDo.getValidityBegin().isAfter(now)) {
            switch (ctx.getCallingSourceEnum()) {
                case Overloan:
                    ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_STRATEGY_BEFORE_EXECUTE);
                    break;
                case ApiHold:
                    ctx.setDecisionResult(DecisionResultEnum.API_HOLD_STRATEGY_AFTER_EXECUTE);
                    break;
            }
            return false;
        }
        if (strategyDo.getValidityEnd().isBefore(now)) {
            switch (ctx.getCallingSourceEnum()) {
                case Overloan:
                    ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_STRATEGY_BEFORE_EXECUTE);
                    break;
                case ApiHold:
                    ctx.setDecisionResult(DecisionResultEnum.API_HOLD_STRATEGY_AFTER_EXECUTE);
                    break;
            }
            return false;
        }
        if ( !StrategyStatusEnum.getActiveCodes().contains(strategyDo.getStatus())) {
            switch (ctx.getCallingSourceEnum()) {
                case Overloan:
                    ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_STRATEGY_NOT_ACTIVE);
                    break;
                case ApiHold:
                    ctx.setDecisionResult(DecisionResultEnum.API_HOLD_STRATEGY_NOT_ACTIVE);
                    break;
            }
            return false;
        }
        return true;
    }

    private static void setNotHitABGroupDecision(DecideContext ctx) {
        switch (ctx.getCallingSourceEnum()) {
            case Overloan:
                ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_NOT_HIT_AB_GROUP);
                break;
            case ApiHold:
                ctx.setDecisionResult(DecisionResultEnum.API_HOLD_NOT_HIT_AB_GROUP);
                break;
        }
    }

    private static void setNotExecutableDecision(DecideContext ctx) {
        switch (ctx.getCallingSourceEnum()) {
            case Overloan:
                ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_GROUP_NOT_EXECUTE);
                break;
            case ApiHold:
                ctx.setDecisionResult(DecisionResultEnum.API_HOLD_GROUP_NOT_EXECUTE);
                break;
        }
    }

    private static void setNotHitCrowdDecision(DecideContext ctx) {
        ctx.setDecisionResult(DecisionResultEnum.INSTANT_CROWD_FILTER_FAIL);
    }

    private static void setNotHitLabelsDecision(DecideContext ctx) {
        switch (ctx.getCallingSourceEnum()) {
            case Overloan:
                ctx.setDecisionResult(DecisionResultEnum.LOAN_OVERLOAD_NOT_HIT_LABELS);
                break;
            case ApiHold:
                ctx.setDecisionResult(DecisionResultEnum.API_HOLD_NOT_HIT_LABELS);
                break;
        }
    }

    private StrategyGroupDo findABGroup(StrategyDo strategyDo, Long userId, List<StrategyGroupDo> strategyGroupDoList) {
        String abNum = getAbNum(strategyDo, userId);

        CrowdDetailDo crowdDetail = new CrowdDetailDo();
        crowdDetail.setUserId(userId);
        crowdDetail.setAbNum(abNum);
        crowdDetail.setAppUserIdLast2((int) (userId % 100));

        return strategyGroupDoList.stream()
                .filter(strategyGroupDo -> {
                    BiPredicate<String, Integer> matchFunc =
                            strategyGroupDo.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType()));
                    List<CrowdDetailDo> matchList =
                            strategyGroupService.matchGroupRule(strategyDo.getBizKey(), matchFunc, Collections.singletonList(crowdDetail));
                    return !matchList.isEmpty();
                }).findFirst().orElse(null);
    }

    private String getAbNum(StrategyDo strategyDo, Long userId) {
        StrategyGroupTypeEnum abType = StrategyGroupTypeEnum.getInstance(strategyDo.getAbType());
        String abNum = "";
        if (abType == StrategyGroupTypeEnum.NEW_RANDOM) {   //随机数组件
            abNum = getRandomNumber(strategyDo.getBizKey(), userId);
        } else { //原随机数
            UserInfoResp userInfo = userCenterClient.getUserByUserId(userId);
            UserAbNumResp userAbNumResp = userCenterClient.getAbNum(Collections.singletonList(userInfo.getMobile()));
            if (!userAbNumResp.getAbNums().isEmpty()) {
                abNum = userAbNumResp.getAbNums().get(0).getAbNum();
            }
        }
        return abNum;
    }

    private String getRandomNumber(String bizKey, Long userId) {
        try {
            AbBO abBO = AbClient.ab(bizKey, String.valueOf(userId), true);
            String randomNumber = String.valueOf(abBO.getRandomNum());
            log.info("获取随机数成功,方法名:ab,场景值:{},用户ID:{},随机数:{}", bizKey, userId, randomNumber);
            return randomNumber;
        } catch (IllegalArgumentException e) {
            log.error("获取随机数异常,参数异常,场景值:{}", bizKey, e);
            return "";
        } catch (Exception e) {
            log.error("获取随机数异常,场景值:{},用户ID:{}", bizKey, userId, e);
            return "";
        }
    }

    private boolean checkLabels(DecideContext ctx, DecideReq decideReq) {
        StrategyDo strategyDo = ctx.getStrategyDo();

        Map<String, List<StrategyMarketEventConditionDo>> labelNameToList =
                strategyMarketEventConditionService.getStringToListByStrategyId(strategyDo.getId());
        if (labelNameToList.isEmpty()) {
            log.warn("[接口调用][{}] 不存在需要查询的标签。策略ID：{}", ctx.getCallingSourceEnum().getLabel(), strategyDo.getId());
            return true;
        }

        Long userId = decideReq.getUserId();
        String mobile = decideReq.getMobile();
        String app = decideReq.getApp();
        Map<Long, Map<String, Object>> paramMaps = queryLabels(ctx, userId, mobile, app, strategyDo.getId(), labelNameToList);
        if (CollectionUtils.isEmpty(paramMaps) || !paramMaps.containsKey(userId)) {
            log.warn("[接口调用][{}] queryLabels失败, userId: {}, strategyId: {}, labelNameToList: {}",
                    ctx.getCallingSourceEnum().getLabel(),
                    userId, strategyDo.getId(), JsonUtil.toJson(labelNameToList));
            throw new BizException("queryLabels查询失败");
        }

        Map<String, Object> paramMap = paramMaps.get(userId);

        Map<Integer, List<StrategyMarketEventConditionDo>> optionalMap = labelNameToList.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(item -> item.getOptional() == 1 ? 1 : 2));
        for (Map.Entry<Integer, List<StrategyMarketEventConditionDo>> entry : optionalMap.entrySet()) {
            Integer optional = entry.getKey();
            List<StrategyMarketEventConditionDo> smecList = entry.getValue();
            if ( !CollectionUtils.isEmpty(smecList)) {
                String expression = smecList.stream()
                        .map(StrategyMarketEventConditionDo::getExpression)
                        .collect(Collectors.joining(" && "));
                Boolean result = AviatorUtil.compute(expression, paramMap);
                if ( !result) {
                    setLabelFilterFail(ctx, optional, expression, paramMap);
                    return false;
                }
            }
        }

        return true;
    }


    private void fetchLabelsBatchByStrategy(List<DecideContext> ctxList) {
        Map<Long, Set<StrategyMarketEventConditionDo>> strategyId2MarketEventCondMap = new HashMap<>(ctxList.size());
        Map<String, List<StrategyMarketEventConditionDo>> allLabelNameMap = new HashMap<>();
        List<Long> strategyIdList = new ArrayList<>(ctxList.size());
        Map<Long, Collection<String>> strategyId2LabelListMap = new HashMap<>(ctxList.size());
        for (DecideContext decideCtx : ctxList) {
            StrategyDo strategyDo = decideCtx.getStrategyDo();
            strategyIdList.add(strategyDo.getId());
            Map<String, List<StrategyMarketEventConditionDo>> labelNameMap =
                    strategyMarketEventConditionService.getStringToListByStrategyId(strategyDo.getId());
            if (labelNameMap.isEmpty()) {
                log.warn("[接口调用][{}] (批量查询)不存在需要查询的标签。策略ID：{}", decideCtx.getCallingSourceEnum().getLabel(), strategyDo.getId());
                continue;
            }
            Set<StrategyMarketEventConditionDo> labelSet = labelNameMap.values().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());
            strategyId2MarketEventCondMap.put(strategyDo.getId(), labelSet);
            strategyId2LabelListMap.put(strategyDo.getId(), labelSet.stream().map(StrategyMarketEventConditionDo::getLabelName).collect(Collectors.toList()));
            allLabelNameMap.putAll(labelNameMap);
        }

        DecideContext ctx = ctxList.get(0);
        if (allLabelNameMap.isEmpty()) {
            log.warn("[接口调用][{}] (批量查询)不存在需要查询的标签。策略ID：{}", ctx.getCallingSourceEnum().getLabel(),
                    JsonUtil.toJson(ctx.getDecideReq()));
            return;
        }

        // 截止本次营销前
        List<StrategyMarketEventConditionDo> list = allLabelNameMap.values().stream().flatMap(List::stream).filter(item -> Objects.nonNull(item.getTimeType())).collect(Collectors.toList());
        String startTime = getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0));

        Long userId = ctx.getUserId();
        String mobile = ctx.getDecideReq().getMobile();
        String app = ctx.getApp();

        Map<Long, Map<Long, Map<String, Object>>> paramMaps = queryLabelsBatch(ctx, userId, mobile, app, startTime, strategyId2LabelListMap);
        if (CollectionUtils.isEmpty(paramMaps) || !paramMaps.containsKey(userId)) {
            log.warn("[接口调用][{}] (批量查询)queryLabels失败, userId: {}, strategyId: {}, allLabelNameMap: {}",
                    ctx.getCallingSourceEnum().getLabel(),
                    userId, strategyIdList, JsonUtil.toJson(allLabelNameMap));
            throw new BizException("queryLabels查询失败");
        }

        Map<Long, Map<String, Object>> paramMap = paramMaps.get(userId);

        for (DecideContext decideCtx : ctxList) {
            StrategyDo strategyDo = decideCtx.getStrategyDo();
            Long strategyId = strategyDo.getId();
            if ( !strategyId2MarketEventCondMap.containsKey(strategyId) ||
                    !paramMap.containsKey(strategyId)) {
                continue;
            }
            decideCtx.setLabelValueMap(paramMap.get(strategyId));
            Set<StrategyMarketEventConditionDo> marketEventConditionSet = strategyId2MarketEventCondMap.get(strategyId);
            decideCtx.setMarketEventConditionDoSet(marketEventConditionSet);
        }
    }



    private boolean checkLabelsByStrategy(DecideContext ctx) {
        Map<String, Object> paramMap = ctx.getLabelValueMap();
        Set<StrategyMarketEventConditionDo> marketEventConditionSet = ctx.getMarketEventConditionDoSet();
        if (Objects.isNull(marketEventConditionSet)) {
            return true;
        }
        Map<Integer, List<StrategyMarketEventConditionDo>> optionalMap = marketEventConditionSet.stream()
                .collect(Collectors.groupingBy(item -> item.getOptional() == 1 ? 1 : 2));
        for (Map.Entry<Integer, List<StrategyMarketEventConditionDo>> entry : optionalMap.entrySet()) {
            Integer optional = entry.getKey();
            List<StrategyMarketEventConditionDo> smecList = entry.getValue();
            if ( !CollectionUtils.isEmpty(smecList)) {
                String expression = smecList.stream()
                        .map(StrategyMarketEventConditionDo::getExpression)
                        .collect(Collectors.joining(" && "));
                Boolean result = AviatorUtil.compute(expression, paramMap);
                if ( !result) {
                    setLabelFilterFail(ctx, optional, expression, paramMap);
                    return false;
                }
            }
        }

        return true;
    }


    private Map<Long, Map<Long, Map<String, Object>>> queryLabelsBatch(DecideContext ctx, Long appUserId, String mobile, String app,
                                                                       String startTime,
                                                                       Map<Long, Collection<String>> strategyId2LabelListMap) {
        if (CollectionUtils.isEmpty(strategyId2LabelListMap))
            return Collections.emptyMap();

//        // 截止本次营销前
//        List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream()
//                .flatMap(List::stream)
//                .filter(item -> Objects.nonNull(item.getTimeType()))
//                .collect(Collectors.toList());
//        String startTime = getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0));

        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setApp(app);
        bizEventVO.setStartTime(startTime);
        bizEventVO.setAppUserId(appUserId);
        bizEventVO.setAppUserIdLast2((int) (appUserId % 100));
        bizEventVO.setMobile(mobile);
        bizEventVO.setMessageId(serialNumberUtil.batchNum());
        if (Objects.nonNull(ctx.getCisUserInfo())) {
            bizEventVO.setRegisterTime(ctx.getCisUserInfo().getRegisterTime());
        }

        return adsStrategyLabelService.queryLabelBatch(bizEventVO, StrategyInstantLabelTypeEnum.LABEL, strategyId2LabelListMap);
    }

    private static void setLabelFilterFail(DecideContext ctx, Integer optional, String expression, Map<String, Object> paramMap) {
        ctx.setUserParam(JsonUtil.toJson(paramMap));
        ctx.setStrategyLabelExpression(expression);
        if (optional.equals(1)) {
            ctx.setDecisionResult(DecisionResultEnum.INSTANT_LABEL_FILTER_FAIL);
        } else {
            ctx.setDecisionResult(DecisionResultEnum.EXCLUDE_LABEL_FILTER_FAIL);
        }
    }


    private boolean checkCrowdPacks(DecideContext ctx, DecideReq decideReq) {
        StrategyDo strategyDo = ctx.getStrategyDo();
        MarketCrowdTypeEnum crowdTypeEnum = MarketCrowdTypeEnum.getInstance(strategyDo.getMarketCrowdType());
        switch (crowdTypeEnum) {
            case T0_REGISTER:
                Map<Integer, List<StrategyMarketSubEventDo>> subEventMap = strategyMarketSubEventService.getByStrategyId(strategyDo.getId());
                List<StrategyMarketSubEventDo> crowdSubEventList = subEventMap.get(2);
                if (CollectionUtils.isEmpty(crowdSubEventList)) {
                    return false;
                }
                String expression = crowdSubEventList.stream()
                        .map(StrategyMarketSubEventDo::getExpression)
                        .collect(Collectors.joining(" && "));


                Map<String, Object> param = new HashMap<>();
                crowdSubEventList.stream()
                        .map(StrategyMarketSubEventDo::getEventName)
                        .forEach(eventName -> {
                            EventMetaDataDo eventMetaDataDo = eventMetaDataService.selectByEventName(eventName);
                            ValueTypeEnum valueTypeEnum = ValueTypeEnum.getInstance(eventMetaDataDo.getLabelValueType());
                            Object fieldValue = ReflectGetFieldUtil.getFieldValue(ctx.getCisUserInfo(), eventName);
                            param.put(eventName, valueTypeEnum.normalizeValue(fieldValue));
                        });
                Boolean result = AviatorUtil.compute(expression, param);
                if (Boolean.TRUE.equals(result)) {
                    return true;
                }
                break;

            case CROWD_PACK:

                Map<Long, CrowdContext> crowdContent = getCrowdContent(strategyDo.getCrowdPackId());
                List<Long> crowdIds = Arrays.stream(strategyDo.getCrowdPackId().split(";"))
                        .map(Convert::toLong)
                        .collect(Collectors.toList());
                Long userId = ctx.getUserId();
                List<CrowdPackDo> crowdPackDos = crowdContent.values().stream().map(CrowdContext::getCrowdPack).collect(Collectors.toList());
                if ( !crowdPackService.verifyCrowdPackByNewRandom(userId, crowdPackDos)) {
                    ctx.setDecisionResult(DecisionResultEnum.CROWD_NEW_RANDOM_FAIL);
                    LogUtil.logDebug("DecideServiceImpl checkCrowdPacks userId={} CROWD_NEW_RANDOM_FAIL");
                    return false;
                }

                // 是否通过洞察平台接口判断
                if (crowdInfoService.isCheckByInsightPlatform(crowdIds)) {
                    Pair<Boolean, CrowdDetailDo> checkResult = crowdInfoService.checkByInsightPlatform(crowdIds, userId);
                    if (checkResult.getLeft() && checkResult.getRight() != null) {
                        CrowdDetailDo crowdDetailDo = checkResult.getRight();
                        ctx.setHitCrowdPack(crowdContent.get(crowdDetailDo.getCrowdId()).getCrowdPack());
                    }
                    return checkResult.getLeft();
                }

                List<Triple<Long, Long, List<String>>> execLogIdAndTablePairList =
                        crowdPackService.getExecLogIdAndTablePairList(crowdIds, crowdContent);
                if (CollectionUtils.isEmpty(execLogIdAndTablePairList)) {
                    LogUtil.logDebug("DecideServiceImpl checkCrowdPacks userId={} execLogIdAndTablePairList empty");
                    return false;
                }

                for (Triple<Long, Long, List<String>> triple : execLogIdAndTablePairList) {
                    for (String tableName : triple.getRight()) {
                        Optional<CrowdDetailDo> detailOptional =
                                crowdPackService.hasUserRecord(triple.getLeft(), decideReq.getUserId(), triple.getMiddle(), tableName);
                        if (detailOptional.isPresent()) {
                            Long crowdId = detailOptional.get().getCrowdId();
                            if (crowdContent.containsKey(crowdId)) {
                                ctx.setHitCrowdPack(crowdContent.get(crowdId).getCrowdPack());
                            }
                            return true;
                        }
                    }
                }

                break;
            case NO_LIMIT:
                return true;
        }
        LogUtil.logDebug("DecideServiceImpl checkCrowdPacks userId={} false");
        return false;
    }

    private Map<Long, CrowdContext> getCrowdContent(String crowdIds) {
        List<Long> crowdPackIds = Arrays.stream(crowdIds.split(";")).map(Convert::toLong).collect(Collectors.toList());
        // 查询人群包上传类型
        if (!CollectionUtils.isEmpty(crowdPackIds)) {
            Map<Long, CrowdContext> res = new HashMap<>(crowdPackIds.size());
            try {
                for (Long cid : crowdPackIds) {
                    CrowdPackDo crowdPack = cacheCrowdPackService.selectById(cid);
                    if (Objects.nonNull(crowdPack)) {
                        CrowdContext c = new CrowdContext();
                        c.setCrowdPack(crowdPack);
                        res.put(cid, c);
                    }

                    // 新增洞察平台人群包
                    if (crowdPack == null && ApolloUtil.isInTheWhitelist(INSIGHT_CROWD_PACK_WHITELIST_CROWD_PACK, cid)) {
                        CrowdInfoDo crowdInfoDo = crowdInfoRepository.selectByCrowdId(cid);
                        if (crowdInfoDo != null) {
                            CrowdContext c = new CrowdContext();
                            c.setCrowdPack(convertCrowInfoDoToCrowdPackDo(crowdInfoDo));
                            res.putIfAbsent(cid, c);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("getCrowdContent error", e);
            }
            return res;
        }
        return new HashMap<>();
    }

    private CrowdPackDo convertCrowInfoDoToCrowdPackDo(CrowdInfoDo crowdInfoDo) {
        CrowdPackDo crowdPackDo = new CrowdPackDo();
        crowdPackDo.setId(crowdInfoDo.getCrowdId());
        crowdPackDo.setFilterMethod(CrowdFilterMethodEnum.LABEL.getCode());
        return crowdPackDo;
    }

    private Map<Long, Map<String, Object>> queryLabels(DecideContext ctx, Long appUserId, String mobile, String app,
                                                       Long strategyId,
                                                       Map<String, List<StrategyMarketEventConditionDo>> labelNameToList) {
        if (CollectionUtils.isEmpty(labelNameToList))
            return Collections.emptyMap();

        // 截止本次营销前
        List<StrategyMarketEventConditionDo> list = labelNameToList.values().stream()
                .flatMap(List::stream)
                .filter(item -> Objects.nonNull(item.getTimeType()))
                .collect(Collectors.toList());
        String startTime = getStartTime(CollectionUtils.isEmpty(list) ? null : list.get(0));

        BizEventVO bizEventVO = new BizEventVO();
        bizEventVO.setStrategyId(strategyId);
        bizEventVO.setApp(app);
        bizEventVO.setStartTime(startTime);
        bizEventVO.setAppUserId(appUserId);
        bizEventVO.setAppUserIdLast2((int) (appUserId % 100));
        bizEventVO.setMobile(mobile);
        bizEventVO.setMessageId(serialNumberUtil.batchNum());
        if (Objects.nonNull(ctx.getCisUserInfo())) {
            bizEventVO.setRegisterTime(ctx.getCisUserInfo().getRegisterTime());
        }

        return adsStrategyLabelService.query(bizEventVO, labelNameToList.keySet(), StrategyInstantLabelTypeEnum.LABEL);
    }

    private String getStartTime(StrategyMarketEventConditionDo eventCondition) {
        LocalDateTime startTime = LocalDateTime.now();
        if (Objects.nonNull(eventCondition)) {
            switch (Optional.ofNullable(eventCondition.getTimeType()).orElse(3)) {
                case 1:
                    startTime = startTime.plusMinutes(-eventCondition.getTimeValue());
                    break;
                case 2:
                    startTime = startTime.plusHours(-eventCondition.getTimeValue());
                    break;
                default:
                    startTime = LocalDate.now().atStartOfDay();
            }
        }
        return LocalDateTimeUtil.format(startTime, TimeFormat.DATE_TIME);
    }
}