/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.domain.stat.entity.StatStrategyEngineFlowDataEntity;
import com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ StatRealtimeStrategyEngineFlowDataRepository, v 0.1 2023/11/28 20:58 lingang.han Exp $
 */

@Component
public class StatStrategyEngineFlowDataRepository {

    public ReportDailyStrategyDo selectById(Long id) {
        return DBUtil.selectOne("statStrategyFlowData.selectByPrimaryKey", id);
    }

    public boolean insert(StatStrategyEngineFlowDataEntity entity) {
        return DBUtil.insert("statStrategyFlowData.insertSelective", entity) > 0;
    }

    public boolean updateById(StatStrategyEngineFlowDataEntity entity) {
        return DBUtil.update("statStrategyFlowData.updateByPrimaryKeySelective", entity) > 0;
    }

    /**
     * 更新实时策略流程数据
     *
     * @param entity
     */
    public void updateByDateAndStrategyId(StatStrategyEngineFlowDataEntity entity) {
        DBUtil.update("statStrategyFlowData.updateByDateAndStrategyId", entity);
    }

    /**
     * 是否存在实时策略流程数据
     *
     * @param entity
     */
    public Boolean existStrategyFlowData(StatStrategyEngineFlowDataEntity entity) {
        Integer num = DBUtil.selectOne("statStrategyFlowData.existStrategyFlowData", entity);
        return num > 0;
    }

    /**
     * 按照策略id分页查询
     * @param strategyId 策略id
     * @param pageNum 当前页
     * @param pageSize 每页数
     * @return page
     */
    public Page<StatStrategyEngineFlowDataEntity> selectPageByStrategyId(Long strategyId, int pageNum, int pageSize) {
        Map<String,Object> param = new HashMap<>();
        param.put("strategyId",strategyId);
        return DBUtil.selectPage("statStrategyFlowData.listByStrategyIdPage", param, pageNum, pageSize);
    }
}