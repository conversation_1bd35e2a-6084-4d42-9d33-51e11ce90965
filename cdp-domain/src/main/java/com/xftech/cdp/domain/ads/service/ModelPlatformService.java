package com.xftech.cdp.domain.ads.service;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.xftech.cdp.feign.EnginePredictionClient;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.feign.service.DataFeatureService;
import com.xftech.cdp.infra.client.ads.model.req.ModelPredictionReq;
import com.xftech.cdp.infra.client.ads.model.req.label.AdbRealTimeReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdbRealTimeResp;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.UserService;
import com.xftech.cdp.infra.client.usercenter.model.AcctInfoModel;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.kafka.producer.T0EventCountProducer;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.proxy.Tracer;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xftech.cdp.infra.utils.MapUtils;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import static com.xftech.cdp.infra.client.dingtalk.config.TechDingTalkConfig.*;

@Slf4j
@Service
@NoArgsConstructor
public class ModelPlatformService {

    private static final String Authorization = "Authorization";

    private static final String PARAM_SUPPLEMENT_MODELS = "xf.model.platform.prediction.param.supplement.models";

    private static final String PARAM_ACCOUNT_SUPPLEMENT_MODELS = "xf.model.platform.prediction.param.account.supplement.models";

    @Resource
    private EnginePredictionClient predictionClient;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private AppConfigService appConfigService;
    @Resource
    private CisService cisService;
    @Resource
    private UserService userService;

    @ApolloJsonValue("${model.query.gary:[]}")
    private Set<String> modelQueryGary;

    @Resource
    private DataFeatureService dataFeatureService;

    @Resource
    private T0EventCountProducer t0EventCountProducer;

    public ModelPlatformService(RestTemplate restTemplate, AppConfigService appConfigService) {
        new ModelPlatformService(restTemplate, appConfigService);
    }

    public JSONObject prediction(ModelPredictionReq param) {
        JSONObject resp;

        if (modelQueryGary.contains("all") || modelQueryGary.contains(param.getModel_name())) {
            t0EventCountProducer.asyncSend(param); // 请求业务引擎埋点
            return (JSONObject) JSON.toJSON(predictionClient.modelPrediction(param));
        } else {

            final String predictionUrl = appConfigService.getXfModelPlatformPredictionUrl();
            final String authorization = appConfigService.getXfModelPlatformPredictionToken();

            HttpHeaders header = new HttpHeaders();
            header.setContentType(MediaType.APPLICATION_JSON);
            header.set(Authorization, authorization);

            // 补充参数
            if (!wrapperModelPredictionReq(param)) {
                String modelName = param.getModel_name();

                JSONObject output_data_js = new JSONObject();
                output_data_js.put(modelName, new JSONObject());

                JSONObject data_js = new JSONObject();
                data_js.put("model_name", modelName);
                data_js.put("output_data", output_data_js);

                resp = new JSONObject();
                resp.put("data", data_js);
                log.warn("调用决策引擎-补充额外参数失败 引擎code={}, resp={}", param.getModel_name(), JSONObject.toJSONString(resp));
                return resp;
            }
            // 补充参数
            addAccountNo(param);

            HttpEntity<Object> httpEntity = new HttpEntity<>(param, header);
            try {
                t0EventCountProducer.asyncSend(param); // 请求业务引擎埋点
                // 监控告警
                String respCode = "None";
                resp = restTemplate.postForObject(predictionUrl, httpEntity, JSONObject.class);
                log.info("接口请求日志:调用决策引擎,url:{},request:{},resp:{}", predictionUrl, JsonUtil.toJson(httpEntity), JsonUtil.toJson(resp));
                if (resp == null || !Objects.equals(2000, resp.getInteger("code"))) {
                    log.error("接口返回错误码:调用决策引擎,url:{},request:{},resp:{}", predictionUrl, JsonUtil.toJson(httpEntity), JsonUtil.toJson(resp), NoneException.catError());
                }
                if (resp != null) {
                    respCode = String.valueOf(resp.getInteger("code"));
                }
                Tracer.logEvent("model_prediction", Optional.ofNullable(respCode).orElse("None"));
                return resp;
            } catch (Exception ex) {
                log.error("接口异常:调用决策引擎,url:{},request:{}", predictionUrl, JsonUtil.toJson(httpEntity), ex);
            }
            return null;
        }
    }

    // 补充accountNo，临时紧急
    private void addAccountNo(ModelPredictionReq modelPredictionReq) {
        try {
            if (modelPredictionReq == null) {
                return;
            }
            String modelName = modelPredictionReq.getModel_name();
            ModelPredictionReq.BizData bizData = modelPredictionReq.getBiz_data();
            if (StringUtils.isBlank(modelName) || bizData == null || bizData.getApp_user_id() == null) {
                return;
            }
            List<String> accountParamSupplementModels = JSONObject.parseArray(ApolloUtil.getAppProperty(PARAM_ACCOUNT_SUPPLEMENT_MODELS, "[]"), String.class);
            if (accountParamSupplementModels.contains(modelName)) {
                AcctInfoModel acctInfo = cisService.getAcctNoByUserNo(bizData.getApp_user_id());
                if (acctInfo != null) {
                    //补充账户号
                    String acctNo = acctInfo.getAccountNo();
                    if (StringUtils.isNotBlank(acctNo)) {
                        bizData.setAcct_no(acctNo);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("ModelPlatformService addAccountNo error");
        }
    }

    /**
     * 特殊引擎规则，参数补充
     *
     * @param modelPredictionReq
     * @return true=无需补充/补充成功; false=补充失败
     */
    private boolean wrapperModelPredictionReq(ModelPredictionReq modelPredictionReq) {
        try {
            if (modelPredictionReq == null) {
                return true;
            }
            String modelName = modelPredictionReq.getModel_name();
            ModelPredictionReq.BizData bizData = modelPredictionReq.getBiz_data();
            if (StringUtils.isBlank(modelName) || bizData == null || bizData.getApp_user_id() == null) {
                return true;
            }
            List<String> paramSupplementModels = JSONObject.parseArray(ApolloUtil.getAppProperty(PARAM_SUPPLEMENT_MODELS, "[]"), String.class);
            if (!paramSupplementModels.contains(modelName)) {
                return true;
            }

            BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo(bizData.getApp_user_id());
            if (resp == null || !resp.isCodeSucceed() || Objects.isNull(resp.getData())) {
                return false;
            }
            String custName = resp.getData().getCustName();
            String custNo = resp.getData().getCustNo();
            String idCardNumber = resp.getData().getIdCardNumber();
            String mobile = resp.getData().getMobile();
            if (StringUtils.isAnyBlank(custName, custNo, idCardNumber, mobile)) {
                return false;
            }
            bizData.setName(custName);
            bizData.setCust_no(custNo);
            bizData.setId_card_number(idCardNumber);
            bizData.setMobile(mobile);

            String deviceId = bizData.getDevice_id();
            String app = resp.getData().getApp();
            if (StringUtils.isAllBlank(deviceId, app)) {
                return false;
            }
            com.alibaba.fastjson2.JSONObject jsonObject = userService.queryLastDeviceByCustNoAndApp(app, custNo);
            if (jsonObject == null || Objects.isNull(jsonObject.get("device_id"))) {
                return false;
            }
            bizData.setDevice_id(jsonObject.get("device_id").toString());

            LogUtil.logDebug("ModelPlatformService wrapperModelPredictionReq bizData={}", JSONObject.toJSONString(bizData));
        } catch (Exception e) {
            log.error("ModelPlatformService wrapperModelPredictionReq error={}", e.getMessage(), e);
        }
        return true;
    }

    public List<String> getModelNames() {
        final String url = appConfigService.getXfModelPlatformModelsUrl();
        final String authorization = appConfigService.getXfModelPlatformModelsToken();

        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        header.set(Authorization, authorization);
        HttpEntity httpEntity = new HttpEntity(header);
        try {
            ResponseEntity<JSONObject> response = restTemplate.exchange(
                    url,
                    HttpMethod.GET,
                    httpEntity,
                    JSONObject.class);
            if (response.getBody() != null && response.getBody().getJSONObject("data") != null) {
                return JsonUtil.toList(JsonUtil.toJson(response.getBody().getJSONObject("data").get("model_codes")), String.class);
            }
        } catch (Exception ex) {
            log.error("获取引擎code列表异常,url:{},authorization:{}", url, authorization, ex);
        }
        return new ArrayList<>(0);
    }

    /**
     * 单策略维度灰度
     * @param realTimeReq
     * @param strategyId
     * @return
     */
    public AdbRealTimeResp getAdbRealTime(AdbRealTimeReq realTimeReq, Long strategyId) {

        if(strategyId != null && WhitelistSwitchUtil.commonGraySwitchByApollo("" + strategyId,"strategyDataFeatureSwitch")) {
            log.info("ModelPlatformService getAdbRealTime 策略ID={} 灰度策略开关通过，灰度调用特征平台", strategyId);
            return getAdbRealTime( realTimeReq);
        } else {
            log.info("ModelPlatformService getAdbRealTime 策略ID={} 灰度策略开关未通过", strategyId);
            return doGetAdbRealTime(realTimeReq);
        }
    }

    /**
     * 调用变量中心
     * TODO 特征平台分页查询
     * @param realTimeReq
     * @return
     */
    public AdbRealTimeResp getAdbRealTime(AdbRealTimeReq realTimeReq) {
        List<String> varCodes = realTimeReq.getVarCodes();
        if(CollectionUtils.isEmpty(varCodes)) {
            return null;
        }
        try {
            List<String> newCodes = varCodes.stream().filter(s ->
                            WhitelistSwitchUtil.commonGraySwitchByApollo(s, "labelDataFeatureSwitch") ||
                                    WhitelistSwitchUtil.commonGraySwitchByApollo(s, "enableDataFeatureSwitch"))
                    .collect(Collectors.toList());
            List<String> enableCodes = varCodes.stream().filter(s ->
                            WhitelistSwitchUtil.commonGraySwitchByApollo(s, "enableDataFeatureSwitch"))
                    .collect(Collectors.toList());
            // enableDataFeatureSwitch 生效部分直接需要剔除请求变量中心
            List<String> oldCodes = varCodes.stream().filter(s -> !enableCodes.contains(s)).collect(Collectors.toList());

            log.info("ModelPlatformService getAdbRealTime newCodes={} enableCodes={} oldCodes={} realTimeReq={}",
                    JSONObject.toJSONString(newCodes), JSONObject.toJSONString(enableCodes),
                    JSONObject.toJSONString(oldCodes), JSONObject.toJSONString(realTimeReq));

            FeatureQueryResponse newResp = null;
            AdbRealTimeResp oldResp = null;
            if (CollectionUtils.isNotEmpty(newCodes)) {
                AdbRealTimeReq newTimeReq = new AdbRealTimeReq();
                newTimeReq.setVarCodes(newCodes);
                newTimeReq.setInputParams(realTimeReq.getInputParams());
                newResp = doCallDataFeature(newTimeReq);
//                AdbRealTimeResp adbRealTime = transformFeatureResponse(newResp);

                log.info("ModelPlatformService getAdbRealTime newResp={} ", JSONObject.toJSONString(newResp));
//                log.info("ModelPlatformService getAdbRealTime adbRealTime={}",
//                        adbRealTime == null ? "null" : adbRealTime.getVariablesResponse());
            }
            if (CollectionUtils.isNotEmpty(oldCodes)) {
                realTimeReq.setVarCodes(oldCodes);
                oldResp = doGetAdbRealTime(realTimeReq);
                log.info("ModelPlatformService getAdbRealTime oldResp={}", JSONObject.toJSONString(oldResp));
            }

            checkCodeTrans(varCodes, newResp, oldResp);

            return combineAdbRealTimeResp(newResp, oldResp, enableCodes, oldCodes);
        } catch (Exception e) {
            log.error("ModelPlatformService getAdbRealTime error={}", e.getMessage(), e);
        }
        return null;
    }

    public void checkCodeTrans(List<String> varCodes, FeatureQueryResponse newResp, AdbRealTimeResp oldResp) {

        try {
            // 单变量陪跑-单独处理对比逻辑
            List<String> checkCodes = varCodes.stream().filter(s ->
                    WhitelistSwitchUtil.commonGraySwitchByApollo(s, "labelDataFeatureSwitch")).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(checkCodes)) {
                return;
            }

            if (newResp != null && oldResp != null) {

                String variablesResponse = oldResp.getVariablesResponse();
                Map<String, Object> oldLabelMapOrigin = StringUtils.isEmpty(variablesResponse) ? Maps.newHashMap() : JsonUtil.parse(variablesResponse, Map.class);

                checkCodes.stream().forEach(checkCode -> {
                    if (StringUtils.isEmpty(checkCode)) {
                        return;
                    }

                    Object newVal = Optional.ofNullable(newResp.getFeatureValues().get(checkCode)).orElse(new FeatureQueryResponse.FeatureValueModel()).getObj();

                    Map<String, Object> oldLabelMap = new HashMap<>();
                    Object oldVal = oldLabelMapOrigin.get(checkCode);
                    if (!Objects.equals(newVal, oldVal)) {
                        log.error("ModelPlatformService checkCodeTrans notCompare label={} coreResp={} oldResp={}", checkCode, newVal, oldVal);
                    } else {
                        if (WhitelistSwitchUtil.boolSwitchByApollo("checkCodeTransPassEnable")) {
                            log.info("ModelPlatformService checkCodeTrans label={} coreResp={} oldResp={}", checkCode, newVal, oldVal);
                        }
                    }
                });
            } else {
                log.error("ModelPlatformService checkCodeTrans notCompare nullError label={} newResp={} oldResp={}",
                        JSONObject.toJSONString(checkCodes), JSONObject.toJSONString(newResp), JSONObject.toJSONString(oldResp));
            }

        } catch (Exception e) {
            log.error("ModelPlatformService checkCodeTrans error={}", e.getMessage(), e);
        }
    }

    public FeatureQueryResponse doCallDataFeature(AdbRealTimeReq realTimeReq) {

        Map<String, Object> outInput = realTimeReq.getInputParams();

        FeatureQueryResponse featureResponse = dataFeatureService.getDateFeatureResponse(outInput, realTimeReq.getVarCodes());

        return featureResponse;
    }

    public AdbRealTimeResp transformFeatureResponse(FeatureQueryResponse featureResponse) {

        if(featureResponse != null && featureResponse.isSuc() && featureResponse.getFeatureValues() != null) {
            AdbRealTimeResp adbRealTimeResp = new AdbRealTimeResp();
            adbRealTimeResp.setCode("10000");
            adbRealTimeResp.setMessage("成功");
            try {
                Map<String, Object> labelMap = new HashMap<>();
                featureResponse.getFeatureValues().forEach((k, v) -> {
                    labelMap.put(k, v.getObj());
                });

                adbRealTimeResp.setVariablesResponse(JSONObject.toJSONString(labelMap));
                log.info("ModelPlatformService transformFeatureResponse featureResponse={} adbRealTimeResp={}",
                        JSONObject.toJSONString(featureResponse), adbRealTimeResp.getVariablesResponse());
                return adbRealTimeResp;
            } catch (Exception e) {
                log.error("ModelPlatformService transformFeatureResponse error={}", e.getMessage(), e);
            }
        }
        return null;
    }


    public AdbRealTimeResp combineAdbRealTimeResp(FeatureQueryResponse newResp, AdbRealTimeResp oldResp,
                                                  List<String> enableCodes, List<String> oldCodes) {

        if(CollectionUtils.isNotEmpty(enableCodes)) { // 需要合并 或 转换
            oldResp = oldResp == null ? new AdbRealTimeResp(): oldResp;

            String variablesResponse = oldResp.getVariablesResponse();
            Map<String, Object> labelMap = StringUtils.isEmpty(variablesResponse) ? Maps.newHashMap() : JsonUtil.parse(variablesResponse, Map.class);
//            AdbRealTimeResp newResult = transformFeatureResponse(newResp);
            if (newResp != null && newResp.getFeatureValues() != null) {
                Map<String, FeatureQueryResponse.FeatureValueModel> newFeaturMap = newResp.getFeatureValues();
                if(newResp.getFeatureValues().size() < enableCodes.size()) {
                    log.error("ModelPlatformService combineAdbRealTimeResp 特征平台返回数据不足 enableCodes={} newResp={} oldResp={}",
                            JSONObject.toJSONString(enableCodes), JSONObject.toJSONString(newResp), JSONObject.toJSONString(oldResp));
                }

                enableCodes.stream().forEach(s -> {
                    if (newFeaturMap.containsKey(s)) {
                        labelMap.put(s, newFeaturMap.get(s).getObj());
                    }
                });
                oldResp.setVariablesResponse(JsonUtil.toJson(labelMap));
            } else {
                String newRespStr = JSONObject.toJSONString(newResp);
                String oldRespStr = JSONObject.toJSONString(oldResp);
                String enableCodesStr = JSONObject.toJSONString(enableCodes);

                log.error("ModelPlatformService combineAdbRealTimeResp 特征平台返回数据为空 newResp={} oldResp={} enabelCodes={}",
                        newRespStr, oldRespStr, enableCodesStr);
                // 发送钉钉通知 需标记异常环境 TODO 需要有频控配置，不然会发送大量通知
//                String active = SpringUtil.getProperty("spring.profiles.active");
//                String content = String.format("变量中心特征合并异常 环境:%s, enableCodes:%s", active, enableCodesStr);
//                DingTalkUtil.sendTextToDingTalk(DingTalkUtil.urlSignProcess(TECH_DINGTALK_ROBOT_URL, TECH_DINGTALK_ROBOT_SECRET) , content,TECH_DINGTALK_APP_IDS, false);
            }
            if(StringUtils.isNotEmpty(oldResp.getVariablesResponse())) {
                oldResp.setCode("10000");
                oldResp.setMessage("成功");
            }
            log.info("ModelPlatformService combineAdbRealTimeResp oldResp={} enableCodes={}", JSONObject.toJSONString(oldResp),
                    JSONObject.toJSONString(enableCodes));
            return oldResp;

        } else { // 返回新或老
            if(CollectionUtils.isNotEmpty(oldCodes)) {
                return oldResp;
            } else {
                return transformFeatureResponse(newResp);
            }
        }
    }

    public AdbRealTimeResp doGetAdbRealTime(AdbRealTimeReq realTimeReq) {
        wrapperRealTimeReq(realTimeReq);
        wrapperRealTimeReqWithDefaultValue(realTimeReq);
        final String url = appConfigService.getAdbRealTimeVariableUrl();
        HttpHeaders header = new HttpHeaders();
        header.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(realTimeReq, header);

        try {
            AdbRealTimeResp realTimeResp = restTemplate.postForObject(url, httpEntity, AdbRealTimeResp.class);
            log.info("ModelPlatformService doGetAdbRealTime url={} request={} resp={}", url, JsonUtil.toJson(httpEntity), JsonUtil.toJson(realTimeResp));
            if (realTimeResp == null || !Objects.equals("10000", realTimeResp.getCode())) {
                log.error("接口返回错误码调用变量中心 url:{},request:{},resp:{}", url, JsonUtil.toJson(httpEntity), JsonUtil.toJson(realTimeResp), NoneException.catError());
            }
            return realTimeResp;
        } catch (Exception ex) {
            log.error("接口异常调用变量中心 url:{},request:{}", url, JsonUtil.toJson(httpEntity), ex);
        }
        return null;
    }

    /**
     * 特殊特征，参数补充
     *
     * @param realTimeReq
     */
    private void wrapperRealTimeReq(AdbRealTimeReq realTimeReq) {
        if (realTimeReq == null || CollectionUtils.isEmpty(realTimeReq.getVarCodes())) {
            return;
        }
        try {
            List<String> varCodes = realTimeReq.getVarCodes();
            String varCodeBefore = JSONObject.toJSONString(varCodes);
            List<String> needIdCardNumberCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.idcardnumber", "[]"), String.class);
            List<String> needCustNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.custno", "[]"), String.class);
            List<String> needAcctNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.acctno", "[]"), String.class);
            List<String> needUserNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.userno", "[]"), String.class);
            Set<String> needCodeSet = Sets.newHashSet();
            needCodeSet.addAll(needIdCardNumberCodes);
            needCodeSet.addAll(needCustNoCodes);
            needCodeSet.addAll(needAcctNoCodes);
            needCodeSet.addAll(needUserNoCodes);

            if (CollectionUtils.containsAny(varCodes, needCodeSet)) {
                Map<String, Object> inputParams = Optional.ofNullable(realTimeReq.getInputParams()).orElse(Maps.newHashMap());
                Object userNo = inputParams.get("app_user_id");
                if (userNo == null) {
                    varCodes.removeIf(needCodeSet::contains);
                } else {
                    String userNoStr = String.valueOf(userNo);
                    if (StringUtils.isNotBlank(userNoStr)) {
                        inputParams.putIfAbsent("user_no", userNoStr);
                    } else {
                        varCodes.removeIf(needUserNoCodes::contains);
                    }
                    BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo((Long) userNo);
                    if (resp != null && resp.isCodeSucceed() && Objects.nonNull(resp.getData())) {
                        // 补充身份证号
                        String idCardNumber = resp.getData().getIdCardNumber();
                        if (StringUtils.isNotBlank(idCardNumber)) {
                            inputParams.putIfAbsent("id_card_number", idCardNumber);
                            inputParams.putIfAbsent("app", resp.getData().getApp());
                        } else {
                            varCodes.removeIf(needIdCardNumberCodes::contains);
                        }
                        // 补充客户号
                        String custNo = resp.getData().getCustNo();
                        if (StringUtils.isNotBlank(custNo)) {
                            inputParams.putIfAbsent("cust_no", custNo);
                            inputParams.putIfAbsent("app", resp.getData().getApp());
                        } else {
                            varCodes.removeIf(needCustNoCodes::contains);
                        }
                    } else {
                        varCodes.removeIf(needIdCardNumberCodes::contains);
                        varCodes.removeIf(needCustNoCodes::contains);
                    }
                    // 仅在需要账户号时，查询并补充账户号
                    if (CollectionUtils.containsAny(varCodes, needAcctNoCodes)) {
                        AcctInfoModel acctInfo = cisService.getAcctNoByUserNo((Long) userNo);
                        if (acctInfo != null) {
                            //补充账户号
                            String acctNo = acctInfo.getAccountNo();
                            if (StringUtils.isNotBlank(acctNo)) {
                                inputParams.putIfAbsent("acct_no", acctNo);
                                inputParams.putIfAbsent("app", acctInfo.getApp());
                            } else {
                                varCodes.removeIf(needAcctNoCodes::contains);
                            }
                        } else {
                            varCodes.removeIf(needAcctNoCodes::contains);
                        }
                    }
                }
                LogUtil.logDebug("ModelPlatformService wrapperRealTimeReq varCodeBefore={} needIdCardNumberCodes={} needCustNoCodes={} needAcctNoCodes={} needUserNoCodes={} realTimeReq={} varCodes={}", JSONObject.toJSONString(varCodeBefore), JSONObject.toJSONString(needIdCardNumberCodes), JSONObject.toJSONString(needCustNoCodes), JSONObject.toJSONString(needAcctNoCodes), JSONObject.toJSONString(needUserNoCodes), JSONObject.toJSONString(realTimeReq), JSONObject.toJSONString(varCodes));
            }
        } catch (Exception e) {
            log.warn("ModelPlatformService wrapperRealTimeReq error={}", e.getMessage(), e);
        }
    }

    /**
     * 特殊特征，参数补充：补充不到传默认值
     * 临时紧急
     * @param realTimeReq
     */
    private void wrapperRealTimeReqWithDefaultValue(AdbRealTimeReq realTimeReq) {
        if (realTimeReq == null || CollectionUtils.isEmpty(realTimeReq.getVarCodes())) {
            return;
        }
        try {
            List<String> varCodes = realTimeReq.getVarCodes();
            List<String> needCustNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.custno.default.null", "[]"), String.class);

            if (CollectionUtils.containsAny(varCodes, needCustNoCodes)) {
                Map<String, Object> inputParams = Optional.ofNullable(realTimeReq.getInputParams()).orElse(Maps.newHashMap());
                inputParams.put("cust_no", "null"); // 默认为"null"

                Object userNo = inputParams.get("app_user_id");
                if (userNo != null) {
                    BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo((Long) userNo);
                    if (resp != null && resp.isCodeSucceed() && Objects.nonNull(resp.getData())) {
                        String custNo = resp.getData().getCustNo();
                        if (StringUtils.isNotBlank(custNo)) {
                            inputParams.put("cust_no", custNo); // 补充客户号
                        }
                    }
                }
                LogUtil.logDebug("ModelPlatformService wrapperRealTimeReqWithDefaultValue varCodes={} needCustNoCodes={} realTimeReq={}", JSONObject.toJSONString(varCodes), JSONObject.toJSONString(needCustNoCodes), JSONObject.toJSONString(realTimeReq));
            }
        } catch (Exception e) {
            log.warn("ModelPlatformService wrapperRealTimeReqWithDefaultValue error={}", e.getMessage(), e);
        }
    }

}
