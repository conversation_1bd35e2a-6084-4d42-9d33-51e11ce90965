package com.xftech.cdp.domain.touch.model;

import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;

/**
 * 触达渠道枚举
 * 统一的触达渠道定义，与现有StrategyMarketChannelEnum保持兼容
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
public enum TouchChannel {
    
    /**
     * 短信渠道
     */
    SMS("SMS", "短信", "短信触达渠道"),
    
    /**
     * 语音外呼渠道
     */
    VOICE("VOICE", "语音外呼", "电话语音外呼渠道"),
    
    /**
     * 推送渠道
     */
    PUSH("PUSH", "推送", "APP推送渠道"),
    
    /**
     * 优惠券渠道
     */
    COUPON("COUPON", "优惠券", "优惠券发放渠道"),
    
    /**
     * AI外呼渠道
     */
    AI_CALL("AI_CALL", "AI外呼", "AI智能外呼渠道"),
    
    /**
     * 生活权益渠道
     */
    LIFE_RIGHTS("LIFE_RIGHTS", "生活权益", "生活权益发放渠道"),
    
    /**
     * X天免息渠道
     */
    X_DAY_INTEREST_FREE("X_DAY_INTEREST_FREE", "X天免息", "X天免息权益渠道"),
    
    /**
     * 提额渠道
     */
    INCREASE_AMOUNT("INCREASE_AMOUNT", "提额", "额度提升渠道"),
    
    /**
     * 无渠道（用于测试或特殊场景）
     */
    NONE("NONE", "无渠道", "无实际触达渠道");
    
    private final String code;
    private final String name;
    private final String description;
    
    TouchChannel(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static TouchChannel fromCode(String code) {
        for (TouchChannel channel : values()) {
            if (channel.getCode().equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("未知的触达渠道代码: " + code);
    }
    
    /**
     * 从现有的StrategyMarketChannelEnum转换
     */
    public static TouchChannel fromStrategyMarketChannel(StrategyMarketChannelEnum strategyChannel) {
        if (strategyChannel == null) {
            return NONE;
        }
        
        switch (strategyChannel) {
            case SMS:
                return SMS;
            case VOICE:
                return VOICE;
            case PUSH:
                return PUSH;
            case COUPON:
                return COUPON;
            case AI_CALL:
                return AI_CALL;
            case LIFE_RIGHTS:
                return LIFE_RIGHTS;
            case X_DAY_INTEREST_FREE:
                return X_DAY_INTEREST_FREE;
            case INCREASE_AMOUNT:
                return INCREASE_AMOUNT;
            case NONE:
                return NONE;
            default:
                throw new IllegalArgumentException("不支持的策略渠道类型: " + strategyChannel);
        }
    }
    
    /**
     * 转换为StrategyMarketChannelEnum
     */
    public StrategyMarketChannelEnum toStrategyMarketChannel() {
        switch (this) {
            case SMS:
                return StrategyMarketChannelEnum.SMS;
            case VOICE:
                return StrategyMarketChannelEnum.VOICE;
            case PUSH:
                return StrategyMarketChannelEnum.PUSH;
            case COUPON:
                return StrategyMarketChannelEnum.COUPON;
            case AI_CALL:
                return StrategyMarketChannelEnum.AI_CALL;
            case LIFE_RIGHTS:
                return StrategyMarketChannelEnum.LIFE_RIGHTS;
            case X_DAY_INTEREST_FREE:
                return StrategyMarketChannelEnum.X_DAY_INTEREST_FREE;
            case INCREASE_AMOUNT:
                return StrategyMarketChannelEnum.INCREASE_AMOUNT;
            case NONE:
                return StrategyMarketChannelEnum.NONE;
            default:
                throw new IllegalArgumentException("不支持的触达渠道类型: " + this);
        }
    }
    
    /**
     * 判断是否为实际触达渠道（排除NONE）
     */
    public boolean isActualChannel() {
        return this != NONE;
    }
    
    /**
     * 判断是否为消息类渠道（SMS、PUSH）
     */
    public boolean isMessageChannel() {
        return this == SMS || this == PUSH;
    }
    
    /**
     * 判断是否为语音类渠道（VOICE、AI_CALL）
     */
    public boolean isVoiceChannel() {
        return this == VOICE || this == AI_CALL;
    }
    
    /**
     * 判断是否为权益类渠道（COUPON、LIFE_RIGHTS、X_DAY_INTEREST_FREE、INCREASE_AMOUNT）
     */
    public boolean isBenefitChannel() {
        return this == COUPON || this == LIFE_RIGHTS || 
               this == X_DAY_INTEREST_FREE || this == INCREASE_AMOUNT;
    }
}
