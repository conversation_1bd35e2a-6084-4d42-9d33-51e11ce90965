package com.xftech.cdp.domain.event.processor;

import cn.hutool.core.convert.Convert;
import com.xftech.cdp.domain.event.model.annotation.ProcessorRule;
import com.xftech.cdp.domain.event.model.config.ConstantConfig;
import com.xftech.cdp.domain.event.model.config.FieldConfig;
import com.xftech.cdp.domain.event.model.dto.Field;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.enums.DataProcessEnum;
import com.xftech.cdp.domain.event.model.enums.DataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

/**
 * 常量值数据处理器处理类
 * <AUTHOR>
 * @version $ ConstantProcessor, v 0.1 2024/11/19 15:04 snail Exp $
 */
@Slf4j
@Component
@ProcessorRule(processor = DataProcessEnum.CONSTANT)
public class ConstantProcessor extends AbstractDataProcessor{
    @Override
    public Field process(FieldDetail detail, FieldConfig fieldConfig, Map<String, Object> values) {
        ConstantConfig constantConfig = (ConstantConfig) fieldConfig;

        DataTypeEnum dataType = DataTypeEnum.getDataType(constantConfig.getDataType());
        if(Objects.isNull(dataType)){
            return null;
        }

        Object result = Convert.convert(dataType.getClazz(),constantConfig.getConstantValue());
        return new Field(detail.getTargetField(),result);
    }
}
