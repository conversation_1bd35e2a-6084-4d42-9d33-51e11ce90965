package com.xftech.cdp.domain.crowd.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.api.dto.req.LabelReq;
import com.xftech.cdp.infra.repository.cdp.crowd.po.LabelDo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 数仓标签配置表操作
 *
 * <AUTHOR>
 * @since 2023/2/13
 */

@Component
public class LabelRepository {

    /**
     * 获取所有数仓标签配置记录
     *
     * @return 标签配置列表
     */
    public List<LabelDo> getAll(String businessType) {
        return DBUtil.selectList("label.getAll", businessType);
    }

    /**
     * 获取所有标签
     *
     * @return 标签配置列表
     */
    public List<LabelDo> getAllWithoutLimit() {
        return DBUtil.selectList("label.getAllWithoutLimit", null);
    }

    /**
     * 根据id批量查询
     *
     * @param distinctMap id列表
     * @return id列表对应的数仓标签配置列表
     */
    public List<LabelDo> selectBatchIds(List<Long> distinctMap) {
        if (CollectionUtils.isEmpty(distinctMap)) {
            return new ArrayList<>(0);
        }
        return DBUtil.selectList("label.selectBatchIds", distinctMap);

    }

    public LabelDo selectById(Long id){
        return DBUtil.selectOne("label.selectById", id);
    }

    public boolean insert(LabelDo labelDo) {
        return DBUtil.insert("label.insertSelective", labelDo) > 0;
    }

    public boolean updateById(LabelDo labelDo){
        labelDo.setUpdatedTime(null);
        return DBUtil.update("label.updateByPrimaryKeySelective", labelDo) > 0;
    }

    public List<LabelDo> getByLabelCode(String labelCode) {
        if (StringUtils.isBlank(labelCode)){
            return null;
        }
        return DBUtil.selectList("label.selectByLabelCode", labelCode);
    }
}
