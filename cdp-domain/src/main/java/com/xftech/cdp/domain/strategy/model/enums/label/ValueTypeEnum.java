package com.xftech.cdp.domain.strategy.model.enums.label;

import com.xftech.cdp.domain.strategy.exception.StrategyException;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.constant.Constants;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.text.NumberFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat10;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat11;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat13;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat29;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat30;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat31;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat5;
import static com.xftech.cdp.infra.aviator.enums.DataTimeFormat.DataTimeFormat9;

/**
 * |String|字符串|abc||
 * |Integer|整型|22||
 * |Long|长整型|98172122132||
 * |Double|小数浮点|32.212||
 * |Currency|金额|32.212|保留小数位2位或3位|
 * |Boolean|布尔型|true,false||
 * |Date|日期|日期格式的格式化信息可按照字段格式进行格式化|格式化日期|
 * |Timestamp|时间戳|1970年1月1日的相对时间毫秒数|相对时间|
 * |Map|k,v结构|{"k","v"}|java中的map,和一个json中的key,value数据对|
 * |Dict|字典类型|字典中的值保存在字段定义的词典数据项中||
 * |Enum|枚举类型|枚举类型是有限固定值||
 * |List|列表类型|["abc","bcd"],||
 * 值类型 枚举
 */
public enum ValueTypeEnum {

    STRING(0, "字符串", String.class, "VARCHAR"),
    INTEGER(1, "整数", Integer.class, "INT"),
    Boolean(2, "布尔", Boolean.class, "INT"),
    LONG(3, "长整数", Long.class, "BIGINT"),
    DATE(4, "日期", LocalDate.class, "DATE"),
    DOUBLE(5, "浮点数", Double.class, "DOUBLE"),
    Currency(6, "金额", BigDecimal.class, "DECIMAL"),
    DICTIONARY(7, "字典", List.class, "VARCHAR"),
    TIMESTAMP(8, "时间戳", Timestamp.class, "TIMESTAMP"),
    Maps(9, "对象", Map.class, "TEXT"),
    Lists(10, "列表", List.class, "TEXT"),
    ListItem(11, "对象列表", List.class, "TEXT"),
    YEAR_MONTH(12, "年月(整型)", Integer.class, "INT"),
    YEAR_MONTH_DATE(13, "日期(整型)", Integer.class, "INT"),
    MONTH(14, "月份(整型)", Integer.class, "INT"),
    MONTH_DATE(15, "月份日期(整型)", Integer.class, "INT"),
    DAY_OF_MONTH(16, "月份中的日期(整型)", Integer.class, "INT"),
    TIME_H_M_S(17, "时分秒(HH:mm:ss)", Integer.class, "INT"),
    NULL(18, "空对象", null, "NULL");


    private Class<?> clazz;

    private Integer id;

    private String description;

    private String dbFieldType;

    private static final Logger logger = LoggerFactory.getLogger(ValueTypeEnum.class);

    ValueTypeEnum(Integer id, String description, Class<?> clazz, String dbFieldType) {
        this.id = id;
        this.description = description;
        this.clazz = clazz;
        this.dbFieldType = dbFieldType;
    }

    public static ValueTypeEnum getInstance(String valueType) {
        for (ValueTypeEnum valueTypeEnum : ValueTypeEnum.values()) {
            if (valueTypeEnum.name().equalsIgnoreCase(valueType)) {
                return valueTypeEnum;
            }
        }
        throw new StrategyException("字符类型异常");
    }


    public Object normalizeValue(Object value) {
        return this.normalizeValue(value, null);
    }

    public Object normalizeValueNullNoValue(String labelName, Object value) {
        // 标签异常兜底值处理 TODO 优雅处理
        if (JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.fallback.labels.valueToNull", "[]"), String.class).contains(labelName)
                && Objects.isNull(value)) {
            return null;
        }
        return this.normalizeValue(value, null);
    }

    /**
     * 正规化字段值
     *
     * @param value
     * @param format
     * @return
     */
    public Object normalizeValue(Object value, String format) {
        Object nValue = value;
        try {
            switch (this) {
                case LONG:
                    nValue = normalLong(value);
                    break;
                case DOUBLE:
                    nValue = normalDouble(value);
                    break;
                case Boolean:
                    nValue = normalBoolean(value);
                    break;
                case INTEGER:
                    nValue = normalInteger(value);
                    break;
                case STRING:
                    nValue = normalString(value, format);
                    break;
                case Currency:
                    nValue = normalCurrency(value, format);
                    break;
                case YEAR_MONTH_DATE:
                    nValue = normalIntDate(value);
                    break;
                case DATE:
                    nValue = normalDate(value,null);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return nValue;
    }

    public Object smsFormatValue(Object value, String format) {
        Object nValue = value;
        try {
            switch (this) {
                case LONG:
                    nValue = normalLong(value);
                    break;
                case DOUBLE:
                    nValue = normalDouble(value);
                    break;
                case Boolean:
                    nValue = normalBoolean(value);
                    break;
                case INTEGER:
                    nValue = normalInteger(value);
                    break;
                case STRING:
                    nValue = normalString(value, null);
                    break;
                case Currency:
                    nValue = normalCurrency(value, format);
                    break;
                case YEAR_MONTH_DATE:
                    nValue = normalIntDate(value);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
           logger.warn("smsFormatValue error, value:{}, format:{}", value, format, e);
        }

        return nValue;
    }

    /**
     * 日期整型格式
     *
     * @param value
     * @return
     */
    private Object normalIntDate(Object value) {

        if (value != null && value instanceof String && !value.equals("")) {
            return Integer.parseInt((String) value);
        }
        return value;
    }

    /**
     * 日期格式
     *
     * @param value
     * @return
     */
    private Object normalDate(Object value, String format) {
        try {
            if (value == null || value instanceof Date) {
                return value;
            } else if (value instanceof Number) {
                return new Date(((Number) value).longValue());
            } else if (value instanceof String) {
                if ("".equals(value)) {
                    return null;
                }
                DateTimeFormatter dateTimeFormatter;
                String v = value.toString();
                if (StringUtils.isNotEmpty(format)) {
                    dateTimeFormatter = DateTimeFormatter.ofPattern(format);
                } else {
                    //日期

                    if (v.length() == DataTimeFormat5.getFormat().length()) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat5.getFormat());
                        //日期时间
                    } else if (v.length() == DataTimeFormat9.getFormat().length()) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat9.getFormat());
                    } else if (v.length() == DataTimeFormat10.getFormat().length()) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat10.getFormat());
                    } else if (DataTimeFormat31.match(v)) {
                        dateTimeFormatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
                    } else if (DataTimeFormat13.match(v)) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat13.getFormat());
                    } else if (DataTimeFormat11.match(v)) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat11.getFormat());
                    } else if (DataTimeFormat29.match(v)) {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat29.getFormat());
                    } else {
                        dateTimeFormatter = DateTimeFormatter.ofPattern(DataTimeFormat30.getFormat());
                    }
                }

                ZoneId zone = ZoneId.systemDefault();
                //先尝试转localDateTime
                try {

                    LocalDateTime localDateTime = LocalDateTime.parse(v, dateTimeFormatter);
                    Instant instant = localDateTime.atZone(zone).toInstant();
                    return Date.from(instant);
                } catch (Exception e) {
                    try {
                        //失败再尝试转localDate
                        LocalDate localDate = LocalDate.parse(v, DateTimeFormatter.ofPattern(DataTimeFormat5.getFormat()));
                        Instant instant = localDate.atStartOfDay().atZone(zone).toInstant();

                        return Date.from(instant);
                    } catch (Exception e2) {
                    }
                }
                try {
                    if (value.toString().startsWith("1") && value.toString().length() == 13) {
                        return new Date(Long.parseLong(value.toString()));
                    }
                } catch (Exception e3) {
                }
                return null;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 日期整型格式
     *
     * @param value
     * @return
     */
    private Object normalDateTime(Object value) {
        return LocalDateTime.parse(value.toString(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }

    private Object normalInteger(Object value) {
        try {
            if (value == null) {
                return 0;
            } else if (value instanceof Integer) {
                return value;
            } else if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                return Integer.parseInt(value.toString());
            } else {
                return Integer.valueOf(value.toString());
            }
        } catch (Exception e) {
            return 0;
        }
    }


    private Object normalLong(Object value) {
        try {
            if (value == null) {
                return 0;
            } else if (value instanceof Long) {
                return value;
            } else if (value instanceof Number) {
                return ((Number) value).longValue();
            } else {
                return Long.valueOf(value.toString());
            }
        } catch (Exception e) {
            return 0L;
        }
    }

    private Object normalDouble(Object value) {
        try {
            if (value == null || value.toString().trim().length() == 0) {
                return 0d;
            } else if (value instanceof Double) {
                return value;
            } else if (value instanceof Number) {
                return ((Number) value).doubleValue();
            } else if (value.toString().matches("[-]?[\\d.]+")) {
                return Double.parseDouble(value.toString());
            } else {
                return 0d;
            }
        } catch (Exception e) {
            return 0d;
        }
    }


    private Object normalCurrency(Object value, String format) {
        try {
            if (value == null || StringUtils.isEmpty(value.toString().trim())) {
                return BigDecimal.ZERO;
            } else if (format == null && value instanceof Number) {
                return BigDecimal.valueOf(((Number) value).doubleValue());
            } else if (format != null) {
                if (Constants.SMS_CURRENCY_FORMAT.equals(format)) {
                    return smsCurrencyFormat(value);
                } else {
                    DecimalFormatSymbols dfs = new DecimalFormatSymbols();
                    dfs.setDecimalSeparator('.');
                    dfs.setGroupingSeparator(',');
                    dfs.setMonetaryDecimalSeparator('.');
                    //"###,###.##"
                    DecimalFormat decimalFormat = new DecimalFormat(format, dfs);
                    return BigDecimal.valueOf(decimalFormat.parse(value.toString()).doubleValue());
                }
            } else {
                return BigDecimal.valueOf(NumberFormat.getNumberInstance().parse(value.toString()).doubleValue());
            }
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private Object smsCurrencyFormat(Object value) {
        BigDecimal bigDecimal = new BigDecimal(value.toString()).setScale(1, BigDecimal.ROUND_HALF_UP);
        if (bigDecimal.toString().endsWith("0")) {
            return bigDecimal.intValue();
        }
        return bigDecimal;
    }

    private Object normalBoolean(Object value) {
        try {
            if (value == null) {
                return false;
            } else if (value instanceof Boolean) {
                return value;
            } else if (value instanceof Number) {
                if (((Number) value).intValue() > 0) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return java.lang.Boolean.valueOf(value.toString());
            }
        } catch (Exception e) {
            return false;
        }
    }

    private Object normalString(Object value, String format) {
        try {
            if (value == null) {
                return value;
            }
            if (format == null || format.equals("")) {
                return value.toString();
            }
            return String.format(format, value);
        } catch (Exception e) {
            return "";
        }
    }

}
