package com.xftech.cdp.domain.crowd.service;

import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdLabelDo;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
public interface CrowdLabelService {
    CrowdLabelDo insert(CrowdLabelDo crowdLabelDo);

    /**
     *
     * @param crowdPackDo
     * @return 第1个为includeNewRandom，第2个为excludeNewRandom
     */
    List<List<String>> getNewRandomLabels(CrowdPackDo crowdPackDo);
}
