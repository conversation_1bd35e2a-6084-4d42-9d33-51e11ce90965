package com.xftech.cdp.domain.crowd.repository;


import com.xftech.base.common.util.DBUtil;
import com.xftech.base.database.Page;
import com.xftech.cdp.api.dto.req.CrowdListReq;
import com.xftech.cdp.api.dto.req.CrowdPackListReq;
import com.xftech.cdp.api.dto.req.label.AssociatedCrowdsReq;
import com.xftech.cdp.domain.crowd.model.enums.CrowdFilterMethodEnum;
import com.xftech.cdp.domain.crowd.model.enums.CrowdStatusEnum;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPackDo;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 人群包主表操作
 *
 * <AUTHOR>
 * @since 2023/2/9 01:36
 */
@Component
public class CrowdPackRepository {

    /**
     * 根据条件分页查询人群包
     *
     * @param crowdListReq 参数对象
     * @return 当前页人群包列表
     */
    public Page<CrowdPackDo> selectPage(CrowdListReq crowdListReq) {
        return DBUtil.selectPage("crowd.selectByCondition", crowdListReq, crowdListReq.getBeginNum(), crowdListReq.getSize());
    }

    public List<CrowdPackDo> getCrowdPackList(CrowdPackListReq crowdPackListReq) {
        return DBUtil.selectList("crowd.getCrowdPackList", crowdPackListReq);
    }

    public List<CrowdPackDo> getCrowdPackAll(Integer pullType) {
        Map<String, Object> param = new HashMap<>();
        param.put("pullType",pullType);
        return DBUtil.selectList("crowd.getCrowdPackAll", param);
    }

    public List<CrowdPackDo> queryAllEffectiveCrowdsByTime(LocalDateTime time) {
        Map<String, Object> param = new HashMap<>();
        param.put("time", time);
        param.put("filterMethod", CrowdFilterMethodEnum.LABEL.getCode());
        return DBUtil.selectList("crowd.queryAllEffectiveCrowdsByTime", param);
    }

    public List<CrowdPackDo> startCrowdPackJob(LocalDateTime time, Integer pullType) {
        Map<String, Object> param = new HashMap<>();
        param.put("time", time);
        param.put("filterMethod", CrowdFilterMethodEnum.LABEL.getCode());
        param.put("pullType", pullType);
        return DBUtil.selectList("crowd.startCrowdPackJob", param);
    }

    public List<CrowdPackDo> queryAllExpireCrowdsByTime(LocalDateTime time) {
        return DBUtil.selectList("crowd.queryAllExpireCrowdsByTime", time);
    }

    public CrowdPackDo selectMinExecTimeBo(LocalDateTime now, Integer pullType) {
        Map<String, Object> param = new HashMap<>();
        param.put("time", now);
        param.put("filterMethod", CrowdFilterMethodEnum.LABEL.getCode());
        param.put("pullType", pullType);
        return DBUtil.selectOne("crowd.selectMinExecTimeBo", param);
    }

    public List<CrowdPackDo> queryLeRefreshTime(LocalDateTime time, Integer pullType) {
        Map<String, Object> param = new HashMap<>();
        param.put("time", time);
        param.put("pullType", pullType);
        return DBUtil.selectList("crowd.queryLeRefreshTime", param);
    }

    /**
     * 根据人群包名称查询人群包
     *
     * @param crowdName 人群包名称
     * @return 该名称对应的人群包
     */
    public CrowdPackDo selectByNameAndBusinessType(String crowdName, String businessType) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdName", crowdName);
        params.put("businessType", businessType);
        return DBUtil.selectOne("crowd.selectByNameAndBusinessType", params);
    }

    public List<Long> selectEffectiveCrowd(List<Long> crowdIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdIds);
        return DBUtil.selectList("crowd.selectEffectiveCrowd", params);
    }

    /**
     * 根据人群包id查询人群包数量
     *
     * @param crowdPackNos 人群包id数组
     * @return 人群包数量
     */
    public int countCrowdPackNum(String[] crowdPackNos) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdPackNos);
        return DBUtil.selectOne("crowd.countCrowdPackNum", params);
    }

    /**
     * 根据人群包id查询人群包
     *
     * @param crowdPackIds
     * @return
     */
    public List<CrowdPackDo> selectCrowdPackByIdsAndBusinessType(String[] crowdPackIds, String businessType) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdPackIds);
        params.put("businessType", businessType);
        return DBUtil.selectList("crowd.selectCrowdPackByIds", params);
    }

    public List<CrowdPackDo> selectByIds(String[] crowdPackIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdPackIds);
        return DBUtil.selectList("crowd.selectByIds", params);
    }

    /**
     * 根据人群包id统计人数数量
     *
     * @param crowdPackNos 人群包id数组
     * @return 人群包id数组对应的人群包总人数
     */
    public Integer countCrowdPackUserNum(String[] crowdPackNos) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdPackNos);
        return DBUtil.selectOne("crowd.countCrowdPackUserNum", params);
    }

    public List<Long> selectAllCrowdIdsByTypeNoDFlag(CrowdFilterMethodEnum crowdFilterMethodEnum) {
        return DBUtil.selectList("crowd.selectAllCrowdIdsByTypeNoDFlag", crowdFilterMethodEnum.getCode());
    }

    /**
     * 是否存在过期人群包
     *
     * @param crowdId
     * @return
     */
    public boolean existExpiredCrowd(Long crowdId) {
        List<Long> crowdIds = new ArrayList<>();
        crowdIds.add(crowdId);
        return this.existExpiredCrowd(crowdIds);
    }

    /**
     * 是否存在过期人群包
     *
     * @param crowdIds
     * @return
     */
    public boolean existExpiredCrowd(List<Long> crowdIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdIds);
        return DBUtil.selectOne("crowd.existExpiredCrowd", params) != null;
    }

    /**
     * 插入一条人群包记录
     *
     * @param crowdPackDo 人群包对象
     */
    public boolean insert(CrowdPackDo crowdPackDo) {
        return DBUtil.insert("crowd.insertSelective", crowdPackDo) > 0;
    }

    /**
     * 根据人群包id查询人群包
     *
     * @param crowdId 人群包id
     * @return 该id对应的人群包记录
     */
    public CrowdPackDo selectById(Long crowdId) {
        return DBUtil.selectOne("crowd.selectByPrimaryKey", crowdId);
    }

    /**
     * 查询所有人群包
     */
    public List<CrowdPackDo> refreshT0CrowdPack() {
        return DBUtil.selectList("crowd.refreshT0CrowdPack", null);
    }

    /**
     * 根据人群包id更新人群包
     *
     * @param crowdPackDo 人群包对象
     */
    public boolean updateById(CrowdPackDo crowdPackDo) {
        return DBUtil.update("crowd.updateByPrimaryKeySelective", crowdPackDo) > 0;

    }

    /**
     * 批量更新人群包
     *
     * @param crowdPackDos 人群包对象列表
     */
    public void updateBatchById(List<CrowdPackDo> crowdPackDos) {
        DBUtil.updateBatchWithoutTx("crowd.updateByPrimaryKeySelective", crowdPackDos);
    }

    /**
     * 根据人群包id删除人群包
     *
     * @param crowdPackDo 人群包对象
     */
    public void deleteById(CrowdPackDo crowdPackDo) {
        DBUtil.delete("crowd.deleteByPrimaryKey", crowdPackDo.getId());
    }

    public CrowdPackDo selectByIdAndNotFlag(Long crowdId) {
        return DBUtil.selectOne("crowd.selectByIdAndNotFlag", crowdId);
    }

    public Page<CrowdPackDo> selectNoStrategyIdCrowdPack(CrowdListReq crowdListReq) {
        return DBUtil.selectPage("crowd.selectNoStrategyIdCrowdPack", crowdListReq, crowdListReq.getBeginNum(), crowdListReq.getSize());
    }

    public List<CrowdPackDo> selectTodayByStatus(CrowdStatusEnum[] statusEnum) {
        return DBUtil.selectList("crowd.selectTodayByStatus",Arrays.stream(statusEnum).map(CrowdStatusEnum::getCode).collect(Collectors.toList()));
    }

    public List<CrowdPackDo> selectTodayReportCrowd() {
        return DBUtil.selectList("crowd.selectTodayReportCrowd",null);
    }

    public Page<CrowdPackDo> queryPageByLabelId(AssociatedCrowdsReq associatedCrowdsReq) {
        return DBUtil.selectPage("crowd.queryPageByLabelId", associatedCrowdsReq, associatedCrowdsReq.getBeginNum(), associatedCrowdsReq.getSize());
    }

    public Integer querySizeByLabelId(Long labelId) {
        return DBUtil.selectOne("crowd.querySizeByLabelId", labelId);
    }

    public List<CrowdPackDo> selectBigDataErrorResultCrowdPacks(String date) {
        return DBUtil.selectList("crowd.selectBigDataErrorResultCrowdPacks",date);
    }

    public int updateBigDataErrorResultCrowdPacks(Set<Long> crowdIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("crowdIds", crowdIds.stream().map(String::valueOf).toArray(String[]::new));
        return DBUtil.update("crowd.updateBigDataErrorResultCrowdPacks", params);
    }

}
