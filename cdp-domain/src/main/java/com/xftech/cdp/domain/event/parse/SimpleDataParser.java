package com.xftech.cdp.domain.event.parse;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.xftech.cdp.domain.event.model.annotation.Parser;
import com.xftech.cdp.domain.event.model.constants.EventConfigConstants;
import com.xftech.cdp.domain.event.model.dto.FieldDetail;
import com.xftech.cdp.domain.event.model.dto.MqEventFieldMappingConfig;
import com.xftech.cdp.domain.event.model.enums.DataParserEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ SimpleDataParser, v 0.1 2024/11/15 14:56 snail Exp $
 */
@Component
@Parser(parser = DataParserEnum.SIMPLE)
public class SimpleDataParser extends AbstractDataParser {

    /**
     * 跟进配置的信息解决消息体
     * @param content 消息体内容
     * @param config 参数解析配置信息
     * @return 解析后的字段信息
     */
    @Override
    public Map<String, Object> doParse(String content, MqEventFieldMappingConfig config) {
        List<FieldDetail> fieldDetails = config.getFieldList();
        if(CollectionUtils.isEmpty(fieldDetails) || StringUtils.isEmpty(content)){
            return Collections.emptyMap();
        }

        JSONObject contentObj = JSONObject.parse(content);

        Map<String,Object> result = Maps.newHashMapWithExpectedSize(fieldDetails.size());
        for (FieldDetail detail : fieldDetails){
            Object value = getFieldValue(detail.getOriginField(),contentObj);
            result.put(detail.getTargetField(),value);
        }

        return result;
    }

    /**
     * 获取某个配置原始字段对应对的值信息
     * @param originKey 原始字段配置信息
     * @param msg 消息体
     * @return 解析消息体之后的原始字段的信息
     */
    private Object getFieldValue(String originKey, JSONObject msg){
        if(isSingleField(originKey)){
            return getFirstLevelValue(originKey,msg);
        }

        return getOtherLevelValue(originKey,msg);
    }

    /**
     * 获取非第一层配置的属性值
     * @param originKey 配置的原始属性名称
     * @param msg 消息体信息
     * @return 配置的属性对应的值
     */
    private Object getOtherLevelValue(String originKey, JSONObject msg){
        String[] keyArray = getKeyArray(originKey);

        JSONObject curItem = null;
        for (int i=0;i<keyArray.length-1;i++){
            curItem = msg.getJSONObject(keyArray[i]);
            if(Objects.isNull(curItem)){
                break;
            }
        }
        if(Objects.isNull(curItem)){
            return null;
        }

        return curItem.get(keyArray[keyArray.length-1]);
    }

    /**
     * 获取第一层配置的属性值
     * @param originKey 配置的原始属性名称
     * @param msg 消息体信息
     * @return 配置的属性对应的值
     */
    private Object getFirstLevelValue(String originKey, JSONObject msg){
        return msg.get(originKey);
    }

    /**
     * 判断配置的原始字段是否为第一层的某个属性，如user.name为第二层
     * @param originKey 原始字段配置信息
     * @return 是否为第一层属性配置
     */
    private boolean isSingleField(String originKey){
        return !originKey.contains(EventConfigConstants.FIELD_JOIN_CHAR);
    }

    /**
     * 消息体参数解析的配置key解析，如ext.amount
     * @param originKey 原始参数解析配置信息
     * @return 参数字段解析后的数组信息
     */
    private String[] getKeyArray(String originKey){
        return StringUtils.split(originKey,EventConfigConstants.FIELD_JOIN_CHAR);
    }
}
