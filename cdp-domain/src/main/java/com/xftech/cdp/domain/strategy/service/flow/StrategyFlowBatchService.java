/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.flow;

import cn.hutool.core.thread.ThreadUtil;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowBatchRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecCycleCounterDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowBatchDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowBatchService, v 0.1 2023/12/21 17:23 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class StrategyFlowBatchService {
    private final RedisUtils redisUtils;
    private final StrategyFlowBatchRepository flowBatchRepository;

    public void insert(StrategyFlowBatchDo strategyFlowBatchDo) {
        String lockKey = String.format("lockStrategyFlowBatchInsert:%s", strategyFlowBatchDo.getFlowNo());
        for (; ; ) {
            boolean ret = redisUtils.lock(lockKey, "1", 200);
            if (ret) {
                StrategyFlowBatchDo batch = selectByDateValue(strategyFlowBatchDo.getFlowNo(), strategyFlowBatchDo.getDateValue());
                if (batch == null) {
                    flowBatchRepository.insert(strategyFlowBatchDo);
                }
                redisUtils.unlock(lockKey);
                break;
            }
            ThreadUtil.sleep(10);
        }
    }

    public StrategyFlowBatchDo selectByDateValue(String flowNo, Integer dateValue){
        return flowBatchRepository.selectByDateValue(flowNo, dateValue);
    }
    public StrategyFlowBatchDo select(String batchNo) {
        return flowBatchRepository.selectByBatchNo(batchNo);
    }
}