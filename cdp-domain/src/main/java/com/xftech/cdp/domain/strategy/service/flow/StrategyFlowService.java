/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.flow;

import com.xftech.cdp.domain.strategy.model.strategy.FlowListQueryBO;
import com.xftech.cdp.domain.strategy.repository.StrategyFlowRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.w3c.dom.stylesheets.LinkStyle;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowService, v 0.1 2023/12/15 13:36 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class StrategyFlowService {
    private final StrategyFlowRepository strategyFlowRepository;

    public void insert(StrategyFlowDo strategyFlowDo) {
        strategyFlowRepository.insert(strategyFlowDo);
    }

    public void updateSelective(StrategyFlowDo strategyFlowDo) {
        strategyFlowRepository.updateSelective(strategyFlowDo);
    }

    public StrategyFlowDo select(String flowNo) {
        if (StringUtils.isEmpty(flowNo)){
            return null;
        }
        return strategyFlowRepository.select(flowNo);
    }

    public List<StrategyFlowDo> selectList(FlowListQueryBO flowListQueryBO) {
        // 当前数据量不大，暂且全量查询
        return strategyFlowRepository.selectList(flowListQueryBO);
    }

    public List<StrategyFlowDo> selectList(List<Integer> statusList){
        return strategyFlowRepository.selectList(statusList);
    }

    public int updateStatus(Long id, Integer fromStatus, Integer toStatus) {
        return strategyFlowRepository.updateStatus(id, fromStatus, toStatus);
    }
}