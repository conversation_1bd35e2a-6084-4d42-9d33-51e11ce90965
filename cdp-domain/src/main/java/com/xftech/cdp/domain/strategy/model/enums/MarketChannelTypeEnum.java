package com.xftech.cdp.domain.strategy.model.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
public enum MarketChannelTypeEnum {
    BATCH_SMS(1, "短信无参"),
    BATCH_SMS_PARAM(2, "短信带参"),
    BATCH_VOICE(3, "电销"),
    BATCH_SALE_TICKET(4, "优惠券"),
    BATCH_NEW_VOICE(5, "新电销"),

    BATCH_PUSH(6, "push无参"),
    BATCH_PUSH_PARAM(7, "push有参"),

    AI_PRONTO(8, "AI-即时触达"),
    INCREASE_AMT(200, "提额"),

    X_DAY_INTEREST_FREE(201, "X天还款免息"),
    LIFE_RIGHTS(202, "生活权益"),
    API_OPEN_AMOUNT(203, "api放开额度"),
    ;
    private final Integer code;
    private final String description;

    MarketChannelTypeEnum(Integer code, String description) {
        this.code = code;
        this.description = description;
    }

    public static MarketChannelTypeEnum getByCode(Integer code) {
        return Arrays.stream(values()).filter(x -> Objects.equals(x.code, code))
                .findFirst().orElse(null);
    }

    public Integer getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
}
