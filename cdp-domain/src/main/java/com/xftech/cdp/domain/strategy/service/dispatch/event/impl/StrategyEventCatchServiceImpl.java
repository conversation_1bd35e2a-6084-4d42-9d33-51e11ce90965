package com.xftech.cdp.domain.strategy.service.dispatch.event.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.domain.strategy.model.enums.StrategyInstantLabelTypeEnum;
import com.xftech.cdp.domain.strategy.repository.EventMetaDataRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyInstantLabelRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventCatchService;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.constant.RedisKeyConstants;
import com.xftech.cdp.infra.repository.cdp.strategy.po.EventMetaDataDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyInstantLabelDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
@Slf4j
@Service
public class StrategyEventCatchServiceImpl implements StrategyEventCatchService {

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private DingTalkConfig dingTalkConfig;
    @Autowired
    private StrategyRepository strategyRepository;
    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;
    @Autowired
    private EventMetaDataRepository eventMetaDataRepository;
    @Autowired
    private StrategyInstantLabelRepository strategyInstantLabelRepository;


    /**
     * 项目启动时，设置事件触发时间
     */
    @Override
    public void initEventTriggerTime() {
        try {
            List<StrategyDo> list = strategyRepository.getExecutingEventStrategy();
            for (StrategyDo strategyDo : list) {
                List<StrategyMarketEventDo> strategyMarketEventDo = strategyMarketEventRepository.selectByStrategyId(strategyDo.getId());
                if (!CollectionUtils.isEmpty(strategyMarketEventDo)) {
                    strategyMarketEventDo.forEach(s->{
                        String key = String.format(RedisKeyConstants.STRATEGY_EVENT_TRIGGER_TIME, s.getEventName(), strategyDo.getId());
                        if (!redisUtils.hasKey(key)) {
                            this.resetEventTriggerTime(s.getEventName(), strategyDo);
                        }
                    });

                }
            }
        } catch (Exception e) {
            log.warn("重置事件策略触发时间异常", e);
        }
    }

    /**
     * 重置事件触发事件
     *
     * @param strategy 策略
     */
    @Override
    public void resetEventTriggerTime(String eventName, StrategyDo strategy) {
        String key = String.format(RedisKeyConstants.STRATEGY_EVENT_TRIGGER_TIME, eventName, strategy.getId());
        redisUtils.set(key, LocalDateTimeUtil.format(LocalDateTime.now(), TimeFormat.DATE_TIME), getSeconds(strategy.getValidityEnd()));
    }

    /**
     * 策略30分钟无事件告警
     *
     * @param strategy 策略
     */
    @Override
    public void noEventAlarm(String eventName, StrategyDo strategy) {
        try {
            String alarmFlagKey = String.format(RedisKeyConstants.STRATEGY_EVENT_TRIGGER_TIME_ALARM, strategy.getId());
            if (redisUtils.hasKey(alarmFlagKey)) {
                return;
            }

            String key = String.format(RedisKeyConstants.STRATEGY_EVENT_TRIGGER_TIME, eventName, strategy.getId());
            if (redisUtils.hasKey(key)) {
                String lastTime = redisUtils.get(key);
                if (StringUtils.isBlank(lastTime)) {
                    return;
                }

                long interval = Duration.between(LocalDateTimeUtil.parse(lastTime, TimeFormat.DATE_TIME), LocalDateTime.now()).toMinutes();
                if (interval >= 30) {
                    List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
                    atMobileList.add(strategy.getUpdatedOpMobile());
                    strategy.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, "今日已执行30分钟，无触发事件，请确认事件是否正常", null);
                    redisUtils.set(alarmFlagKey, strategy.getId(), getSeconds(LocalDateTime.of(LocalDate.now(), LocalTime.MAX)));
                }
            }
        } catch (Exception e) {
            log.warn("策略30分钟无事件告警异常", e);
        }
    }

    /**
     * 设置有事件但没有符合条的用户的标识
     *
     * @param eventName  事件名称
     * @param strategyId 策略ID
     */
    @Override
    public void hasEventButNoMatchFlag(String eventName, Long strategyId) {
        String key = String.format(RedisKeyConstants.STRATEGY_EVENT_30MIN_HAS_BUT_NOMATCH, eventName, strategyId);
        if (!redisUtils.hasKey(key)) {
            LocalDateTime startTime = LocalDateTime.now();
            LocalDateTime endTime = startTime.plusDays(1).with(LocalTime.of(23, 59, 59));
            redisUtils.set(key, LocalDateTimeUtil.format(startTime, TimeFormat.DATE_TIME), Duration.between(startTime, endTime).getSeconds());
        }
    }

    /**
     * 删除有事件但没有符合条的用户的标识
     *
     * @param eventName  事件名称
     * @param strategyId 策略ID
     */
    @Override
    public void removeHasEventButNoMatchFlag(String eventName, Long strategyId) {
        String key = String.format(RedisKeyConstants.STRATEGY_EVENT_30MIN_HAS_BUT_NOMATCH, eventName, strategyId);
        if (redisUtils.hasKey(key)) {
            redisUtils.delete(key);
        }
    }

    @Override
    public void hasEventButNoMatchAlarm(String eventName, StrategyDo strategy) {
        try {
            String alarmFlagKey = String.format(RedisKeyConstants.STRATEGY_EVENT_HAS_BUT_NOMATCH_ALARM, strategy.getId());
            if (redisUtils.hasKey(alarmFlagKey)) {
                return;
            }

            String key = String.format(RedisKeyConstants.STRATEGY_EVENT_30MIN_HAS_BUT_NOMATCH, eventName, strategy.getId());
            if (redisUtils.hasKey(key)) {
                String lastTime = redisUtils.get(key);
                if (StringUtils.isBlank(lastTime)) {
                    return;
                }

                long interval = Duration.between(LocalDateTimeUtil.parse(lastTime, TimeFormat.DATE_TIME), LocalDateTime.now()).toMinutes();
                if (interval >= 30) {
                    List<String> atMobileList = new ArrayList<>(dingTalkConfig.atMobileList());
                    atMobileList.add(strategy.getUpdatedOpMobile());
                    strategy.alarmDingTalk(dingTalkConfig.getAlarmUrl(), atMobileList, "今日已执行30分钟，有触发事件，但无符合筛选条件的用户", null);
                    redisUtils.set(alarmFlagKey, strategy.getId(), getSeconds(LocalDateTime.of(LocalDate.now(), LocalTime.MAX)));
                }
            }
        } catch (Exception e) {
            log.warn("有触发事件，但无符合筛选条件的用户告警异常", e);
        }
    }

    /**
     * 获取时间区间
     *
     * @param endTime 结束时间
     * @return 时间区间
     */
    private Long getSeconds(LocalDateTime endTime) {
        return Duration.between(LocalDateTime.now(), endTime).getSeconds();
    }

    /**
     * 缓存元数据
     */
    @Override
    public void catchMetaData() {
        try {
            List<StrategyInstantLabelDo> strategyInstantLabelDoList = strategyInstantLabelRepository.selectCacheInfo();
            strategyInstantLabelDoList.forEach(item -> {
                StrategyInstantLabelTypeEnum labelTypeEnum = StrategyInstantLabelTypeEnum.getInstance(item.getLabelType());
                //标签
                String key;
                if (StrategyInstantLabelTypeEnum.LABEL.getType().equals(labelTypeEnum.getType())) {
                    key = String.format(RedisKeyConstants.STRATEGY_INSTANT_LABEL_STRATEGY, item.getStrategyType(), item.getLabelName());
                } else {
                    key = String.format(RedisKeyConstants.STRATEGY_INSTANT_LABEL_MSG, item.getLabelName());
                }
                redisUtils.set(key, JSON.toJSONString(item), RedisUtils.DEFAULT_EXPIRE_HOUR);
            });

            List<EventMetaDataDo> eventMetaDataList = eventMetaDataRepository.selectCacheInfo();
            eventMetaDataList.forEach(item -> redisUtils.hPut(RedisKeyConstants.STRATEGY_EVENT_META_DATA, item.getEventName(), JSON.toJSONString(item), RedisUtils.DEFAULT_EXPIRE_HOUR));

        } catch (Exception e) {
            log.info("加载标签缓存数据失败");
        }
    }
}
