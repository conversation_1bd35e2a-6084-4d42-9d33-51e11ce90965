package com.xftech.cdp.domain.strategy.factory;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.infra.exception.BizException;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class MonitorListFactory {

//    private MonitorListFactory() {
//
//    }

    public AbsMonitorListRespFinal createOpt(Integer marketChannel) {
        if (marketChannel.equals(StrategyMarketChannelEnum.SMS.getCode())) {
            return new MonitorListSmsResp();
        } else if (marketChannel.equals(StrategyMarketChannelEnum.VOICE.getCode())) {
            return new MonitorListVoiceResp();
        } else if (marketChannel.equals(StrategyMarketChannelEnum.SALE_TICKET.getCode())) {
            return new MonitorListCouponRespImpl();
        } else if (marketChannel.equals(StrategyMarketChannelEnum.VOICE_NEW.getCode())) {
            return new MonitorListNewVoiceResp();
        } else if (marketChannel.equals(StrategyMarketChannelEnum.PUSH.getCode())) {
            return new MonitorListPushResp();
        } else {
            throw new BizException("渠道类型错误");
        }
    }
}
