package com.xftech.cdp.domain.stat.repository;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity;
import com.xftech.cdp.infra.rabbitmq.enums.DecisionResultEnum;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 离线策略决策记录表
 */
@Component
public class OfflineDecisionRecordDao {


    public void saveOfflineDecisionRecord(List<OfflineDecisionRecordEntity> list) {
        DBUtil.insertBatch("offlineDecisionRecord.saveOfflineDecisionRecord", list);
    }

    public void saveOfflineDecisionRecordWithoutTx(List<OfflineDecisionRecordEntity> list) {
        DBUtil.insertBatchWithoutTx("offlineDecisionRecord.saveOfflineDecisionRecord", list);
    }

    public Integer countLabelNum(String tableNameNo, Long strategyId, List<Long> strategyChannelIds, String startDate, String endDate, DecisionResultEnum decisionResultEnum) {
        Map<String, Object> param = new HashMap<>();
        param.put("tableName", getTableName(tableNameNo));
        param.put("strategyId", strategyId);
        param.put("strategyChannelIds", strategyChannelIds);
        param.put("startDate", startDate);
        param.put("endDate", endDate);
        param.put("failCode", decisionResultEnum.getFailCode());
        return DBUtil.selectOne("offlineDecisionRecord.countLabelNum", param);
    }

    public String getTableName(String tableNameNo) {
        return "offline_decision_record_" + tableNameNo;
    }
}
