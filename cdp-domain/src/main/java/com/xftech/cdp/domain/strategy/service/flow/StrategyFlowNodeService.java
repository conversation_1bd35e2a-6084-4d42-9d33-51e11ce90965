/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.domain.strategy.service.flow;

import com.xftech.cdp.domain.strategy.repository.StrategyFlowNodeRepository;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyFlowNodeDo;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ StrategyFlowNodeService, v 0.1 2023/12/19 15:32 yye.xu Exp $
 */

@Service
@AllArgsConstructor
public class StrategyFlowNodeService {
    private final StrategyFlowNodeRepository strategyFlowNodeRepository;
    public void batchInsert(List<StrategyFlowNodeDo> strategyFlowNodes) {
        strategyFlowNodeRepository.batchInsert(strategyFlowNodes);
    }
    public List<StrategyFlowNodeDo> select(String flowNo) {
        if (StringUtils.isEmpty(flowNo)){
            return new ArrayList<>(0);
        }
        return strategyFlowNodeRepository.select(flowNo);
    }

    public void deleteByFlowNo(String flowNo) {
        strategyFlowNodeRepository.deleteByFlowNo(flowNo);
    }
}