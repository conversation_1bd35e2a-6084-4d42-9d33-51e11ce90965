/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.oss.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version $ OssContentResponse, v 0.1 2025/5/9 15:37 xu.fan Exp $
 */
@Data
public class OssContentResponse {

    private boolean isSuccess;

    private String errorMsg;

    private String bucketName;

    private List<String> contentList;

    private List<String> columnNames;

    public OssContentResponse() {}

    public OssContentResponse(boolean isSuccess, String errorMsg) {
        this.isSuccess = isSuccess;
        this.errorMsg = errorMsg;
    }
}
