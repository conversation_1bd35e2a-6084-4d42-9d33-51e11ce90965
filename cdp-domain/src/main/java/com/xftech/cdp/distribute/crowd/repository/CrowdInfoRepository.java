/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */
package com.xftech.cdp.distribute.crowd.repository;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoDo;

import com.google.common.collect.Maps;
import org.springframework.stereotype.Repository;

@Repository
public class CrowdInfoRepository {

    public int insert(CrowdInfoDo crowdInfoDo) {
        return DBUtil.insert("crowdInfoMapper.insert", crowdInfoDo);
    }

    public int insertSelective(CrowdInfoDo crowdInfoDo) {
        return DBUtil.insert("crowdInfoMapper.insertSelective", crowdInfoDo);
    }

    public void insertBatch(List<CrowdInfoDo> crowdInfoDoList) {
        DBUtil.insertBatch("crowdInfoMapper.insertSelective", crowdInfoDoList);
    }

    public int updateByPrimaryKey(CrowdInfoDo crowdInfoDo) {
        if (crowdInfoDo == null || crowdInfoDo.getId() == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        return DBUtil.update("crowdInfoMapper.updateByPrimaryKey", crowdInfoDo);
    }

    public int updateByPrimaryKeySelective(CrowdInfoDo crowdInfoDo) {
        return DBUtil.update("crowdInfoMapper.updateByPrimaryKeySelective", crowdInfoDo);
    }

    public CrowdInfoDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("crowdInfoMapper.selectByPrimaryKey", id);
    }

    public void deleteByPrimaryKey(Long id) {
        DBUtil.delete("crowdInfoMapper.deleteByPrimaryKey", id);
    }

    /**
     * 更新人群包信息
     *
     * @param crowdId       人群包ID
     * @param crowdStatus   人群包状态
     * @param validityBegin 生效时间-开始
     * @param validityEnd   生效时间-结束
     * @param isPermanent   是否永久有效
     * @return
     */
    public int updateCrowdStatusByCrowdId(Long crowdId, Integer crowdStatus,
                                          Date validityBegin, Date validityEnd, Integer isPermanent) {
        if (crowdId == null || crowdStatus == null) {
            return 0;
        }
        Map<String, Object> params = Maps.newHashMap();
        params.put("validityBegin", validityBegin);
        params.put("validityEnd", validityEnd);
        params.put("isPermanent", isPermanent);
        params.put("crowdStatus", crowdStatus);
        params.put("crowdId", crowdId);
        return DBUtil.update("crowdInfoMapper.updateCrowdStatusByCrowdId", params);
    }

    /**
     * 查询所有人群包
     *
     * @return
     */
    public List<CrowdInfoDo> selectAll() {
        return DBUtil.selectList("crowdInfoMapper.selectAll", Maps.newHashMap());
    }

    /**
     * 查询所有上线人群包
     *
     * @return
     */
    public List<CrowdInfoDo> selectAllOnline() {
        return DBUtil.selectList("crowdInfoMapper.selectAllOnline", Maps.newHashMap());
    }

    /**
     * 根据人群ID查询人群包
     *
     * @param crowdId 人群包ID
     * @return
     */
    public CrowdInfoDo selectByCrowdId(Long crowdId) {
        return DBUtil.selectOne("crowdInfoMapper.selectByCrowdId", crowdId);
    }

    public CrowdInfoDo selectValidCrowdByCrowdId(Long crowdId) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("crowdId", crowdId);
        params.put("curDate", LocalDateTime.now());
        return DBUtil.selectOne("crowdInfoMapper.selectValidCrowdByCrowdId", params);
    }

}
