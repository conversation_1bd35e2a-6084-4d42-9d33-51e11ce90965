/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.distribute.offline.xxljob;

import com.google.common.base.Stopwatch;
import com.xftech.cdp.distribute.offline.StrategyTaskDistributeHandler;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version $ OfflineStrategyJobDispatch, v 0.1 2025/4/27 16:33 xu.fan Exp $
 */
@Slf4j
@Component
public class OfflineStrategyJobDispatch {

    @Autowired
    private StrategyTaskDistributeHandler strategyTaskDistributeHandler;

    /**
     * 到达任务执行时间，生成策略分片执行任务List
     * 调度到单机执行
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.OFFLINE_ENGINE_DISTRIBUTE_TASK_GENERATE)
    public ReturnT<String> strategySliceTask(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        XxlJobLogger.log("Xxl-Job 分布式策略生成下发任务开始执行, param={}", param);
        strategyTaskDistributeHandler.generateDispatchTask();
        stopwatch.stop();
        XxlJobLogger.log("Xxl-Job 分布式策略生成下发任务结束, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 分布式执行策略分片
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.OFFLINE_ENGINE_DISTRIBUTE_EXECUTE)
    public ReturnT<String> strategySliceTaskExecute(String param) {

        Stopwatch stopwatch = Stopwatch.createStarted();
        int total = ShardingUtil.getShardingVo().getTotal();
        int index = ShardingUtil.getShardingVo().getIndex();
        log.info("Xxl-Job 分布式策略执行下发任务开始执行, 总分片数:{}, 当前分片数:{}", total, index);
        strategyTaskDistributeHandler.execDistributeSliceTask(total, index);
        stopwatch.stop();
        log.info("Xxl-Job 分布式策略执行下发任务结束执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }
}
