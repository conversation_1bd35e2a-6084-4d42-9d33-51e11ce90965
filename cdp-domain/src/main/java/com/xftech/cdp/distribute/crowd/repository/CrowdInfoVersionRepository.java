/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */
package com.xftech.cdp.distribute.crowd.repository;

import java.util.List;

import com.xftech.base.common.util.DBUtil;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo;

import org.springframework.stereotype.Repository;

@Repository
public class CrowdInfoVersionRepository {

    public int insert(CrowdInfoVersionDo crowdInfoVersionDo) {
        return DBUtil.insert("crowdInfoVersionMapper.insert", crowdInfoVersionDo);
    }

    public int insertSelective(CrowdInfoVersionDo crowdInfoVersionDo) {
        return DBUtil.insert("crowdInfoVersionMapper.insertSelective", crowdInfoVersionDo);
    }

    public void insertBatch(List<CrowdInfoVersionDo> crowdInfoDoList) {
        DBUtil.insertBatch("crowdInfoVersionMapper.insertSelective", crowdInfoDoList);
    }

    public int updateByPrimaryKey(CrowdInfoVersionDo crowdInfoVersionDo) {
        if (crowdInfoVersionDo == null || crowdInfoVersionDo.getId() == null) {
            throw new IllegalArgumentException("id cannot be null");
        }
        return DBUtil.update("crowdInfoVersionMapper.updateByPrimaryKey", crowdInfoVersionDo);
    }

    public int updateByPrimaryKeySelective(CrowdInfoVersionDo crowdInfoVersionDo) {
        return DBUtil.update("crowdInfoVersionMapper.updateByPrimaryKeySelective", crowdInfoVersionDo);
    }

    public CrowdInfoVersionDo selectByPrimaryKey(Long id) {
        return DBUtil.selectOne("crowdInfoVersionMapper.selectByPrimaryKey", id);
    }

    public void deleteByPrimaryKey(Long id) {
        DBUtil.delete("crowdInfoVersionMapper.deleteByPrimaryKey", id);
    }

    /**
     * 查询最新的已分片版本
     *
     * @param crowdId 人群包ID
     * @return
     */
    public CrowdInfoVersionDo selectMaxSliceVersion(Long crowdId) {
        return DBUtil.selectOne("crowdInfoVersionMapper.selectMaxSliceVersion", crowdId);
    }

    /**
     * 查询最新的人群包版本
     *
     * @param crowdId 人群包ID
     * @return
     */
    public CrowdInfoVersionDo selectMaxCrowdVersion(Long crowdId) {
        return DBUtil.selectOne("crowdInfoVersionMapper.selectMaxCrowdVersion", crowdId);
    }

}
