package com.xftech.cdp.distribute.crowd.repository.model;

import java.util.Date;
import java.util.Objects;

import com.xftech.cdp.distribute.crowd.enums.CrowdEffectiveEnum;
import com.xftech.cdp.distribute.crowd.enums.CrowdStatusEnum;
import com.xftech.cdp.infra.utils.DateUtil;

import lombok.Data;

/**
 * 人群元数据表
 * crowd_info
 */
@Data
public class CrowdInfoDo {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 人群包id
     */
    private Long crowdId;

    /**
     * 人群包名称
     */
    private String crowdName;

    /**
     * 人群包描述
     */
    private String crowdDesc;

    /**
     * 生效周期-开始时间
     */
    private Date validityBegin;

    /**
     * 生效周期-结束时间
     */
    private Date validityEnd;

    /**
     * 是否永久有效:0否;1是(可忽略生效周期)
     */
    private Integer isPermanent;

    /**
     * 创建来源 1:规则创建;2:文件上传
     */
    private Integer crowdCreateType;

    /**
     * 人群分类
     */
    private String crowdCategory;

    /**
     * 状态 0:已删除;1:草稿中;2:审批中;3:已下线;4:已上线(不在有效期内,不会生成oss)
     */
    private Integer crowdStatus;

    /**
     * 是否删除:0未删除;1已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 修改时间
     */
    private Date updatedTime;

    public boolean isValid() {
        return Objects.equals(crowdStatus, CrowdStatusEnum.ONLINE.getStatus()) &&
                (Objects.equals(isPermanent, CrowdEffectiveEnum.PERMANENT.getStatus()) || (Objects.equals(isPermanent, CrowdEffectiveEnum.NO_PERMANENT.getStatus()) && DateUtil.isDateInRange(new Date(), validityBegin, validityEnd))) &&
                Objects.equals(dFlag, 0);
    }

}