/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.api.TestUtilApi;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo;
import com.xftech.cdp.distribute.offline.StrategyTaskDistributeHandler;
import com.xftech.cdp.distribute.offline.repository.StrategySliceExecLogRepository;
import com.xftech.cdp.distribute.oss.RangeReadFileService;
import com.xftech.cdp.distribute.oss.StrategyOssReaderService;
import com.xftech.cdp.distribute.oss.dto.OssContentResponse;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.crowd.model.enums.SliceExecLogEnum;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.param.service.TemplateParamService;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.infra.annotation.ApiNoSigner;
import com.xftech.cdp.infra.client.ads.model.req.label.AdbRealTimeReq;
import com.xftech.cdp.infra.client.ads.model.resp.AdbRealTimeResp;
import com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdDetailDo;
import com.xftech.cdp.infra.repository.cdp.slice.po.StrategySliceExecLogDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.xftech.cdp.infra.utils.PageUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @version $ TestUtilController, v 0.1 2023/10/11 11:49 wancheng.qu Exp $
 */
@AllArgsConstructor
@RestController
@Slf4j
public class TestUtilController implements TestUtilApi {

    @Autowired
    private TemplateParamService templateParamService;

    @Autowired
    private CrowdPackService crowdPackService;

    @Autowired
    private ModelPlatformService modelPlatformService;

    @Autowired
    private SerialNumberUtil serialNumberUtil;

    @Autowired
    private StrategyTaskDistributeHandler strategyTaskDistributorHandler;

    @Autowired
    private StrategyRepository strategyRepository;

    @Autowired
    private StrategyOssReaderService strategyOssReaderService;

    @Autowired
    private RangeReadFileService rangeReadFileService;

    @Autowired
    private StrategySliceExecLogRepository strategySliceExecLogRepository;

    @Autowired
    private StrategyMarketEventRepository strategyMarketEventRepository;


    @Override
    public Response<String> testConfig(String type, List<String> name) throws InterruptedException {
        return Response.success(JsonUtil.toJson(templateParamService.testConfig(type, name)));
    }

    @Override
    public Response<String> operateSql() {
        return Response.success(crowdPackService.operateSql());
    }

    // TODO 特征测试查询接口
    @ApiNoSigner
    @Override
    public Response<String> labelQueryAll(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo) {

        if (StringUtils.isEmpty(labels)) {
            log.info("TestUtilController labelQueryAll labels is empty");
        }

        String[] labelArray = StringUtils.split(labels, ",");
        List<String> labelList = Arrays.asList(labelArray);

        AdbRealTimeReq adbRealTimeReq = new AdbRealTimeReq();
        adbRealTimeReq.setVarCodes(labelList);
        String requestId = serialNumberUtil.batchNum();
        adbRealTimeReq.setRequestId(requestId);
        adbRealTimeReq.setRequestGroup("xyf-cdp");
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("requestId", requestId);
        inputParams.put("timestamp", new Date().getTime());
        inputParams.put("app_user_id", userNo);
        adbRealTimeReq.setInputParams(inputParams);

        log.info("TestUtilController labelQueryAll adbRealTimeReq:{} ", JSON.toJSONString(adbRealTimeReq));

        AdbRealTimeResp resp = modelPlatformService.getAdbRealTime(adbRealTimeReq);
        return Response.success(JSON.toJSONString(resp));
    }

    @ApiNoSigner
    @Override
    public Response<String> labelQueryNew(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo) {

        if (StringUtils.isEmpty(labels)) {
            log.info("TestUtilController labelQueryNew labels is empty");
        }

        String[] labelArray = StringUtils.split(labels, ",");
        List<String> labelList = Arrays.asList(labelArray);

        AdbRealTimeReq adbRealTimeReq = new AdbRealTimeReq();
        adbRealTimeReq.setVarCodes(labelList);
        String requestId = serialNumberUtil.batchNum();
        adbRealTimeReq.setRequestId(requestId);
        adbRealTimeReq.setRequestGroup("xyf-cdp");
        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("timestamp", new Date().getTime());
        inputParams.put("app_user_id", userNo);
        adbRealTimeReq.setInputParams(inputParams);

        log.info("TestUtilController labelQueryNew adbRealTimeReq:{} ", JSON.toJSONString(adbRealTimeReq));

        FeatureQueryResponse featureResp = modelPlatformService.doCallDataFeature(adbRealTimeReq);
        log.info("TestUtilController labelQueryNew featureResp:{} ", JSON.toJSONString(featureResp));

        AdbRealTimeResp response = modelPlatformService.transformFeatureResponse(featureResp);

        return Response.success(JSON.toJSONString(response));
    }

    @ApiNoSigner
    @Override
    public Response<String> labelQueryOld(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo) {

        if (StringUtils.isEmpty(labels)) {
            log.info("TestUtilController labelQueryOld labels is empty");
        }

        String[] labelArray = StringUtils.split(labels, ",");
        List<String> labelList = Arrays.asList(labelArray);

        AdbRealTimeReq adbRealTimeReq = new AdbRealTimeReq();
        adbRealTimeReq.setVarCodes(labelList);
        String requestId = serialNumberUtil.batchNum();
        adbRealTimeReq.setRequestId(requestId);
        adbRealTimeReq.setRequestGroup("xyf-cdp");

        Map<String, Object> inputParams = new HashMap<>();
        inputParams.put("timestamp", new Date().getTime());
        inputParams.put("app_user_id", userNo);
        adbRealTimeReq.setInputParams(inputParams);

        log.info("TestUtilController labelQueryOld adbRealTimeReq:{} ", JSON.toJSONString(adbRealTimeReq));

        AdbRealTimeResp resp = modelPlatformService.doGetAdbRealTime(adbRealTimeReq);
        return Response.success(JSON.toJSONString(resp));
    }

    @ApiNoSigner
    @Override
    public Response<String> sliceTaskWrite(@RequestParam("strategyId") Long strategyId) {
        StrategyDo strategyDo = strategyRepository.selectById(strategyId);
        strategyTaskDistributorHandler.createStrategyDistributeSliceTasks(strategyDo);
        return Response.success(JSON.toJSONString(strategyDo));
    }


    @ApiNoSigner
    @Override
    public Response<String> sliceTaskExecute(@RequestParam("labels") String labels, @RequestParam("userNo") Long userNo) {
        // 写入 CrowdSliceExecLog表
        LocalDateTime start = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        LocalDateTime end = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        Map<String, Long> map = strategySliceExecLogRepository.selectStrategyCnt(111L,start,end,0L);

        log.info("TestUtilController sliceTaskExecute map:{}", JSON.toJSONString(map));

        strategyTaskDistributorHandler.execDistributeSliceTask(1,0);
        return Response.success("success");
    }

    @ApiNoSigner
    @Override
    public Response<String> readSliceFile(@RequestParam("crowdSliceId") Long crowdSliceId, @RequestParam("crowdId") Long crowdId) {
        StrategySliceExecLogDo strategyExecLogDo = new StrategySliceExecLogDo();
        strategyExecLogDo.setCrowdSliceId(crowdSliceId);
        strategyExecLogDo.setCrowdId(crowdId);
        List<CrowdDetailDo> crowdDetail = strategyOssReaderService.readBySliceExecLog(strategyExecLogDo);

        return Response.success(JSON.toJSONString(crowdDetail));
    }

    @ApiNoSigner
    @Override
    public Response<String> readSliceFileByDetail(@RequestParam("filePath") String filePath,
                                                  @RequestParam("startPos") Long startPos,
                                                  @RequestParam("endPos") Long endPos) {
//        StrategySliceExecLogDo strategyExecLogDo = new StrategySliceExecLogDo();
//        strategyExecLogDo.setCrowdSliceId(crowdSliceId);
//        strategyExecLogDo.setCrowdId(crowdId);
//        filePath = "procrowd/C_1_0_2025041711_9796/V1/20250611_1/proads_user_crowd_detail_782_V1_20250611_1_38.text";
//        startPos = 5242880L;
//        endPos = 14286492L;
        try {
            OssContentResponse crowdDetail = rangeReadFileService.readByRange(filePath, startPos, endPos);
            return Response.success(JSON.toJSONString(crowdDetail));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Response.fail("read file fail");

    }

    @ApiNoSigner
    @Override
    public Response<String> marketEventQuery(@RequestParam("bizEventType") String bizEventType) {
        List<StrategyMarketEventDo> marketEventList = strategyMarketEventRepository.getByEventName(bizEventType);
        return Response.success(JSON.toJSONString(marketEventList));
    }

}