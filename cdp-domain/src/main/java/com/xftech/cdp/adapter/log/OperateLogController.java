/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.log;

import com.xftech.cdp.api.OperateLogApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.log.QueryOperateLogReq;
import com.xftech.cdp.api.dto.resp.log.OperateLogResp;
import com.xftech.cdp.domain.strategy.service.impl.OperateLogService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ OperateLogController, v 0.1 2024/5/28 11:37 lingang.han Exp $
 */
@Slf4j
@RestController
@AllArgsConstructor
public class OperateLogController implements OperateLogApi {

    private final OperateLogService operateLogService;

    @Override
    public Response<PageResultResponse<OperateLogResp>> queryPage(QueryOperateLogReq queryOperateLogReq) {
        return Response.success(operateLogService.queryPage(queryOperateLogReq));
    }
}