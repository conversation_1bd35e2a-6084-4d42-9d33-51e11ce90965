package com.xftech.cdp.adapter.crowd;

import java.util.List;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import com.xftech.cdp.api.CrowdApi;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.external.ExternalBaseRequest;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.domain.crowd.service.CrowdPackService;
import com.xftech.cdp.domain.crowd.service.LabelService;
import com.xftech.cdp.infra.annotation.ApiSigner;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import com.xftech.cdp.infra.constant.SysConstants;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@RestController
public class CrowdController implements CrowdApi {

    public final CrowdPackService crowdPackService;
    public final LabelService labelService;

    public CrowdController(CrowdPackService crowdPackService, LabelService labelService) {
        this.crowdPackService = crowdPackService;
        this.labelService = labelService;
    }

    @Override
    @OperateLogAnnotation(description = "新增人群包", type = OperateTypeEnum.ADD, mode = OperateModeEnum.CROWD)
    public Response<Boolean> createByCondition(CrowdCreateReq crowdCreateReq) {
        return Response.success(crowdPackService.createByCondition(crowdCreateReq));
    }

    @Override
    @OperateLogAnnotation(description = "修改人群包", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.CROWD)
    public Response<Boolean> updateByCondition(CrowdUpdateReq crowdUpdateReq) {
        return Response.success(crowdPackService.updateByCondition(crowdUpdateReq));
    }

    @Override
    public Response<CrowdUploadResp> uploadExcel(MultipartFile file) {
        return Response.success(crowdPackService.uploadExcel(file));
    }

    @Override
    public Response<CrowdParseResp> parseExcel(CrowdDownLoadReq crowdDownLoadReq) {
        return Response.success(crowdPackService.parseExcel(crowdDownLoadReq));
    }
    /*
    @Override
    public Response<CrowdOneResp> createByExcel( MultipartFile file, String crowdName ) {
        return Response.success( crowdApplication.createByExcel( file, crowdName ) );
    }

    @Override
    public Response<CrowdOneResp> updateByExcel( MultipartFile file, Integer crowdId, String crowdName ) {
        return Response.success( crowdApplication.updateByExcel( file, crowdId, crowdName ) );
    }

    @Override
    public Response<CrowdParseResp> parseExcel( CrowdParseReq crowdParseReq ) {
        return Response.success( crowdApplication.parseExcel( crowdParseReq ) );
    }
     */

    @Override
    public void downloadFile(CrowdDownLoadReq crowdDownLoadReq, HttpServletResponse response) {
        crowdPackService.downLoadFile(crowdDownLoadReq.getUploadLogId(),response);
    }

    @Override
    public Response<CrowdReportDailyResp> reportDailyCrowdList() {
        return Response.success(crowdPackService.queryReportDailyList());
    }

    @Override
    public Response<PageResultResponse<CrowdRefreshInfoResp>> crowdRefreshInfo(CrowdRefreshInfoReq crowdRefreshInfo) {
        return Response.success(crowdPackService.crowdRefreshInfo(crowdRefreshInfo));
    }

    @Override
    public Response<Boolean> createCrowdByExcel(CrowdUploadReq crowdUploadReq) {
        return Response.success(crowdPackService.createCrowdByExcel(crowdUploadReq));
    }

    @Override
    public Response<Boolean> Duplicate(CrowdDuplicateReq crowdDuplicateReq) {
        return Response.success(crowdPackService.duplicate(crowdDuplicateReq.getCrowdId()));
    }

    @Override
    public Response<PageResultResponse<CrowdListResp>> list(CrowdListReq crowdListReq) {
        return Response.success(crowdPackService.queryList(crowdListReq));
    }

    @Override
    public Response<CrowdOneResp> getOne(CrowdOneReq crowdOneReq) {
        return Response.success(crowdPackService.getOne(crowdOneReq.getCrowdId()));
    }

    @Override
    @OperateLogAnnotation(description = "操作人群包", type = OperateTypeEnum.OPERATE, mode = OperateModeEnum.CROWD)
    public Response<Boolean> operate(CrowdOperateReq crowdOperateReq) {
        return Response.success(crowdPackService.operate(crowdOperateReq));
    }

    @Override
    @OperateLogAnnotation(description = "删除人群包", type = OperateTypeEnum.DELETE, mode = OperateModeEnum.CROWD)
    public Response<String> batchDelete(CrowdBatchDeleteReq crowdBatchDeleteReq) {
        return Response.success(null, crowdPackService.batchDelete(crowdBatchDeleteReq));
    }

    @Override
    public Response<List<CrowdLabelsResp>> getLabels(LabelReq labelReq) {
        return Response.success(labelService.getTree(labelReq));
    }

    /*@Override
    public Response<CrowdExecSqlResp> getExecSql(CrowdOneReq crowdOneReq) {
        return Response.success(crowdPackService.getExecSql(crowdOneReq.getCrowdId()));
    }*/

    @ApiSigner(ua = {SysConstants.SignerEnum.TEST})
    @Override
    public Response<Set<Long>> resetBigDataErrorResultCrowdPacks(@Validated @RequestBody ExternalBaseRequest req) {
        return Response.success(crowdPackService.resetBigDataErrorResultCrowdPacks());
    }

}
