package com.xftech.cdp.adapter.strategy;

import com.alibaba.fastjson.JSON;
import com.google.common.base.Stopwatch;
import com.xftech.cdp.api.dto.strategy.StrategyDispatchSmsRetryXxlJobParam;
import com.xftech.cdp.api.dto.strategy.StrategyXxlJobParam;
import com.xftech.cdp.application.StrategyHandler;
import com.xftech.cdp.domain.strategy.model.enums.DispatchTaskBizTypeEnum;
import com.xftech.cdp.infra.constant.XxlJobConstants;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import com.xxl.job.core.util.ShardingUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 2023/2/13
 */
@Slf4j
@Component
public class StrategyDispatch {

    @Autowired
    private StrategyHandler strategyHandler;

    // 生产任务，高频执行，只生成周期策略的任务、离线引擎任务
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH_TASK_GENERATOR)
    public ReturnT<String> strategyDispatchTaskProducer(String param) {
        log.info("Xxl-Job 策略执行任务生成器开始执行");
        Stopwatch stopwatch = Stopwatch.createStarted();
        strategyHandler.strategyDispatchTaskProducer();
        stopwatch.stop();
        log.info("Xxl-Job 策略执行任务生成器结束执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 消费任务 - 传入bizType类型
     * 101 --离线策略-任务执行器
     * 2 -- 离线引擎-策略执行器
     * 1 -- 周期策略-任务执行器
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH_TASK_EXECCUTE)
    public ReturnT<String> strategyDispatchTaskConsumer(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        int total = ShardingUtil.getShardingVo().getTotal();
        int index = ShardingUtil.getShardingVo().getIndex();
        int bizType = Integer.parseInt(param);
        log.info("Xxl-Job 策略执行下发任务开始执行, 总分片数:{}, 当前分片数:{}, biz_type:{}", total, index, DispatchTaskBizTypeEnum.getByCode(bizType));
        strategyHandler.strategyDispatchTaskConsumer(total, index, bizType);
        stopwatch.stop();
        log.info("Xxl-Job 策略执行下发任务结束执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 不营销专用下发方法
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH_OFFLINE_ENGINE)
    public ReturnT<String> strategyDispatchOfflineEngine(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("Xxl-Job 引擎策略执行下发任务开始执行");
        if (WhitelistSwitchUtil.boolSwitchByApollo("XxlJobTelStrategyDispatchDelayEnable")) {
            log.info("Xxl-Job 引擎策略执行下发任务开始执行, 单独处理none模式开启");
            strategyHandler.noneStrategyDispatchOfflineEngine();
        } else {
            strategyHandler.strategyDispatchOfflineEngine();
        }

        stopwatch.stop();
        log.info("Xxl-Job 引擎策略执行下发任务开始执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    /**
     * 电销专用下发方法
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE)
    public ReturnT<String> telStrategyDispatchOfflineEngine(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("Xxl-Job 单独电销引擎策略执行下发任务开始执行");

        strategyHandler.telStrategyDispatchOfflineEngine();
        stopwatch.stop();
        log.info("Xxl-Job 单独电销引擎策略执行下发任务开始执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));

        return ReturnT.SUCCESS;
    }

    /**
     * 非电销专用下发方法
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.NOT_TEL_STRATEGY_DISPATCH_OFFLINE_ENGINE)
    public ReturnT<String> notTelStrategyDispatchOfflineEngine(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("Xxl-Job 非电销引擎策略执行下发任务开始执行");

        strategyHandler.notTelStrategyDispatchOfflineEngine();
        stopwatch.stop();
        log.info("Xxl-Job 非电销引擎策略执行下发任务开始执行, 总计耗时:{}", stopwatch.elapsed(TimeUnit.SECONDS));

        return ReturnT.SUCCESS;
    }

    /**
     * 离线引擎-延迟决策专用下发方法
     * @param param
     * @return
     */
    @XxlJob(XxlJobConstants.RE_DECISION_STRATEGY_DISPATCH_OFFLINE_ENGINE)
    public ReturnT<String> ReDecisionStrategyDispatchOfflineEngine(String param) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        log.info("Xxl-Job 离线引擎-延迟决策-执行下发任务开始");
        strategyHandler.reDecisionStrategyDispatchOfflineEngine();
        log.info("Xxl-Job离线引擎-延迟决策-执行下发任务结束, 总计耗时:{}", stopwatch.stop().elapsed(TimeUnit.SECONDS));
        return ReturnT.SUCCESS;
    }

    // 任务长时间未成功告警
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH_TASK_EXECCUTE_TIMEOUT_ALARM)
    public ReturnT<String> strategyDispatchTaskExectuteTimeOutAlram(String param) {
        int delayMinutes = Integer.parseInt(Optional.ofNullable(param).orElse("30"));
        strategyHandler.strategyDispatchTaskExectuteAlarm(delayMinutes);
        return ReturnT.SUCCESS;
    }

    /**
     * 普通离线 策略执行入口
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH)
    public ReturnT<String> strategyDispatch(String param) {
        log.info("StrategyDispatch begin param:{}", param);
        XxlJobLogger.log("StrategyDispatch begin param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("StrategyDispatch param error param:{}", param);
            XxlJobLogger.log("StrategyDispatch param error param:{}", param);
            return ReturnT.FAIL;
        }
        try {
            StrategyXxlJobParam strategyXxlJobParam = JSON.parseObject(param, StrategyXxlJobParam.class);
            strategyHandler.execute(strategyXxlJobParam.getMarketChannelId(), null);
        } catch (Exception e) {
            log.warn("StrategyDispatch execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 策略重试
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_DISPATCH_RETRY)
    public ReturnT<String> strategyDispatchRetry(String param) {
        log.info("StrategyDispatchRetry begin param:{}", param);
        XxlJobLogger.log("StrategyDispatchRetry begin param:{}", param);
        if (StringUtils.isEmpty(param)) {
            log.warn("StrategyDispatchRetry param error param:{}", param);
            XxlJobLogger.log("StrategyDispatchRetry param error param:{}", param);
            return ReturnT.FAIL;
        }
        try {
            StrategyDispatchSmsRetryXxlJobParam strategyDispatchSmsRetryXxlJobParam = JSON.parseObject(param, StrategyDispatchSmsRetryXxlJobParam.class);
            strategyHandler.retry(strategyDispatchSmsRetryXxlJobParam.getStrategyExecLogId(), null);
        } catch (Exception e) {
            log.warn("StrategyDispatchRetry execute error param:{}, e: ", param, e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 刷新策略状态
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_REFRESH_STATUS)
    public ReturnT<String> strategyRefreshStatus(String param) {
        try {
            strategyHandler.refreshStatus();
        } catch (Exception e) {
            log.warn("strategyRefreshStatus execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 事件刷新策略状态
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_EVENT_REFRESH_STATUS)
    public ReturnT<String> strategyEventRefreshStatus(String param) {
        try {
            strategyHandler.strategyEventRefreshStatus();
        } catch (Exception e) {
            log.warn("strategyEventRefreshStatus execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 刷新策略状态
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_EVENT_30_MIN_ALARM)
    public ReturnT<String> strategyEvent30MinAlarm(String param) {
        try {
            strategyHandler.strategyEvent30MinAlarm();
        } catch (Exception e) {
            log.warn("strategyEvent30MinAlarm execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 当天结束后把日志状态修改为已结束
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_EVENT_REFRESH_EXEC_LOG_STATUS)
    public ReturnT<String> strategyEventRefreshExecLogStatus(String param) {
        try {
            strategyHandler.strategyEventRefreshExecLogStatus();
        } catch (Exception e) {
            log.warn("strategyEventRefreshExecLogStatus execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 缓存元数据
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_EVENT_CATCH_METADATA)
    public ReturnT<String> strategyEventCatchMetadata(String param) {
        try {
            strategyHandler.strategyEventCatchMetadata();
        } catch (Exception e) {
            log.warn("strategyEventCatchMetadata execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }


    /**
     * 策略执行日志刷新
     *
     * @param param 执行参数
     * @return 执行结果
     */
    @XxlJob(XxlJobConstants.STRATEGY_EVENT_EXEC_LOG_REFRESH)
    public ReturnT<String> strategyEventExecLogRefresh(String param) {
        try {
            strategyHandler.strategyEventExecLogRefresh(param);
        } catch (Exception e) {
            log.warn("strategyEventExecLogRefresh execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.STRATEGY_REFRESH_CACHE_DATA)
    public ReturnT<String> strategyCacheDataRefresh(String param) {
        try {
            strategyHandler.refreshCacheData();
        } catch (Exception e) {
            log.warn("strategyEventExecLogRefresh execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.T0_STRATEGY_DISPATCH_USER_NUM_ALARM)
    public ReturnT<String> t0StrategyDispatchUserNumAlarm(String param) {
        try {
            strategyHandler.t0StrategyDispatchUserNumAlarm();
        } catch (Exception e) {
            log.warn("t0StrategyDispatchUserNumAlarm execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.DISPATCH_FAIL_USER_UPDATE)
    public ReturnT<String> dispatchFailUserUpdate(String param) {
        try {
            strategyHandler.dispatchFailUserUpdate();
        } catch (Exception e) {
            log.warn("dispatchFailUserUpdate execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    @XxlJob(XxlJobConstants.EXIST_STRATEGY_FLOW_CTRL_UPDATE)
    public ReturnT<String> existStrategyFlowCtrlUpdate(String param) {
        try {
            strategyHandler.existStrategyFlowCtrlUpdate();
        } catch (Exception e) {
            log.warn("existStrategyFlowCtrlUpdate execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }

        return ReturnT.SUCCESS;
    }

    /**
     * 离线策略要用的人群包未更新完报警
     */
    @XxlJob(XxlJobConstants.OFFLINE_STRATEGY_CROWD_STATUS_ALARM)
    public ReturnT<String> offlineStrategyCrowdStatusAlarm(String param) {
        try {
            strategyHandler.offlineStrategyCrowdStatusAlarm();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("offlineStrategyCrowdStatusAlarm execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 策略的人群包过期告警
     * 策略过期告警排除失效策略  检查时间前置 2025-03-09
     */
    @XxlJob(XxlJobConstants.STRATEGY_CROWD_PACK_EXPIRE_ALARM)
    public ReturnT<String> strategyCrowdPackExpireAlarm(String param) {
        try {
            strategyHandler.strategyCrowdPackExpireAlarm();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("strategyCrowdPackExpireAlarm execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    // TODO 6点人群包刷新状态结果推送
    // TODO 20点当日新增策略，人群，策略变更推送 -- mail
    // TODO 每小时策略执行状态报表统计  -- mail


    /**
     * 离线策略执行结果统计报表
     */
    @XxlJob(XxlJobConstants.REPORT_DAILY_STRATEGY)
    public ReturnT<String> reportDailyStrategyJob(String param){
        try {
            strategyHandler.reportDailyStrategyExecute();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("reportDailyStrategyJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }

    /**
     * 离线策略执行结果告警
     */
    @XxlJob(XxlJobConstants.REPORT_DAILY_STRATEGY_ALARM)
    public ReturnT<String> reportDailyStrategyAlarmJob(String param){
        try {
            strategyHandler.reportDailyStrategyAlarm();
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.warn("reportDailyStrategyAlarmJob execute error, e: ", e);
            XxlJobLogger.log(e);
            return ReturnT.FAIL;
        }
    }
}
