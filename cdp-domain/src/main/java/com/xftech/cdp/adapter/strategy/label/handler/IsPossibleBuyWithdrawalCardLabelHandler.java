package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.adapter.strategy.label.request.IsPossibleBuyWithdrawalCardRequest;
import com.xftech.cdp.adapter.strategy.label.request.IsPossibleBuyWithdrawalCardResponse;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;


/**
 * <AUTHOR>
 * @version $ IsPossibleBuyWithdrawalCardLabelHandler, v 0.1 2024/12/23 15:00 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class IsPossibleBuyWithdrawalCardLabelHandler  implements LabelHandler {
    @Autowired
    private RestTemplate restTemplate;

    @Override
    public String getLabel() {
        return "is_possible_buy_withdrawal_card";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();

        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
            IsPossibleBuyWithdrawalCardRequest req = new IsPossibleBuyWithdrawalCardRequest();
                req.setUserNo(Collections.singletonList(userNo));
            IsPossibleBuyWithdrawalCardResponse response = request(req);

                if ( Objects.nonNull(response) && response.getCode().equals("1")
                        && Objects.nonNull(response.getData())) {
                    boolean data = response.getData();
                    result.add(new AdsLabelResp.Param(userNo, null, String.valueOf(data)));
                }
        }
        return new AdsLabelResp(label, result);
    }


    private IsPossibleBuyWithdrawalCardResponse request(IsPossibleBuyWithdrawalCardRequest req) {
        String uri = ApolloUtil.getAppProperty("xyf-vip-system.url","https://qa1-xyf-vip-system.testxinfei.cn/rpc/tag/improve-times");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> requestEntity = new HttpEntity<>(req, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, requestEntity, String.class);
            IsPossibleBuyWithdrawalCardResponse resp = JsonUtil.parse(responseEntity.getBody(), IsPossibleBuyWithdrawalCardResponse.class);
            log.info("接口请求日志:[{}],url:{}, header:{}, request:{},resp:{}",
                    getLabel(),
                    uri, JsonUtil.toJson(headers), JsonUtil.toJson(req), JsonUtil.toJson(responseEntity));
            if (resp == null || !Objects.equals(responseEntity.getStatusCode(),HttpStatus.OK)) {
                log.error("接口返回错误码:[{}],url:{},header:{}, request:{},resp:{}",
                        getLabel(),
                        uri, JsonUtil.toJson(headers), JsonUtil.toJson(req),
                        JsonUtil.toJson(responseEntity), NoneException.catError());
            }
            return resp;
        } catch (Exception ex) {
            log.error("接口异常:[{}],url:{},request:{}", getLabel(), uri, JsonUtil.toJson(requestEntity), ex);
        }
        return null;
    }
}
