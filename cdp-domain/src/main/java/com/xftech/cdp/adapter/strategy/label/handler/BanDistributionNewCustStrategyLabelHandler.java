package com.xftech.cdp.adapter.strategy.label.handler;

import com.xftech.cdp.adapter.strategy.label.LabelHandler;
import com.xftech.cdp.adapter.strategy.label.QueryLabelRequest;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.client.ads.model.resp.AdsLabelResp;
import com.xftech.cdp.infra.client.increaseamt.RcsProviderClient;
import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @version $ BanDistributionNewCustStrategyLabelHandler, v 0.1 2024/12/2 10:21 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class BanDistributionNewCustStrategyLabelHandler implements LabelHandler {
    @Autowired
    private RcsProviderClient rcsProviderClient;
    @Autowired
    private CisService cisService;

    @Override
    public String getLabel() {
        return "new_cust_ban_distribution";
    }

    @Override
    public AdsLabelResp execute(String label, QueryLabelRequest batchAdsLabelVO) {
        List<AdsLabelResp.Param> result = new ArrayList<>();
        for (Long userNo : batchAdsLabelVO.getUserNoList()) {
            BaseCisResp<RegisterInfoByUserNo.RespDto> respDtoBaseCisResp = cisService.queryRegisterInfoByUserNo(userNo);
            log.info("贷超实时标签查询cis接口req:{},resp:{}", userNo, JsonUtil.toJson(respDtoBaseCisResp));
            if (respDtoBaseCisResp != null && respDtoBaseCisResp.getData() != null
                    && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) &&
                    StringUtils.isNotBlank(respDtoBaseCisResp.getData().getCustNo()) && StringUtils.isNotBlank(respDtoBaseCisResp.getData().getIdCardNumber())) {
                RegisterInfoByUserNo.RespDto userInfo = respDtoBaseCisResp.getData();
                String orderNo = String.format("%s_%s", DateTimeUtil.formatDateToStr(new Date(), null), userInfo.getUserNo());
                RequestHeader requestHeader = rcsProviderClient.buildAccessControlHeader(orderNo, userInfo.getApp(), userInfo.getSourceChannel());
                AccessControlQueryReq accessControlQueryReq = new AccessControlQueryReq(userInfo.getIdCardNumber(), userInfo.getCustNo(), null);
                Response<AccessControlResp> accessControlRespResponse = rcsProviderClient.accessControlQuery(requestHeader, accessControlQueryReq);
                // 修改后的条件判断
                if (accessControlRespResponse != null && accessControlRespResponse.getData() != null) {
                    AccessControlResp data = accessControlRespResponse.getData();
                    List<String> validForbiddenReasons = Arrays.asList("RTX90d1", "Rapp_dz_dl1", "RTX15d1", "RTX30d1", "RSX90d1", "RSX30d1", "RSX1y1");
                    if (data.getForbidden_status() && validForbiddenReasons.contains(data.getForbidden_reason()) && Objects.equals(data.getDiversion_label(), "appDL01")) {
                        result.add(new AdsLabelResp.Param(userNo, null, "1"));
                    } else {
                        result.add(new AdsLabelResp.Param(userNo, null, "0"));
                    }
                }
            } else {
                result.add(new AdsLabelResp.Param(userNo, null, null));
            }
        }
        return new AdsLabelResp(label, result);
    }
}