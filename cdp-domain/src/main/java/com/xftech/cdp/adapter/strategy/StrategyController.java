package com.xftech.cdp.adapter.strategy;

import com.xftech.cdp.api.StrategyApi;
import com.xftech.cdp.api.dto.aitel.AITelBizSourceRequest;
import com.xftech.cdp.api.dto.aitel.AiParam;
import com.xftech.cdp.api.dto.aitel.BizSourceInfo;
import com.xftech.cdp.api.dto.base.PageResultResponse;
import com.xftech.cdp.api.dto.base.Response;
import com.xftech.cdp.api.dto.req.*;
import com.xftech.cdp.api.dto.req.random.NewRandomListReq;
import com.xftech.cdp.api.dto.req.sms.SmsGroupReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateCheckReq;
import com.xftech.cdp.api.dto.req.sms.SmsTemplateListReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.req.StrategyCreateReq;
import com.xftech.cdp.api.dto.req.StrategyDetailReq;
import com.xftech.cdp.api.dto.req.StrategyListReq;
import com.xftech.cdp.api.dto.req.StrategyOperateReq;
import com.xftech.cdp.api.dto.req.StrategyUpdateReq;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.*;
import com.xftech.cdp.api.dto.resp.AbsMonitorListResp;
import com.xftech.cdp.api.dto.resp.CouponListResp;
import com.xftech.cdp.api.dto.resp.CrowdPackListResp;
import com.xftech.cdp.api.dto.resp.InstantStrategyDetailResp;
import com.xftech.cdp.api.dto.resp.SmsTemplateListResp;
import com.xftech.cdp.api.dto.resp.StrategyDetailResp;
import com.xftech.cdp.api.dto.resp.StrategyListResp;
import com.xftech.cdp.api.dto.resp.TeleNodeListReq;
import com.xftech.cdp.api.dto.resp.TeleNodeListResp;
import com.xftech.cdp.api.dto.resp.dict.Dict;
import com.xftech.cdp.api.dto.resp.random.RandomListResp;
import com.xftech.cdp.domain.ads.service.ModelPlatformService;
import com.xftech.cdp.domain.randomnum.RandomNumService;
import com.xftech.cdp.domain.strategy.model.dto.StrategyCycleDayConfig;
import com.xftech.cdp.domain.strategy.service.AppBannerService;
import com.xftech.cdp.domain.strategy.service.InstantStrategyService;
import com.xftech.cdp.domain.strategy.service.StrategyService;
import com.xftech.cdp.domain.strategy.service.impl.StrategyCommonService;
import com.xftech.cdp.infra.annotation.OperateLogAnnotation;
import com.xftech.cdp.infra.annotation.SysLog;
import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.AppConfigService;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Array;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:52
 */
@AllArgsConstructor
@RestController
public class StrategyController implements StrategyApi {

    private final StrategyService strategyService;
    private final StrategyCommonService strategyCommonService;
    private final InstantStrategyService instantStrategyService;
    private final RandomNumService randomNumService;
    private final ModelPlatformService modelPlatformService;
    private final AppBannerService appBannerService;
    private AppConfigService appConfigService;

    @Override
    @SysLog("策略配置-创建接口")
    @OperateLogAnnotation(description = "新增策略", type = OperateTypeEnum.ADD, mode = OperateModeEnum.STRATEGY)
    public Response<Boolean> create(StrategyCreateReq strategyCreateReq) {
        return Response.success(strategyService.insert(strategyCreateReq));
    }

    @Override
    @SysLog("策略配置-更新接口")
    @OperateLogAnnotation(description = "修改策略", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.STRATEGY)
    public Response<Boolean> update(StrategyUpdateReq strategyUpdateReq) {
        return Response.success(strategyService.update(strategyUpdateReq));
    }

//    @Override
//    @SysLog("策略配置-删除接口")
//    public Response<Boolean> delete(StrategyDeleteReq strategyDeleteReq) {
//        return Response.success(strategyService.delete(strategyDeleteReq));
//    }

    @Override
    @SysLog("策略配置-列表接口")
    public Response<PageResultResponse<StrategyListResp>> list(StrategyListReq strategyListReq) {
        return Response.success(strategyService.list(strategyListReq));
    }

    @Override
    @SysLog("策略配置-详情接口")
    public Response<StrategyDetailResp> getDetail(StrategyDetailReq strategyDetailReq) {
        return Response.success(strategyService.getDetail(strategyDetailReq.getStrategyId()));
    }

    @Override
    @SysLog("策略配置-操作接口")
    @OperateLogAnnotation(description = "操作策略", type = OperateTypeEnum.OPERATE, mode = OperateModeEnum.STRATEGY)
    public Response<Boolean> operate(StrategyOperateReq strategyOperateReq) {
        return Response.success(strategyService.operate(strategyOperateReq));
    }

    @Override
    @SysLog("策略配置-批量删除接口")
    @OperateLogAnnotation(description = "删除策略", type = OperateTypeEnum.DELETE, mode = OperateModeEnum.STRATEGY)
    public Response<String> batchDelete(StrategyBatchDeleteReq strategyBatchDeleteReq) {
        return Response.success(null, strategyService.batchDelete(strategyBatchDeleteReq));
    }

    @Override
    @SysLog("策略配置-短信模板列表接口")
    public Response<PageResultResponse<SmsTemplateListResp>> smsTemplateList(SmsTemplateListReq smsTemplateListReq) {
        return Response.success(strategyCommonService.smsTemplateList(smsTemplateListReq));
    }

    @Override
    @SysLog("策略配置-电销节点列表接口")
    public Response<TeleNodeListResp> teleNodeList(TeleNodeListReq teleNodeListReq) {
        return Response.success(strategyCommonService.teleNodeList(teleNodeListReq));
    }

    @Override
    @SysLog("策略配置-优惠券列表接口")
    public Response<CouponListResp> couponList(CouponListReq couponListReq) {
        return Response.success(strategyCommonService.couponList(couponListReq));
    }

    @Override
    @SysLog("策略配置-监控列表接口")
    public Response<PageResultResponse<AbsMonitorListResp>> monitorList(MonitorListReq monitorListReq) {
        return Response.success(strategyService.monitorList(monitorListReq));
    }

    @Override
    @SysLog("策略配置-引擎版-监控列表接口")
    public Response<PageResultResponse<AbsMonitorListResp>> monitorEngineList(MonitorEngineListReq request) {
        return Response.success(strategyService.queryStrategyEngineMonitorList(request));
    }

    @Override
    @SysLog("策略配置-监控列表导出接口")
    public void exportMonitor(ExportMonitorReq exportMonitorReq, HttpServletResponse response) throws IOException {
        strategyService.exportMonitor(exportMonitorReq, response);
    }

    @Override
    @SysLog("策略配置-人群包人数统计接口")
    public Response<Integer> getCrowdPackUserNum(CrowdPackUserNumReq crowdPackUserNumReq) {
        return Response.success(strategyService.countCrowdPackUserNum(crowdPackUserNumReq));
    }

    @Override
    @SysLog("策略配置-人群包列表接口")
    public Response<PageResultResponse<CrowdPackListResp>> getCrowdPackList(CrowdPackListReq crowdPackListReq) {
        return Response.success(strategyService.getCrowdPackList(crowdPackListReq));
    }

    @Override
    @SysLog("策略配置-创建实时策略接口")
    @OperateLogAnnotation(description = "新增T0策略", type = OperateTypeEnum.ADD, mode = OperateModeEnum.STRATEGY)
    public Response<Boolean> createInstantStrategy(InstantStrategyCreateReq strategyCreateReq) {
        return Response.success(instantStrategyService.createInstantStrategy(strategyCreateReq));
    }

    @Override
    @SysLog("策略配置-更新实时策略接口")
    @OperateLogAnnotation(description = "修改T0策略", type = OperateTypeEnum.UPDATE, mode = OperateModeEnum.STRATEGY)
    public Response<Boolean> updateInstantStrategy(InstantStrategyUpdateReq strategyUpdateReq) {
        return Response.success(instantStrategyService.updateInstantStrategy(strategyUpdateReq));
    }

    @Override
    @SysLog("策略配置-实时策略详情接口")
    public Response<InstantStrategyDetailResp> getInstantStrategyDetail(StrategyDetailReq strategyDetailReq) {
        return Response.success(instantStrategyService.getInstantStrategyDetail(strategyDetailReq.getStrategyId()));
    }

    @Override
    @SysLog("策略配置-获取短信模板分类接口")
    public Response<List<Dict>> getSmsTemplateGroup(SmsGroupReq smsGroupReq) {
        return Response.success(strategyService.getSmsTemplateGroup(smsGroupReq));
    }

    @Override
    @SysLog("策略配置-短信模板检查接口")
    public Response<Boolean> checkSmsTemplate(SmsTemplateCheckReq smsTemplateCheckReq) {
        return Response.success(strategyService.templateParamCheck(smsTemplateCheckReq));
    }

    @Override
    public Response<PageResultResponse<RandomListResp>> randomList(NewRandomListReq newRandomListReq) {
        return Response.success(randomNumService.getRandomList(newRandomListReq,null));
    }

    @Override
    public Response<List<String>> engineCodeList() {
        return Response.success(modelPlatformService.getModelNames());
    }

    @Override
    public Response<List<CyclePreviewDto>> cyclePreview(Integer cycleNum) {
        return Response.success(strategyService.cyclePreview(cycleNum));
    }

    @Override
    @SysLog("策略配置-获取电销类型列表")
    public Response<PageResultResponse<NameTypeResp>> getNameTypeList(NameTypeReq nameTypeReq) {
        return Response.success(strategyService.getNameTypeList(nameTypeReq));
    }

    @Override
    @SysLog("策略配置-获取电销配置项")
    public Response<NameTypeConfigResp> getNameTypeConfigList() {
        return Response.success(strategyService.getNameTypeConfigList());
    }

    @Override
    public Response<StrategyReportDailyResp> reportDailyStrategyList(){
        return Response.success(strategyService.queryReportDailyList());
    }

    @Override
    @SysLog("策略配置-获取电销策略列表")
    public Response<PageResultResponse<PolicyListResp>> getPolicyList(PolicyListReq policyListReq) {
        return Response.success(strategyService.getPolicyList(policyListReq));
    }

    @Override
    @SysLog("策略配置-操作电销策略")
    public Response<Boolean> operatePolicy(OperatePolicyReq operatePolicyReq) {
        return Response.success(strategyService.operatePolicy(operatePolicyReq));
    }

    @Override
    @SysLog("策略配置-新电销更新策略优先级")
    public Response<Boolean> updatePolicyPriority(@Validated @RequestBody UpdatePolicyPriorityReq req) {
        req.setTraceId(UUID.randomUUID().toString());
        req.setTs(System.currentTimeMillis()/1000);
        req.setUa("xyf-cdp");
        return Response.success(strategyService.updatePolicyPriority(req));
    }

    @Override
    @SysLog("策略配置-电销策略详情")
    public Response<String> policyDetail(Integer policyId) {
        return Response.successString(strategyService.policyDetail(policyId));
    }

    @Override
    public Response<PageResultResponse<GoodsListResp>> getGoodsList(GoodsListReq goodsListReq) {
        return Response.success(strategyService.getGoodsList(goodsListReq));
    }

    @Override
    public Response<PageResultResponse<PushTemplateResp>> getPushTemplateList(PushTemplateReq pushTemplateReq) {
        return Response.success(strategyService.getPushTemplateList(pushTemplateReq));
    }

    @Override
    public Response<PageResultResponse<AppBannerTemplateResp>> getAppBannerTemplateList(@RequestBody AppBannerTemplateReq appBannerTemplateReq) {
        return Response.success(appBannerService.getAppBannerList(appBannerTemplateReq));
    }

    @Override
    @SysLog("AI电销-查询业务线下所有业务来源")
    public Response<List<BizSourceInfo>> bizSourceList(@Validated @RequestBody AITelBizSourceRequest aiTelBizSourceRequest) {
        return Response.success(strategyService.bizSourceList(aiTelBizSourceRequest));
    }

    @Override
    public Response<List<AiParam>> aiParams() {
        return Response.success(strategyService.aiParams());
    }

    @Override
    public Response<PageResultResponse<EngineReInputReportResp>> monitorEngineReInput(@Validated @RequestBody MonitorEngineReInputListReq monitorListReq) {
        if (ApolloUtil.switchStatus("mock.monitorEngineReInput.switch")) {
            return Response.success(JSONObject.parseObject(ApolloUtil.getAppProperty("mock.monitorEngineReInput.data"), new TypeReference<PageResultResponse<EngineReInputReportResp>>() {}));
        }

        Long strategyId = monitorListReq.getStrategyId();
        String date = monitorListReq.getDate();
        if (strategyId == null || StringUtils.isBlank(date)) {
            return Response.success();
        }
        return Response.success(instantStrategyService.monitorEngineReInput(strategyId, Integer.parseInt(StringUtils.replace(date, "-", "")), monitorListReq.getBeginNum(), monitorListReq.getSize()));
    }

}
