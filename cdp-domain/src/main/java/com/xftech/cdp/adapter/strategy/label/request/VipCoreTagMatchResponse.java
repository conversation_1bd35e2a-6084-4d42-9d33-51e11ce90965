/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.adapter.strategy.label.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xinfei.xfframework.common.BaseResponse;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ VipCoreTagMatchRequest, v 0.1 2024/8/5 15:05 benlin.wang Exp $
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class VipCoreTagMatchResponse extends BaseResponse {
    @ApiModelProperty(value = "异常code", required = true)
    private String code = "-1";

    @ApiModelProperty(value = "异常信息", required = true)
    private String message = "sys error";

    private Map<String, Object> data;
}