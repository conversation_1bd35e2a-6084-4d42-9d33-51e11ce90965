package com.xftech.cdp.infra.client.datacenter.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/16
 */
@RefreshScope
@Component
@Setter
@Getter
public class DataCenterConfig {
    @Value("${cdp.datacenter.host:https://api.devxinfei.cn/data-center}")
    private String dataCenterHost;

    @Value("${cdp.datacenter.mobile.decrypt.route:user/mobile-get}")
    private String mobileDecryptRoute;

    @Value("${cdp.datacenter.mobile.decrypt.key:dev-5vnG2yfALaPC68Ti}")
    private String mobileDecryptKey;

    @Value("${cdp.datacenter.id.card.decrypt.route:user/id-get}")
    private String idCardDecryptRoute;

}
