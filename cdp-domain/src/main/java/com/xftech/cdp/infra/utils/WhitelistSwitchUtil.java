/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.infra.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.config.GrayConfig;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.dingtalk.config.DingTalkConfig;
import com.xftech.cdp.infra.config.ApolloUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.xftech.cdp.infra.client.dingtalk.config.TechDingTalkConfig.*;

/**
 * <AUTHOR>
 * @version $ WhitelistSwitchUtil, v 0.1 2025/1/14 12:01 xu.fan Exp $
 */
@Slf4j
public class WhitelistSwitchUtil {

    private final static String ACT_INTERFACE_SWITCH = "activity.interface.switch";

    private final static String COMMON_GRAY_SWITCH = "common.gray.switch";

    private final static String ACT_INTERFACE_WHITELIST = "activity.interface.whitelist";

    private final static String ACT_BOOL_SWITCH = "activity.remote.switch";


    public static boolean boolSwitchByApollo(String key) {

        if(StringUtils.isBlank(key) || StringUtils.isBlank(ApolloUtil.getAppProperty(ACT_BOOL_SWITCH))) {
            return true;
        }

        JSONObject switchMap = JSON.parseObject(ApolloUtil.getAppProperty(ACT_BOOL_SWITCH));
        if (switchMap == null || switchMap.get(key) == null) {
            return true; // 默认全开
        }
        return switchMap.getBoolean(key);
    }


    /**
     * Apollo配置接口灰度开关
     * @param userNo
     * @param switchKey
     * @param cacheKey 定制缓存开关比例Key，可覆盖默认
     * @return
     */
    public static boolean graySwitchByApollo(String userNo,String switchKey, String cacheKey) {
        if (StringUtils.isBlank(userNo) || StringUtils.isBlank(switchKey)) {
            return true;
        }

        int grayValue = 100; // 默认全开
        if(StringUtils.isNotBlank(cacheKey)) {
            String grayStr = ApolloUtil.getAppProperty(cacheKey);
            if(StringUtils.isNumeric(grayStr)) {
                grayValue = Integer.parseInt(grayStr);
            }
        } else {
            JSONObject switchMap = JSON.parseObject(ApolloUtil.getAppProperty(ACT_INTERFACE_SWITCH));
            if(switchMap != null &&switchMap.getInteger(switchKey) != null) {
                grayValue = switchMap.getInteger(switchKey);
            }
        }
        String userNoStr = ApolloUtil.getAppProperty(ACT_INTERFACE_WHITELIST);

        return isGraySwitchPass(userNo, grayValue, userNoStr);
    }

    public static boolean commonGraySwitchByApollo(String targetStr, String switchKey) {
        if (StringUtils.isBlank(targetStr) || StringUtils.isBlank(switchKey)) {
            return true;
        }

        int grayValue = 100; // 默认全开
        String whitelistStr = "";
        JSONObject switchMap = JSON.parseObject(ApolloUtil.getAppProperty(COMMON_GRAY_SWITCH));
        if(switchMap != null && switchMap.getString(switchKey) != null) {
            try {
                GrayConfig config = switchMap.getObject(switchKey, GrayConfig.class);
                if (config != null) {
                    grayValue = config.getPercentNum();
                    whitelistStr = config.getWhitelist();
                }
            } catch (Exception e) {
                log.error("解析灰度配置异常, targetStr:{}, switchKey:{}",targetStr, switchKey, e);
                // 发送钉钉通知 需标记异常环境
                String active = SpringUtil.getProperty("spring.profiles.active");
                String content = String.format("解析Apollo灰度配置异常 环境:%s, targetStr:%s, switchKey:%s", active, targetStr, switchKey);
                DingTalkUtil.sendTextToDingTalk(DingTalkUtil.urlSignProcess(TECH_DINGTALK_ROBOT_URL, TECH_DINGTALK_ROBOT_SECRET) , content,TECH_DINGTALK_APP_IDS, false);

                return false;
            }
        }

        return isGraySwitchPass(targetStr, grayValue, whitelistStr);
    }

    public static String getPropertyValue(String key, String defaultValue) {
        if(StringUtils.isBlank(key)) {
            return "";
        }
        return ApolloUtil.getAppProperty(key, defaultValue);
    }

    public static boolean isWhitelist(String userNo, String userList) {

        if(StringUtils.isBlank(userList)) {
          return false;
        }

        Set<String> userSet = new HashSet<>(Arrays.asList(userList.split(",")));
        return userSet.contains(userNo);
    }

    public static boolean isGraySwitchPass(String userNo, Integer grayValue, String userStrList) {

        if(StringUtils.isBlank(userNo) || grayValue == null) {
            // 参数不合法，返回不通过
            return false;
        }

        if(grayValue == 100) {
            return true;
        }

        if(isWhitelist(userNo, userStrList)) {
            return true;
        }

        long hashVal = HashUtils.crc32Hash(userNo);
        if(hashVal % 100 < grayValue) {
            // 灰度值通过
            return true;
        }
        return false;
    }


    public static void main(String[] args) {

        List<Integer> percent = Arrays.asList(5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100);
        Map<String, List<Long>> res = new HashMap<>();
        for (int i = 0; i < percent.size(); i++) {
            List<Long> list = new ArrayList<>();
            Integer sp = i == 0 ?  0 : percent.get(i-1);
            Integer ep = percent.get(i);
            for (long j = 2330L; j < 2366L; j++) {
                if(!isGraySwitchPass("" + j, sp, null) && isGraySwitchPass("" + j, ep, null)) {
                    list.add(j);
                }
            }
            res.put("" + ep, list);
        }
        System.out.println(res);
    }

}
