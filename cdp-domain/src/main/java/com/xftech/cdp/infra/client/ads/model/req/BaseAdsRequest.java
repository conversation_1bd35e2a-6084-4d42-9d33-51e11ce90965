package com.xftech.cdp.infra.client.ads.model.req;

import lombok.Data;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/27 10:40
 */
@Data
public class BaseAdsRequest<T> {
    /**
     * 必须
     * 请求流水号
     */
    private String requestId;
    /**
     * 必须
     * 调用方系统简称，麻雀： xyf-cdp
     */
    private String ua;
    /**
     * 必须
     * 接口名称
     */
    private String apiName;
    /**
     * 必须
     * 时间戳
     */
    private Long timestamp = System.currentTimeMillis() / 1000L;
    /**
     * 非必须
     * 回调地址，可为空
     */
    private String caller;
    /**
     * 验签的key
     */
    private String key;
    /**
     * 非必须
     * 签名串，可为空
     */
    private String sign;
    /**
     * 请求参数
     */
    private T args;

    public BaseAdsRequest() {
    }

    public BaseAdsRequest(T args) {
        this.args = args;
    }


}
