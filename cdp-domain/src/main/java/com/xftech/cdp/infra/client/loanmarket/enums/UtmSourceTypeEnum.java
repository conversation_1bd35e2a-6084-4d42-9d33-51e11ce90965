package com.xftech.cdp.infra.client.loanmarket.enums;

import lombok.Getter;

import java.util.Objects;

/**
 * 渠道类型
 * <AUTHOR>
 * @since 2023-05-17
 */

@Getter
public enum UtmSourceTypeEnum {
    /**
     * 注册渠道
     */
    REGISTER("user_register_channel", "1", "注册渠道"),

    /**
     * 进件渠道
     */
    ENTRY("user_borrow_channel", "2", "进件渠道"),

    /**
     * 放款渠道
     */
    LOAN("", "3", "放款渠道");

    private final String labelName;
    private final String type;
    private final String desc;

    UtmSourceTypeEnum(String labelName, String type, String desc) {
        this.labelName = labelName;
        this.type = type;
        this.desc = desc;
    }

    public static String getType(String labelName) {
        for (UtmSourceTypeEnum channelTypeEnum : UtmSourceTypeEnum.values()) {
            if (Objects.equals(channelTypeEnum.getLabelName(), labelName)) {
                return channelTypeEnum.getType();
            }
        }
        return "";
    }

    public static String getDesc(String labelName) {
        for (UtmSourceTypeEnum channelTypeEnum : UtmSourceTypeEnum.values()) {
            if (Objects.equals(channelTypeEnum.getLabelName(), labelName)) {
                return channelTypeEnum.getDesc();
            }
        }
        return null;
    }
}
