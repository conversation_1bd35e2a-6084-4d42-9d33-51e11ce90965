/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt;

import com.xftech.cdp.infra.client.increaseamt.model.req.AccessControlQueryReq;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.AccessControlResp;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Response;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version $ RcsProviderClient, v 0.1 2024/7/3 17:37 lingang.han Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class RcsProviderClient {
    private RestTemplate restTemplate;
    private AppConfigService appConfigService;


    /**
     * 风险 禁申管制状态查询
     */
    public Response<AccessControlResp> accessControlQuery(RequestHeader header, AccessControlQueryReq request) {
        String url = appConfigService.getAccessControlUrl();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Rpc-Context", JsonUtil.toJson(header.toMap()));
        HttpEntity<Object> requestEntity = new HttpEntity<>(request, headers);
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            AccessControlResp resp = JsonUtil.parse(responseEntity.getBody(), AccessControlResp.class);
            log.info("接口请求日志:调用风险禁申管制状态查询,url:{}, header:{}, request:{},resp:{}", url, JsonUtil.toJson(header), JsonUtil.toJson(request), JsonUtil.toJson(responseEntity));
            if (resp == null || !Objects.equals(responseEntity.getStatusCode(),HttpStatus.OK)) {
                log.error("接口返回错误码:调用风险禁申管制状态查询,url:{},header:{}, request:{},resp:{}", url, JsonUtil.toJson(header), JsonUtil.toJson(request), JsonUtil.toJson(responseEntity), NoneException.catError());
            }
            return new Response<>(""+HttpStatus.OK.value(),null,resp);
        } catch (Exception ex) {
            log.error("接口异常:调用风险禁申管制状态查询,url:{},request:{}", url, JsonUtil.toJson(requestEntity), ex);
        }
        return null;
    }

    public RequestHeader buildAccessControlHeader(String orderNo, String app, String innerApp) {
        RequestHeader header = new RequestHeader();
        header.setRequester("xyf-cdp");
        header.setBiz_type("xjd_withdraw");
        header.setBiz_flow_number(orderNo);
        header.setSeq_id(orderNo);
        header.setSource_type("wap");
        header.setProduct_name("xinyongfei");
        header.setApp(app);
        header.setInner_app(innerApp);
        return header;
    }


}