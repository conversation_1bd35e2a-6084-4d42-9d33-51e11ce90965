package com.xftech.cdp.infra.utils;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.xftech.cdp.infra.constant.RedisKeyConstants;

import java.time.LocalDate;

/**
 * redis key 生成工具类
 */
public class RedisKeyUtils {

    public static String genEventNameKey(String eventName) {
        return String.format(RedisKeyConstants.SME_LIST, eventName);
    }

    public static String genSMEStrategyIdKey(Long strategyId) {
        return String.format(RedisKeyConstants.SME_LIST_STRATEGY_ID, strategyId);
    }

    public static String genStrategyIdKey(Long strategyId) {
        return String.format(RedisKeyConstants.STRATEGY_ONE, strategyId);
    }

    public static String genRefreshKey() {
        String date = LocalDateTimeUtil.format(LocalDate.now(), "yyyyMMdd");
        return String.format(RedisKeyConstants.REFRESH_META_FLAG, date);
    }

    public static String genStrategyConditionKey(Long strategyId) {
        return String.format(RedisKeyConstants.SMEC_LIST, strategyId);
    }

    public static String genEventIdListKey(Long eventId) {
        return String.format(RedisKeyConstants.SMSE_LIST, eventId);
    }

    public static String genEventIdListKeyByStrategyId(Long strategyId) {
        return String.format(RedisKeyConstants.SMSE_LIST_BY_STRATEGY, strategyId);
    }

    public static String genStrategyGroupListKey(Long strategyId) {
        return String.format(RedisKeyConstants.SG_LIST, strategyId);
    }


    public static String genStrategyGroupOneKey(Long id) {
        return String.format(RedisKeyConstants.SG_ONE, id);
    }

    public static String genOneByGroupKey(Long strategyGroupId, Integer marketChannel) {
        return String.format(RedisKeyConstants.SMC_ONE_GROUPID_CHANNEL, strategyGroupId, marketChannel);
    }

    public static String genListByStrategyGroupIdKey(Long strategyGroupId) {
        return String.format(RedisKeyConstants.SMC_LIST_GROUPID, strategyGroupId);
    }

    public static String genListByStrategyIdKey(Long strategyId) {
        return String.format(RedisKeyConstants.SMC_LIST_STRATEGYID, strategyId);
    }

    public static String genFcListByStrategyIdKey(Long strategyId, Integer marketChannel) {
        return String.format(RedisKeyConstants.FC_LIST, strategyId, marketChannel);
    }

    public static String genNewFcListByStrategyIdKey(Long strategyId, Integer marketChannel) {
        return String.format(RedisKeyConstants.NEW_FC_LIST, strategyId, marketChannel);
    }

    public static String genMaxCrowdExecLogIdKey(Long crowdId) {
        return String.format(RedisKeyConstants.MAX_CROWD_EXEC_LOG_ID, crowdId);
    }

    public static String genMaxCrowdExecTimeKey(Long crowdId) {
        return String.format(RedisKeyConstants.MAX_CROWD_EXEC_TIME_ID, crowdId);
    }

    public static String genTableNameByLogId(Long crowdExceLogId) {
        return String.format(RedisKeyConstants.CROWD_EXCE_TABLE, crowdExceLogId);
    }


    public static String genStatRealtimeStrategyDayKey(String day) {
        return String.format(RedisKeyConstants.STAT_REALTIME_STRATEGY_DAY, day);
    }

    public static String genStrategyChannelIdGroupNameKey(Long strategyChannelId) {
        return String.format(RedisKeyConstants.STRATEGY_CHANNEL_ID_GROUP_NAME, strategyChannelId);
    }

    public static String genStatDecnSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_SUM, date, strategyId);
    }

    public static String genStatDecnUser(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER, date, strategyId);
    }

    public static String genStatDecnEventSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_EVENT_SUM, date, strategyId);
    }

    public static String genStatDecnUserEvent(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_EVENT, date, strategyId);
    }

    public static String genStatDecnUserRegtime(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_REGTIME, date, strategyId);
    }

    public static String genStatDecnUserCrow(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_CROWD, date, strategyId);
    }

    public static String genStatDecnUserLabel(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_LABEL, date, strategyId);
    }

    public static String genStatDecnUserExclude(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_EXCLUDE, date, strategyId);
    }

    public static String genStatDecnUserPass(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_PASS, date, strategyId);
    }

    public static String genStatDecnUserFlow(String date, Long strategyId, Long strategyGroupId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_FLOW, date, strategyId, strategyGroupId);
    }

    public static String genStatDecnFlowSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_FLOW_SUM, date, strategyId);
    }

    public static String genStatDecnUserDisp(String date, Long strategyId, Long strategyGroupId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_DISP, date, strategyId, strategyGroupId);
    }

    public static String genStatDecnUserBlank(String date, Long strategyId, Long strategyGroupId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_BLANK, date, strategyId, strategyGroupId);
    }

    public static String genStgyChnl(Long strategyChannelId) {
        return String.format(RedisKeyConstants.STGY_CHNL, strategyChannelId);
    }

    public static String genCrowdLatestRefreshTimeKey(Long crowdId) {
        return String.format(RedisKeyConstants.CROWD_LATEST_REFRESH_TIME, crowdId);
    }

    public static String genCrowdRefreshTimeoutAlarmKey(Long crowdId) {
        return String.format(RedisKeyConstants.CROWD_REFRESH_TIMEOUT_ALARM, crowdId);
    }

    public static String genOfflineStrategyCrowdStatusAlarmKey(Long strategyId) {
        return String.format(RedisKeyConstants.OFFLINE_STRATEGY_CROWD_STATUS_ALARM, strategyId);
    }

    public static String genStrategyEndAlarmKey(Long strategyId) {
        return String.format(RedisKeyConstants.STRATEGY_END_ALARM, strategyId);
    }

    public static String genRandomNumAlarmKey(Long strategyId) {
        return String.format(RedisKeyConstants.RANDOM_NUM_ALARM, strategyId);
    }
    public static String genIntoEngineSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_ENGINE_SUM, date, strategyId);
    }
    public static String genIntoEngineNum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_ENGINE_USER, date, strategyId);
    }

    public static String genMarketSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_MARKET_SUM, date, strategyId);
    }
    public static String genMarketNum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_MARKET_USER, date, strategyId);
    }
    public static String genNotMarketSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_UN_MARKET_SUM, date, strategyId);
    }

    public static String genNotMarketNum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_UN_MARKET_USER, date, strategyId);
    }

    public static String genExcludeUserMarketSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_EXCLUDE_MARKET, date, strategyId);
    }
    public static String genExcludeUserNotMarketSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_DECN_USER_EXCLUDE_UN_MARKET, date, strategyId);
    }

    public static String genExecuteEngineFailSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_ENGINE_FAIL_SUM, date, strategyId);
    }

    public static String genExecuteEngineFailNum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_ENGINE_FAIL_USER, date, strategyId);
    }

    public static String genOffEngineNotIntoEngineNum(String date,Long strategyId){
        return String.format(RedisKeyConstants.STAT_OFF_ENGINE_NOT_INTO_ENGINE_USER, date, strategyId);
    }

    public static String genOffEngineLabelExcludeSum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_OFF_ENGINE_LABEL_EXCLUDE_SUM, date, strategyId);
    }

    public static String genOffEngineSendNum(String date, Long strategyId) {
        return String.format(RedisKeyConstants.STAT_OFF_ENGINE_SEND_SUM, date, strategyId);
    }

    public static String genCrowdSliceExecLockKey(Long strategyId, Long crowdSliceId, String versionNo) {
        return String.format(RedisKeyConstants.CROWD_SLICE_EXEC_LOCK_KEY, strategyId, crowdSliceId, versionNo);
    }

    public static String genStrategyUserHisKey(Long strategyId, Long userNo, String dateValue, String versionNo) {
        return String.format(RedisKeyConstants.STRATEGY_USER_HIS_KEY, strategyId, userNo, dateValue, versionNo);
    }

    public static String strategyStatisticMapKey(Long strategyId, String dateValue) {
        return String.format(RedisKeyConstants.STRATEGY_EXEC_STATISTIC_MAP_KEY, strategyId, dateValue);
    }

}
