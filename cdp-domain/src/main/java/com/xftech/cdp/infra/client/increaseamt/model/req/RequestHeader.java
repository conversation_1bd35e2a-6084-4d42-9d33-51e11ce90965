/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt.model.req;

import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.Data;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version $ RequestHeader, v 0.1 2024/3/13 20:44 yye.xu Exp $
 */


// 文档地址
@Data
public class RequestHeader {
    private String requester;
    private String biz_type;
    private String biz_flow_number;
    private String seq_id;
    private String source_type;
    private String product_name;
    private String app;
    private String inner_app;
    private String user_id;
    private String device_id;
    private String ip;

    public Map<String, Object> toMap() {
        return JsonUtil.toMap(JsonUtil.toJson(this));
    }
}