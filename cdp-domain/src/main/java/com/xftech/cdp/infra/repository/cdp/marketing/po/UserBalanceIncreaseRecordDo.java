package com.xftech.cdp.infra.repository.cdp.marketing.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class UserBalanceIncreaseRecordDo {
    private String orderNumber;

    private Date createTime;

    private Date updateTime;

    private Long appUserId;

    private BigDecimal amount;

    private Integer status;

    private Long id;

    private String reason;

    private Byte dFlag;
}