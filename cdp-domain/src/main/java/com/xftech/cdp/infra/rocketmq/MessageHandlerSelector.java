/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq;

import java.util.List;
import java.util.concurrent.TimeUnit;

import com.xftech.cdp.infra.config.LogUtil;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.RedisUtils;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ MessageHandlerSelector, v 0.1 2024/5/17 15:31 lingang.han Exp $
 */

@Slf4j
@Component
public class MessageHandlerSelector {

    @Autowired
    private List<MessageHandler> messageHandlerList;
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 注意：
     *
     * @param tag 对接非tag 的消息，调用该方法应该传null
     */
    public void doMessage(String topic, String tag, String messageId, Object message) {
        boolean result = false;
        String lockKey = null;
        if (StringUtils.isNotBlank(messageId)) {
            lockKey = SecureUtil.md5(String.format("cg:%s:", topic + tag) + messageId);
        }
        LogUtil.logDebug("MessageHandlerSelector doMessage topic={} tag={} messageId={} message={}", topic, tag, messageId, JSONObject.toJSONString(message));
        try {
            MessageHandler messageHandler = messageHandlerList.stream().filter(item -> item.topicTagCompare(topic, tag)).findFirst().orElse(null);
            if (messageHandler != null) {
                if (StringUtils.isNotBlank(lockKey) && !redisUtils.lock(lockKey, "0", 10, TimeUnit.MINUTES)) {
                    log.warn("MessageHandlerSelector 重复消息, topic={}, tag={}, messageId={}, body={}", topic, tag, messageId, message);
                    return;
                }
                result = messageHandler.execute(messageId, message);
            }
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(lockKey)) {
                redisUtils.unlock(lockKey);
            }
            log.error("MessageHandlerSelector 消息订阅失败", e);
        } finally {
            log.info("MessageHandlerSelector 消息订阅topic:{}, result:{}, messageId:{}, message:{}", topic, result, messageId, JsonUtil.toJson(message));
        }
    }

}