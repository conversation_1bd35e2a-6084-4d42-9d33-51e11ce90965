package com.xftech.cdp.infra.repository.cdp.marketing.po;

import java.util.Date;

import lombok.Data;

/**
 * 营销活动奖品表
 *
 * @TableName marketing_activity_rewards
 */
@Data
public class MarketingActivityRewardsDo {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 奖品名称
     */
    private String rewardName;

    /**
     * 奖品类型 1:优惠券
     */
    private Integer rewardType;

    /**
     * 奖品ID
     */
    private String rewardId;

    /**
     * 奖品优先级,数值越大优先级越高
     */
    private Integer rewardPriority;

    /**
     * 奖品人群包id,多个以英文逗号分隔
     */
    private String rewardCrowdPack;

    /**
     * 是否兜底奖品 0:否;1:是
     */
    private Integer rewardFallback;

    /**
     * 是否删除 0:未删除;1:已删除
     */
    private Integer dFlag;

    /**
     * 创建人
     */
    private String createdOp;

    /**
     * 修改人
     */
    private String updatedOp;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

}