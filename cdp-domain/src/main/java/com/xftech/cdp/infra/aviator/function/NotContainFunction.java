package com.xftech.cdp.infra.aviator.function;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.function.FunctionUtils;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

public class NotContainFunction extends AbstractFunction {

    public static final String NAME = "notcontain";

    @Override
    public String getName() {
        return NAME;
    }

    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1, AviatorObject arg2) {

        Object obj1 = arg1.getValue(env);
        Object obj2 = arg2.getValue(env);
        if (obj1 != null && obj2 != null) {
            if (obj1 instanceof String) {
                String left = FunctionUtils.getStringValue(arg1, env);
                String right = FunctionUtils.getStringValue(arg2, env);

                String[] lList = left.split(",");
                String[] rList = right.split(",");

                Set<String> lSet = Arrays.stream(lList).map(String::trim).collect(Collectors.toSet());
                Set<String> rSet = Arrays.stream(rList).map(String::trim).collect(Collectors.toSet());

                if (CollectionUtils.containsAny(lSet, rSet)) {
                    return AviatorBoolean.FALSE;
                } else {
                    return AviatorBoolean.TRUE;
                }
            }
            if (obj1 instanceof Integer || obj1 instanceof Long
                    || obj1 instanceof Double || obj1 instanceof Float) {
                Number left = FunctionUtils.getNumberValue(arg1, env);
                BigDecimal decimalLeft = new BigDecimal(left.toString());

                String right = FunctionUtils.getStringValue(arg2, env);
                String[] uList = right.split(",");
                for (String s : uList) {
                    BigDecimal decimal = new BigDecimal(s);
                    if (decimal.compareTo(decimalLeft) == 0) {
                        return AviatorBoolean.FALSE;
                    }
                }
            }
        }
        return AviatorBoolean.TRUE;

    }

}
