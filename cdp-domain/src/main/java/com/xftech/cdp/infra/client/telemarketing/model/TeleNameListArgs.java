package com.xftech.cdp.infra.client.telemarketing.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @<NAME_EMAIL>
 * @date 2023/3/31 14:32
 */
@Data
public class TeleNameListArgs {
    @JsonProperty("user_type")
    @JSONField(name = "user_type")
    private Long userType;

    @JsonProperty("name")
    @JSONField(name = "name")
    private String name;

    @JsonProperty("type")
    @JSONField(name = "type")
    private Integer type;

    @JsonProperty("sub_type")
    @JSONField(name = "sub_type")
    private Integer subType;

    @JsonProperty("page")
    @JSONField(name = "page")
    private Integer page;

    @JsonProperty("pageSize")
    @JSONField(name = "pageSize")
    private Integer pageSize;


}
