/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.rcspt.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.domain.strategy.service.dispatch.event.StrategyEventDispatchService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.MessageTopicConstants;
import com.xftech.cdp.infra.rocketmq.dto.RcsptRocketMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ rcsptIncreaseCreditHandler, v 0.1 2024/8/8 15:20 lingang.han Exp $
 */
@Slf4j
@Component
public class RcsptIncreaseCreditHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return MessageTopicConstants.RCSPT_INCREASE_CREDIT;
    }

    @Override
    public boolean execute(String messageId, Object message) {
        RcsptRocketMessageVO messageVO = JSONObject.parseObject(message.toString(), RcsptRocketMessageVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        bizEventMessageVO.setBizEventType("RcsptIncreaseCredit");
        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(RcsptRocketMessageVO bizEventRocketMessageVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        bizEventMessageVO.setAppUserId(Long.parseLong(bizEventRocketMessageVO.getUserNo()));
        bizEventMessageVO.setApp(bizEventRocketMessageVO.getApp());
        bizEventMessageVO.setInnerApp(bizEventRocketMessageVO.getInnerApp());
        bizEventMessageVO.setTriggerDatetime(bizEventRocketMessageVO.getTriggerDatetime());
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setIncreaseCreditResult(bizEventRocketMessageVO.getAdjustResult());
        extrData.setAdjustAmount(bizEventRocketMessageVO.getExtrData().getAdjustAmount());
        extrData.setAvailableAmount(bizEventRocketMessageVO.getExtrData().getAvailableAmount());
        extrData.setManageType(bizEventRocketMessageVO.getManageType());
        extrData.setEnd_time(bizEventRocketMessageVO.getExtrData().getEndTime());
        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}