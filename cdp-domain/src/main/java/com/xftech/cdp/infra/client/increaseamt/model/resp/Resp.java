/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt.model.resp;

import lombok.Data;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version $ Resp, v 0.1 2024/3/14 10:05 yye.xu Exp $
 */

@Data
public class Resp {
    // 000000 - 成功
    private String code;
    private String message;
    private Object data;

    public boolean isSucceed(){
        return Objects.equals("000000", code);
    }
}