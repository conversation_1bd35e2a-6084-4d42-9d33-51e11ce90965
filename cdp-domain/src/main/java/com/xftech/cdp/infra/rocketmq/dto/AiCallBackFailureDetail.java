/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ AiCallBackFailureDetail, v 0.1 2024/9/3 11:45 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiCallBackFailureDetail implements Serializable {

    private static final long serialVersionUID = -2056590994643650812L;

    private String app;

    private Long userNo;

    /**
     * 5001：服务异常
     * 5002：校验异常
     */
    private String code;

    /**
     * 失败原因
     */
    private String message;
}