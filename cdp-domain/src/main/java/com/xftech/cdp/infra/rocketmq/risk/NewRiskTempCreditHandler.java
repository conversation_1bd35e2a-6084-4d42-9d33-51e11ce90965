package com.xftech.cdp.infra.rocketmq.risk;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.AmountChangeRocketMessageDTO;
import com.xftech.cdp.infra.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;


/**
 * <AUTHOR>
 * @version $ NewTrackingCardClickHandler, v 0.1 2025/3/25 16:40 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class NewRiskTempCreditHandler implements MessageHandler {
    @Resource
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return "tp_rcspt_risk_amount_change_message_tg_risk_temp_amt";
    }

    @Override
    public boolean execute(String messageId, Object message) {
        AmountChangeRocketMessageDTO messageVO = JSONObject.parseObject(message.toString(), AmountChangeRocketMessageDTO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);

        if (bizEventMessageVO == null || StringUtils.isBlank(bizEventMessageVO.getBizEventType())) {
            return false;
        }

        mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
        return true;
    }

    private BizEventMessageVO transform(AmountChangeRocketMessageDTO amountChangeRocketMessageDTO) {
        if (amountChangeRocketMessageDTO == null || amountChangeRocketMessageDTO.getExtraData() == null || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getApp()) || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getManageType()) || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getAdjustResult()) || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getUserNo()) || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getExtraData().getStartTime()) || StringUtils.isAnyBlank(amountChangeRocketMessageDTO.getExtraData().getEndTime())) {
            return null;
        }

        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();

        bizEventMessageVO.setAppUserId(Long.valueOf(amountChangeRocketMessageDTO.getUserNo()));
        bizEventMessageVO.setApp(amountChangeRocketMessageDTO.getApp());
        bizEventMessageVO.setInnerApp(amountChangeRocketMessageDTO.getInnerApp());

        if (StringUtils.isNotBlank(amountChangeRocketMessageDTO.getTriggerDatetime())) {
            bizEventMessageVO.setTriggerDatetime(amountChangeRocketMessageDTO.getTriggerDatetime());
        } else {
            bizEventMessageVO.setTriggerDatetime(DateUtil.convertStr(new Date()));
        }

        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        if (amountChangeRocketMessageDTO.getAdjustResult().equals("S") && amountChangeRocketMessageDTO.getManageType().equals("risk_temp_amt")) {
            extrData.setManageType("risk_temp_amt");
            //提额后可用额度
            extrData.setAmount(new BigDecimal(Optional.ofNullable(amountChangeRocketMessageDTO.getExtraData().getAvailableAmount()).orElse("0")));
            //提额金额
            extrData.setAdjustAmount(new BigDecimal(Optional.ofNullable(amountChangeRocketMessageDTO.getExtraData().getAdjustAmount()).orElse("0")));
            extrData.setStart_time(amountChangeRocketMessageDTO.getExtraData().getStartTime());
            extrData.setEnd_time(amountChangeRocketMessageDTO.getExtraData().getEndTime());
            bizEventMessageVO.setBizEventType("NewRiskTempCredit");
        }

        bizEventMessageVO.setExtrData(extrData);
        return bizEventMessageVO;
    }
}
