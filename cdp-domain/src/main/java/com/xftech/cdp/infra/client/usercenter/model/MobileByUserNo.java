/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.usercenter.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version $ MobileByUserNo, v 0.1 2023/11/15 13:25 yye.xu Exp $
 */

@Data
public class MobileByUserNo {

    @Data
    public static class RespDto {
        private String mobile;
        private String app;
        private Long userNo;


        @JsonIgnore
        @JSONField(serialize = false)
        public UserIdDetailResp convert(){
            UserIdDetailResp userIdDetailResp = new UserIdDetailResp();
            userIdDetailResp.setMobile(mobile);
            return userIdDetailResp;
        }
    }
}