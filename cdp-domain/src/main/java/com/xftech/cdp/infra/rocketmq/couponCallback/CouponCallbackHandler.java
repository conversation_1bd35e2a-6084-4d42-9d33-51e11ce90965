package com.xftech.cdp.infra.rocketmq.couponCallback;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rabbitmq.vo.CouponCallbackVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.dto.NewCouponCallbackVO;
import com.xftech.cdp.infra.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR>
 * @version $ CouponCallbackHandler, v 0.1 2025/3/17 17:36 tianshuo.qiu Exp $
 */
@Slf4j
@Component
public class CouponCallbackHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    public boolean execute(String messageId, Object message) {
        if (message == null) {
            log.warn("优惠券回调消息为空, messageId={}", messageId);
            return false;
        }
        NewCouponCallbackVO newCouponCallbackVO;
        newCouponCallbackVO = JSONObject.parseObject(message.toString(), NewCouponCallbackVO.class);
        if (newCouponCallbackVO == null) {
            log.warn("优惠券回调消息解析为空, messageId={}", messageId);
            return false;
        }
        CouponCallbackVO couponCallbackVO = new CouponCallbackVO();
        couponCallbackVO.setReport(new CouponCallbackVO.Report());
        // 复制属性
        try {
            BeanUtils.copyProperties(newCouponCallbackVO,couponCallbackVO.getReport());
        } catch (Exception e) {
            log.info("复制属性失败, messageId={}", messageId, e);
            e.printStackTrace();
        }
        List<CouponCallbackVO> couponCallbackVOList = Lists.newArrayList();
        couponCallbackVOList.add(couponCallbackVO);
        try {
            mqConsumeService.couponCallbackProcess(couponCallbackVOList);
            log.info("优惠券回调处理成功, messageId={}, couponCallbackVO={}", messageId, JsonUtil.toJson(newCouponCallbackVO));
        } catch (Exception ex) {
            log.warn("优惠券批量消费异常, message={}, couponCallbackVOList:{}", JsonUtil.toJson(message), JsonUtil.toJson(couponCallbackVOList));
        }

        return true;
    }

}
