package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 策略营销事件表
 *
 * @TableName strategy_market_event
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StrategyMarketEventDo extends Do {

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 事件英文名
     */
    private String eventName;

    /**
     * 时间类型 1-立即 2-延迟
     */
    private Integer timeType;

    /**
     * 延迟数值 立即：0，延迟-具体数值
     */
    private Integer timeValue;

    /**
     * 时间单位 1-分钟 2-小时
     */
    private Integer timeUnit;

    /**
     * 每日应触达用户数-最小值
     */
    private Integer dispatchMinUserNum;

    /**
     * 每日应触达用户数-最大值
     */
    private Integer dispatchMaxUserNum;

    /**
     * 业务线类型
     */
    private String bizType;

    private static final long serialVersionUID = 1L;
}