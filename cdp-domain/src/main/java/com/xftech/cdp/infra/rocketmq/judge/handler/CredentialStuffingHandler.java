package com.xftech.cdp.infra.rocketmq.judge.handler;

import java.util.Objects;

import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.aviator.util.DateTimeUtil;
import com.xftech.cdp.infra.rabbitmq.vo.BizEventMessageVO;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.judge.model.CredentialStuffingVO;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import static com.xftech.cdp.infra.rocketmq.EventEnum.EVENT_CREDENTIAL_STUFFING_APPROVED;
import static com.xftech.cdp.infra.rocketmq.EventEnum.EVENT_CREDENTIAL_STUFFING_NOT_APPROVED;
import static com.xftech.cdp.infra.rocketmq.RocketMQEnum.TP_APIOPFCORE_API_JUDGE_ALL;

/**
 * <AUTHOR>
 * @version v1.0 2024/9/23
 * @description 事件-撞库
 */

@Slf4j
@Component
public class CredentialStuffingHandler implements MessageHandler {
    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return TP_APIOPFCORE_API_JUDGE_ALL.getTopic();
    }

    @Override
    public boolean execute(String messageId, Object message) {
        CredentialStuffingVO messageVO = JSONObject.parseObject(message.toString(), CredentialStuffingVO.class);
        BizEventMessageVO bizEventMessageVO = transform(messageVO);
        if (StringUtils.isNotBlank(bizEventMessageVO.getBizEventType())) {
            mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
            return true;
        } else {
            log.info("CredentialStuffingHandler execute 不符合撞库事件筛选条件, message={}", JSONObject.toJSONString(messageVO));
            return false;
        }
    }

    private BizEventMessageVO transform(CredentialStuffingVO credentialStuffingVO) {
        BizEventMessageVO bizEventMessageVO = new BizEventMessageVO();
        if (credentialStuffingVO.getUser_no() == null || credentialStuffingVO.getRequest_time() == null || credentialStuffingVO.getResult() == null ||
                StringUtils.isAnyBlank(credentialStuffingVO.getInner_app(), credentialStuffingVO.getRegister_app())) {
            return bizEventMessageVO;
        }
        bizEventMessageVO.setAppUserId(credentialStuffingVO.getUser_no());
        bizEventMessageVO.setApp(credentialStuffingVO.getRegister_app());
        bizEventMessageVO.setInnerApp(credentialStuffingVO.getInner_app());
        bizEventMessageVO.setTriggerDatetime(DateTimeUtil.formatDateToStr(credentialStuffingVO.getRequest_time(), null));
        BizEventMessageVO.ExtrData extrData = new BizEventMessageVO.ExtrData();
        extrData.setCredentialStuffingResult(credentialStuffingVO.getResult());
        bizEventMessageVO.setExtrData(extrData);

        // 用户类型 1：新用户； 2、无预授信记录；3、有预授信记录，无激活成功记录 ；4、有预授信记录，有激活成功记录
        Integer userType = credentialStuffingVO.getUser_type();
        // 是否存在确认借款记录（0不存在、1存在）
        Integer isLoan = credentialStuffingVO.getIs_loan();
        // 是否有放款中或还款中订单（0：没有在贷订单、1：有在贷订单）
        Integer isLoaning = credentialStuffingVO.getIs_loaning();
        // 0：无已结清记录
        Integer loanType = credentialStuffingVO.getLoan_type();

        if ((Objects.equals(userType, 4) && Objects.equals(isLoan, 0)) ||
                (Objects.equals(userType, 4) && Objects.equals(isLoan, 1) && Objects.equals(isLoaning, 0) && Objects.equals(loanType, 0))) {
            bizEventMessageVO.setBizEventType(EVENT_CREDENTIAL_STUFFING_APPROVED.getEventType());
        } else if (Objects.equals(userType, 1) || Objects.equals(userType, 2) || Objects.equals(userType, 3)) {
            bizEventMessageVO.setBizEventType(EVENT_CREDENTIAL_STUFFING_NOT_APPROVED.getEventType());
        }
        return bizEventMessageVO;
    }

}
