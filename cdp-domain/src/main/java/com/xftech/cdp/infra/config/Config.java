package com.xftech.cdp.infra.config;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 公共配置
 *
 * <AUTHOR>
 * @since 2023/2/18
 */
@RefreshScope
@Getter
@Component
public class Config {

    @Value("${server.port}")
    private int port;

    @Value("${spring.application.name}")
    private String applicationName;

    @Value("${aliyun.oss.uploadFilePath}")
    private String uploadFilePath;
    /**
     * 短信报告查询天数
     */
    @Value("${sms.query.report.day:2}")
    private Integer smsQueryReportDay;
    /**
     * 数仓aesKey
     */
    @Value("${ads.mobile.aes.key}")
    private String adsMobileAesKey;
    /**
     * 加贷电销人群包对应类型
     */
    @Value("${crowd.pack.sms.flg}")
    private String crowdPackSmsFlg;
    /**
     * 优惠券查询天数
     */
    @Value("${coupon.query.day:30}")
    private Integer couponQueryDay;
    /**
     * 批次数量统计是否使用缓存
     */
    @Value("${batch.count.useCache:true}")
    private boolean batchCountUseCache;
    /**
     * 短信回执推送超时时间：单位：小时
     */
    @Value("${sms.report.over.time:60}")
    private Integer smsReportOverTime;

    /**
     * 风控模型分线程数
     */
    @Value("${refresh.risk.thread.num:5}")
    private Integer refreshRiskThreadNum;

    @Value("${refresh.risk.page.size:1000}")
    private Integer refreshRiskPageSize;

    /**
     * 外部接口验签开关
     */
    @Value("${api.ext.sign.enable:true}")
    private boolean extApiSignEnable;

    public boolean getBatchCountUseCache () {
        return batchCountUseCache;
    }

    public JSONObject getCrowdPackSmsFlg () {
        return JSONObject.parseObject(crowdPackSmsFlg);
    }
}
