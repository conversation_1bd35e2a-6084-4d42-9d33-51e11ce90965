/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.client.increaseamt;

import java.util.Optional;

import javax.annotation.Resource;

import com.xftech.cdp.infra.client.increaseamt.model.req.Request;
import com.xftech.cdp.infra.client.increaseamt.model.req.RequestHeader;
import com.xftech.cdp.infra.client.increaseamt.model.resp.Resp;
import com.xftech.cdp.infra.config.AppConfigService;
import com.xftech.cdp.infra.exception.NoneException;
import com.xftech.cdp.infra.utils.JsonUtil;

import com.dianping.cat.proxy.Tracer;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @version $ IncreaseAmtClient, v 0.1 2024/3/13 20:39 yye.xu Exp $
 */

@Slf4j
@Service
@AllArgsConstructor
public class IncreaseAmtClient {
    private RestTemplate restTemplate;
    private AppConfigService appConfigService;
    @Resource
    private IncreaseAmtUtils increaseAmtUtils;

    public Resp increaseAmt(RequestHeader header, Request request) {
        String url = appConfigService.getIncreaseAmtUrl();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Rpc-Context", JsonUtil.toJson(header.toMap()));
        HttpEntity<Object> requestEntity = new HttpEntity<>(request, headers);
        try {
            // 调用风控提额记录
            increaseAmtUtils.recordToday(Long.parseLong(request.getUser_no()), request.getModify_stage_type());

            ResponseEntity<String> responseEntity = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            Resp resp = JsonUtil.parse(responseEntity.getBody(), Resp.class);
            log.info("接口请求日志:调用风险提额,url:{}, header:{}, request:{},resp:{}", url, JsonUtil.toJson(header), JsonUtil.toJson(requestEntity), JsonUtil.toJson(resp));
            if (resp == null || !resp.isSucceed()) {
                log.error("接口返回错误码:调用风险提额,url:{},header:{}, request:{},resp:{}", url, JsonUtil.toJson(header), JsonUtil.toJson(requestEntity), JsonUtil.toJson(resp), NoneException.catError());
            }
            // 监控告警
            String code = "None";
            code = resp != null ? Optional.ofNullable(resp.getCode()).orElse(code) : code;
            Tracer.logEvent("increaseAmt_code", code);
            return resp;
        } catch (Exception ex) {
            log.error("接口异常:调用风险提额,url:{},request:{}", url, JsonUtil.toJson(requestEntity), ex);
        }
        return null;
    }
}
