package com.xftech.cdp.infra.aviator;

import com.googlecode.aviator.AviatorEvaluator;
import com.xftech.cdp.infra.aviator.function.ContainFunction;
import com.xftech.cdp.infra.aviator.function.DateFunction;
import com.xftech.cdp.infra.aviator.function.NotContainFunction;
import com.xftech.cdp.infra.aviator.function.TimeCompareFunction;

/**
 * 加载Aviator自定义函数
 *
 * <AUTHOR>
 */
public class AviatorCommon {

    /**
     * 加载Aviator自定义函数
     */
    public static void loadFunction() {
        AviatorEvaluator.addFunction(new ContainFunction());
        AviatorEvaluator.addFunction(new NotContainFunction());
        AviatorEvaluator.addFunction(new DateFunction());
        AviatorEvaluator.addFunction(new TimeCompareFunction());
    }

}
