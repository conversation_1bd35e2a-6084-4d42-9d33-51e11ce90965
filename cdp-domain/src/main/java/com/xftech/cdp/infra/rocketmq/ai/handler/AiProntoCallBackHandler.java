/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.ai.handler;

import com.alibaba.fastjson.JSONObject;
import com.xftech.cdp.domain.mq.MqConsumeService;
import com.xftech.cdp.infra.rocketmq.MessageHandler;
import com.xftech.cdp.infra.rocketmq.MessageTopicConstants;
import com.xftech.cdp.infra.rocketmq.dto.AiCallBackMessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version $ AiProntoCallBackHandler, v 0.1 2024/8/26 10:34 lingang.han Exp $
 */
@Slf4j
@Component
public class AiProntoCallBackHandler implements MessageHandler {

    @Autowired
    private MqConsumeService mqConsumeService;

    @Override
    public String getTopic() {
        return MessageTopicConstants.AI_PRONTO_CALLBACK;
    }

    @Override
    public boolean execute(String messageId, Object message) {
        try {
            AiCallBackMessageVO aiCallBackMessageVO = JSONObject.parseObject(message.toString(), AiCallBackMessageVO.class);
            mqConsumeService.aiCallbackProcess(aiCallBackMessageVO);
        } catch (Exception e) {
            log.error("aiCallBackRocketMqConsumer consumer error, messageid={},body={}", messageId, message.toString(), e);
        }
        return false;
    }
}