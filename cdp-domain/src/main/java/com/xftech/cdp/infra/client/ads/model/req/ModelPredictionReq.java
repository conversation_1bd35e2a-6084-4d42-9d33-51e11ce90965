package com.xftech.cdp.infra.client.ads.model.req;

import java.util.Map;

import com.google.common.collect.Maps;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ModelPredictionReq {
    private String model_name;
    private BizData biz_data;

    @Data
    @Builder
    public static class BizData {
        private String requestId;
        private String mobile;
        private String app;
        private Long app_user_id;
        private Long trigger_datetime;
        private String biz_type;
        private Long timestamp;
        // 第几次调用，第1,2,3,,,次
        private Long callerCount;
        /**
         * 同app_user_id
         */
        private Long user_no;

        /**
         * 账户号，部分特殊特征需要补充透传该参数
         */
        private String acct_no;
        /**
         * 客户号
         */
        private String cust_no;
        /**
         * 设备ID
         */
        private String device_id;
        /**
         * 身份证号
         */
        private String id_card_number;
        /**
         * 客户名称
         */
        private String name;

        /**
         * 挽留弹窗：选项(中文名称)
         */
        private String choiceTitle;
        /**
         * 挽留弹窗：期数
         */
        private Integer installments;
        /**
         * 挽留弹窗：金额 单位:元
         */
        private Double amount;

        /**
         * 扩展参数。避免每次修改接口参数，统一使用map存储
         */
        private Map<String, Object> extMap = Maps.newHashMap();

        /**
         * 当前策略ID
         */
        private Long strategy_id;

        /**
         * 请求类型: OFFLINE=离线策略; ONLINE=T0策略; API=实时接口
         */
        private String requestType;
    }

}