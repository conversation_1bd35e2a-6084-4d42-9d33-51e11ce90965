package com.xftech.cdp.infra.annotation;

import com.xftech.cdp.infra.constant.SysConstants;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 外部接口验签
 *
 * @<NAME_EMAIL>
 * @date 2023/4/4 11:53
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ApiSigner {

    String value() default "";

    SysConstants.SignerEnum[] ua();
}
