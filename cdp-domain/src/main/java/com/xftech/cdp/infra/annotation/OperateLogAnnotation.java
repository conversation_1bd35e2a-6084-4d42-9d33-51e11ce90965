package com.xftech.cdp.infra.annotation;

import com.xftech.cdp.infra.aviator.enums.OperateModeEnum;
import com.xftech.cdp.infra.aviator.enums.OperateTypeEnum;

import java.lang.annotation.*;

/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 *
 * <AUTHOR>
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperateLogAnnotation {
    String description() default ""; // 操作描述

    OperateTypeEnum type() default OperateTypeEnum.ADD; // 操作类型，默认为 TYPE_ADD

    OperateModeEnum mode() default OperateModeEnum.STRATEGY;//操作模块 默认为 STRATEGY
}
