package com.xftech.cdp.infra.repository.cdp.crowd.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CrowdWereHouseSnapshotDo extends Do {

    /**
     * 执行时间
     */
    private LocalDateTime bizDate;
    /**
     * 数仓最大app_user_id
     */
    private Long maxId;

    private Long minId;

}
