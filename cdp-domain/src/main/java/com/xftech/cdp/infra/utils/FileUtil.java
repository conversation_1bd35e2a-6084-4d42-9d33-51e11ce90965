package com.xftech.cdp.infra.utils;

/**
 * <AUTHOR>
 * @version v1.0 2025/5/9
 * @description FileUtil
 */

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileUtil {

    /**
     * 计算文件按指定大小分片后的各分片的开始和结束位置,最后一个分片过小合并到上一分片
     *
     * @param fileSize  要分片的文件字节大小
     * @param chunkSize 分片大小,单位bytes
     * @return 每个分片的 [startPos, endPos] 列表, startPos 和 endPos 都是包含的字节偏移[0-based]
     */
    public static List<long[]> calculateChunkPositionsLastOneMerge(long fileSize, long chunkSize) {
        List<long[]> chunkPositions = Lists.newArrayList();
        try {
            if (fileSize <= 0 || chunkSize <= 0) {
                return chunkPositions;
            }

            long start = 0;
            while (start < fileSize) {
                long end = Math.min(start + chunkSize - 1, fileSize - 1);
                chunkPositions.add(new long[]{start, end});
                start = end + 1;
            }

            // 合并最后一个过小的分片到倒数第二个分片中
            if (chunkPositions.size() >= 2) {
                long[] lastChunk = chunkPositions.get(chunkPositions.size() - 1);
                long lastChunkSize = lastChunk[1] - lastChunk[0] + 1;
                if (lastChunkSize < chunkSize) {
                    // 合并到前一个
                    long[] penultimate = chunkPositions.get(chunkPositions.size() - 2);
                    penultimate[1] = lastChunk[1];
                    chunkPositions.remove(chunkPositions.size() - 1);
                }
            }
        } catch (Exception e) {
            log.error("FileUtil calculateChunkPositionsLastOneMerge error", e);
        }

        return chunkPositions;
    }

    /**
     * 计算文件按指定大小分片后的各分片的开始和结束位置
     *
     * @param fileSize  要分片的文件字节大小
     * @param chunkSize 分片大小,单位bytes
     * @return 每个分片的 [startPos, endPos] 列表, startPos 和 endPos 都是包含的字节偏移[0-based]
     */
    public static List<long[]> calculateChunkPositions(long fileSize, long chunkSize) {
        if (fileSize <= 0 || chunkSize <= 0) {
            return Lists.newArrayList();
        }

        List<long[]> positions = Lists.newArrayList();
        long start = 0;
        while (start < fileSize) {
            long end = Math.min(start + chunkSize - 1, fileSize - 1);
            positions.add(new long[]{start, end});
            start = end + 1;
        }
        return positions;
    }

    /**
     * 计算文件按指定大小分片后的各分片的开始和结束位置
     *
     * @param file      要分片的文件
     * @param chunkSize 分片大小,,单位bytes
     * @return 每个分片的 [startPos, endPos] 列表, startPos 和 endPos 都是包含的字节偏移[0-based]
     */
    public static List<long[]> calculateChunkPositions(File file, long chunkSize) {
        long fileSize = file.length();
        List<long[]> positions = Lists.newArrayList();

        long start = 0;
        while (start < fileSize) {
            long end = Math.min(start + chunkSize - 1, fileSize - 1);
            positions.add(new long[]{start, end});
            start = end + 1;
        }
        return positions;
    }

    /**
     * 按给定分片位置读取文件内容并打印
     *
     * @param file         要读取的文件
     * @param chunkOffsets 每个分片的 [start, end] 数组（字节偏移，0-based）
     */
    public static void readChunks(File file, List<long[]> chunkOffsets) {
        try (RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            byte[] buffer = new byte[1024 * 8]; // 8KB缓冲区

            for (int i = 0; i < chunkOffsets.size(); i++) {
                long start = chunkOffsets.get(i)[0];
                long end = chunkOffsets.get(i)[1];
                long length = end - start + 1;

                raf.seek(start);
                long remaining = length;

                System.out.printf("Reading chunk %d: start=%d, end=%d, size=%d bytes%n", i, start, end, length);

                while (remaining > 0) {
                    int toRead = (int) Math.min(buffer.length, remaining);
                    int bytesRead = raf.read(buffer, 0, toRead);

                    if (bytesRead == -1) {
                        break; // EOF
                    }

                    // 打印内容（如果是二进制文件请谨慎！）
                    System.out.print(new String(buffer, 0, bytesRead));

                    remaining -= bytesRead;
                }

                System.out.println("\n--- End of chunk ---\n");
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将源文件按指定大小分片到目标目录，返回所有分片文件列表。
     *
     * @param sourceFile 源文件
     * @param chunkSize  每个分片的最大字节数,单位bytes
     * @param outputDir  分片输出目录（会自动创建）
     * @return 分片文件列表
     * @throws IOException 如果读写出错
     */
    public static List<File> splitFile(File sourceFile, long chunkSize, File outputDir) throws IOException {
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }
        List<File> chunks = new ArrayList<>();
        try (InputStream in = new BufferedInputStream(new FileInputStream(sourceFile))) {
            byte[] buffer = new byte[64 * 1024];
            int bytesRead;
            long written = 0;
            int partIndex = 0;
            File chunk = new File(outputDir, sourceFile.getName() + ".part" + (partIndex++));
            OutputStream out = new BufferedOutputStream(new FileOutputStream(chunk));
            try {
                while ((bytesRead = in.read(buffer)) != -1) {
                    int offset = 0;
                    while (offset < bytesRead) {
                        long remain = chunkSize - written;
                        int toWrite = (int) Math.min(remain, bytesRead - offset);
                        out.write(buffer, offset, toWrite);
                        written += toWrite;
                        offset += toWrite;

                        if (written >= chunkSize) {
                            out.close();
                            chunks.add(chunk);

                            // 准备下一个分片
                            chunk = new File(outputDir, sourceFile.getName() + ".part" + (partIndex++));
                            out = new BufferedOutputStream(new FileOutputStream(chunk));
                            written = 0;
                        }
                    }
                }
                out.close();
                chunks.add(chunk);
            } finally {
                // 确保流被关闭
                try {
                    out.close();
                } catch (IOException ignore) {
                }
            }
        }
        return chunks;
    }

    /**
     * 根据分片列表，对每个分片执行指定的处理逻辑。
     *
     * @param chunks    分片文件列表
     * @param processor 每个分片文件的处理逻辑
     * @throws IOException 如果处理过程中出现 I/O 错误
     */
    public static void processChunks(List<File> chunks, Consumer<File> processor) throws IOException {
        for (File chunk : chunks) {
            processor.accept(chunk);
        }
    }

    // 示例主方法
    public static void main(String[] args) throws IOException {
        File source = new File("/Users/<USER>/Downloads/crowd.text");
        File outputDir = new File("/Users/<USER>/Downloads/chunks");
        long chunkSize = 1024;

        System.out.printf("fSize=%d", source.length());
        System.out.println();

        List<long[]> chunks = calculateChunkPositionsLastOneMerge(source.length(), chunkSize);
        for (int i = 0; i < chunks.size(); i++) {
            long[] pos = chunks.get(i);
            System.out.printf("Chunk %d: start=%d, end=%d%n", i, pos[0], pos[1]);
            System.out.println();
        }
        readChunks(source, chunks);
    }

}