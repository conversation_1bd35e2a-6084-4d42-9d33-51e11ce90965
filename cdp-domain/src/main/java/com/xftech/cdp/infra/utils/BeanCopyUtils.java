package com.xftech.cdp.infra.utils;

import com.xftech.cdp.infra.rabbitmq.vo.BizEventVO;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version v1.0 2025/4/10
 * @description BeanCopyUtils
 */
@Component
public class BeanCopyUtils {

    private static final ObjectMapper mapper = new ObjectMapper() {
        {
            registerModule(new JavaTimeModule());
            // 关闭写入时间戳（否则反序列化时会失败）
            disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        }
    };

    public static <T> T deepCopy(T object, Class<T> clazz) {
        return mapper.convertValue(object, clazz);
    }

}
