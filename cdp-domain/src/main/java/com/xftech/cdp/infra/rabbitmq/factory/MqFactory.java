package com.xftech.cdp.infra.rabbitmq.factory;

import com.xftech.cdp.infra.rabbitmq.factory.impl.BizEventMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.SmsMqService;
import com.xftech.cdp.infra.rabbitmq.factory.impl.CouponMqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MqFactory {
    @Autowired
    protected SmsMqService smsMqService;
    @Autowired
    protected CouponMqService couponMqService;
    @Autowired
    protected BizEventMqService bizEventMqService;

//    private MqFactory() {
//    }

    public AbstractMqFactory createMq(int type) {
        if (0 == type) {
            return smsMqService;
        } else if (1 == type) {
            return couponMqService;
        } else if (2 == type) {
            return bizEventMqService;
        }
        return null;
    }
}
