/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.infra.rocketmq.telemkt;

import com.google.gson.Gson;
import com.xftech.cdp.api.dto.req.external.TeleImportResultReq;
import com.xftech.cdp.domain.strategy.model.enums.StrategyMarketChannelEnum;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xinfei.xfframework.common.starter.mq.MqConsumerListener;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
/**
 *  新电销回执消息
 * <AUTHOR>
 * @version $ TeleCallRocketMqConsumer, v 0.1 2023/10/20 17:10 wancheng.qu Exp $
 */

@Component
@RocketMQMessageListener(consumerGroup = "cg_xyf_cdp_tp_marketing_telname_enter_result", topic = "tp_marketing_telname_enter_result",consumeMode = ConsumeMode.CONCURRENTLY)
@Slf4j
public class TeleCallRocketMqConsumer extends MqConsumerListener<String> {

    @Autowired
    private UserDispatchDetailService userDispatchDetailService;
    @Override
    protected void doMessage(String s, String s1, MessageExt messageExt) {
        log.info("TeleCallRocketMqConsumer receive message topic={},messageid={},body={}",s,messageExt.getMsgId(),s1);
        try {
            TeleImportResultReq t = new Gson().fromJson(s1,TeleImportResultReq.class);
            userDispatchDetailService.teleCall(t, StrategyMarketChannelEnum.VOICE_NEW);
        }catch (Exception e){
            log.error("TeleCallRocketMqConsumer consumer error, messageid={},body={}",messageExt.getMsgId(),s1,e);
        }

    }
}