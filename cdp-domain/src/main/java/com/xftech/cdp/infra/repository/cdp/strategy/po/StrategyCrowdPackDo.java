package com.xftech.cdp.infra.repository.cdp.strategy.po;

import com.xftech.cdp.infra.repository.Do;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 策略-人群包关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StrategyCrowdPackDo extends Do {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 策略id
     */
    private Long strategyId;

    /**
     * 人群包id
     */
    private Long crowdPackId;
}
