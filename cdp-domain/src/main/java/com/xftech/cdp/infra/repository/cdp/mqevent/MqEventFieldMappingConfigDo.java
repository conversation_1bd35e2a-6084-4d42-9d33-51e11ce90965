package com.xftech.cdp.infra.repository.cdp.mqevent;

import java.util.Date;

import lombok.Data;

/**
 * 消息事件字段映射表
 * mq_event_field_mapping_config
 */
@Data
public class MqEventFieldMappingConfigDo {
    /**
     * id
     */
    private Long id;

    /**
     * MQ生产者
     */
    private String topic;

    /**
     * MQ消费者
     */
    private String consumer;

    /**
     * MQ过滤Tag信息
     */
    private String tag;

    /**
     * 消息事件名称
     */
    private String eventType;

    /**
     * 事件映射配置信息
     */
    private String fieldMapping;

    /**
     * 参数解析处理器
     */
    private String parse;

    /**
     * 是否删除：1删除 0未删除
     */
    private Short dFlag;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

}