package com.xftech.cdp.infra.client.sso.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2023/2/9
 */
@RefreshScope
@Component
@Setter
@Getter
public class SsoConfig {
    @Value("${cdp.sso.host}")
    private String ssoHost;

    @Value("${cdp.sso.getImageCode.route:login/img-code}")
    private String getImageCodeRoute;

    @Value("${cdp.sso.sendCaptcha.route:login/captcha}")
    private String sendCaptchaRoute;

    @Value("${cdp.sso.getToken.route:login/get-token}")
    private String getTokenRoute;

    @Value("${cdp.sso.getToken.route:login/check-token}")
    private String checkTokenRoute;
}
