package com.xftech.cdp.infra.repository.cdp.strategy.po;

import lombok.Data;

/**
 * 引擎延迟重新决策记录表
 * engine_redecision_delay
 */
@Data
public class EngineReDecisionDelayReportDo {

    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 延迟轮次
     */
    private Integer reInputCount;
    /**
     * 本轮延迟决策人次
     */
    private Integer totalRecords;
    /**
     * 本轮延迟决策人数
     */
    private Integer distinctUserCount;
    /**
     * 本轮进入引擎人次
     */
    private Integer totalStatus1Records;
    /**
     * 本轮进入引擎人数
     */
    private Integer distinctUserCountStatus1;
    /**
     * 本轮决策结果为营销人次
     */
    private Integer totalReInputResult3Records;
    /**
     * 本轮决策结果为营销人数
     */
    private Integer distinctUserReInputResult3;
    /**
     * 本轮排除标签过滤人数
     */
    private Integer distinctUserReInputResult1;
    /**
     * 本轮决策失败人次
     */
    private Integer totalStatus2Records;
    /**
     * 本轮决策失败人数
     */
    private Integer distinctUserCountStatus2;

}