package com.xftech.cdp.infra.repository.cdp.strategy.po;

import java.io.Serializable;
import java.util.Date;

public class StrategyFlowNodeDo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.node_id
     *
     * @mbg.generated
     */
    private String nodeId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.flow_no
     *
     * @mbg.generated
     */
    private String flowNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.strategy_id
     *
     * @mbg.generated
     */
    private Long strategyId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.level_num
     *
     * @mbg.generated
     */
    private Integer levelNum;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.parent_id
     *
     * @mbg.generated
     */
    private String parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.d_flag
     *
     * @mbg.generated
     */
    private Short dFlag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column strategy_flow_node.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table strategy_flow_node
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.id
     *
     * @return the value of strategy_flow_node.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.id
     *
     * @param id the value for strategy_flow_node.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.node_id
     *
     * @return the value of strategy_flow_node.node_id
     *
     * @mbg.generated
     */
    public String getNodeId() {
        return nodeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.node_id
     *
     * @param nodeId the value for strategy_flow_node.node_id
     *
     * @mbg.generated
     */
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId == null ? null : nodeId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.flow_no
     *
     * @return the value of strategy_flow_node.flow_no
     *
     * @mbg.generated
     */
    public String getFlowNo() {
        return flowNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.flow_no
     *
     * @param flowNo the value for strategy_flow_node.flow_no
     *
     * @mbg.generated
     */
    public void setFlowNo(String flowNo) {
        this.flowNo = flowNo == null ? null : flowNo.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.strategy_id
     *
     * @return the value of strategy_flow_node.strategy_id
     *
     * @mbg.generated
     */
    public Long getStrategyId() {
        return strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.strategy_id
     *
     * @param strategyId the value for strategy_flow_node.strategy_id
     *
     * @mbg.generated
     */
    public void setStrategyId(Long strategyId) {
        this.strategyId = strategyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.level_num
     *
     * @return the value of strategy_flow_node.level_num
     *
     * @mbg.generated
     */
    public Integer getLevelNum() {
        return levelNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.level_num
     *
     * @param levelNum the value for strategy_flow_node.level_num
     *
     * @mbg.generated
     */
    public void setLevelNum(Integer levelNum) {
        this.levelNum = levelNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.parent_id
     *
     * @return the value of strategy_flow_node.parent_id
     *
     * @mbg.generated
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.parent_id
     *
     * @param parentId the value for strategy_flow_node.parent_id
     *
     * @mbg.generated
     */
    public void setParentId(String parentId) {
        this.parentId = parentId == null ? null : parentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.d_flag
     *
     * @return the value of strategy_flow_node.d_flag
     *
     * @mbg.generated
     */
    public Short getdFlag() {
        return dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.d_flag
     *
     * @param dFlag the value for strategy_flow_node.d_flag
     *
     * @mbg.generated
     */
    public void setdFlag(Short dFlag) {
        this.dFlag = dFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.created_time
     *
     * @return the value of strategy_flow_node.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.created_time
     *
     * @param createdTime the value for strategy_flow_node.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column strategy_flow_node.updated_time
     *
     * @return the value of strategy_flow_node.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column strategy_flow_node.updated_time
     *
     * @param updatedTime the value for strategy_flow_node.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_node
     *
     * @mbg.generated
     */
    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        StrategyFlowNodeDo other = (StrategyFlowNodeDo) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getNodeId() == null ? other.getNodeId() == null : this.getNodeId().equals(other.getNodeId()))
            && (this.getFlowNo() == null ? other.getFlowNo() == null : this.getFlowNo().equals(other.getFlowNo()))
            && (this.getStrategyId() == null ? other.getStrategyId() == null : this.getStrategyId().equals(other.getStrategyId()))
            && (this.getLevelNum() == null ? other.getLevelNum() == null : this.getLevelNum().equals(other.getLevelNum()))
            && (this.getParentId() == null ? other.getParentId() == null : this.getParentId().equals(other.getParentId()))
            && (this.getdFlag() == null ? other.getdFlag() == null : this.getdFlag().equals(other.getdFlag()))
            && (this.getCreatedTime() == null ? other.getCreatedTime() == null : this.getCreatedTime().equals(other.getCreatedTime()))
            && (this.getUpdatedTime() == null ? other.getUpdatedTime() == null : this.getUpdatedTime().equals(other.getUpdatedTime()));
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_node
     *
     * @mbg.generated
     */
    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getNodeId() == null) ? 0 : getNodeId().hashCode());
        result = prime * result + ((getFlowNo() == null) ? 0 : getFlowNo().hashCode());
        result = prime * result + ((getStrategyId() == null) ? 0 : getStrategyId().hashCode());
        result = prime * result + ((getLevelNum() == null) ? 0 : getLevelNum().hashCode());
        result = prime * result + ((getParentId() == null) ? 0 : getParentId().hashCode());
        result = prime * result + ((getdFlag() == null) ? 0 : getdFlag().hashCode());
        result = prime * result + ((getCreatedTime() == null) ? 0 : getCreatedTime().hashCode());
        result = prime * result + ((getUpdatedTime() == null) ? 0 : getUpdatedTime().hashCode());
        return result;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table strategy_flow_node
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", nodeId=").append(nodeId);
        sb.append(", flowNo=").append(flowNo);
        sb.append(", strategyId=").append(strategyId);
        sb.append(", levelNum=").append(levelNum);
        sb.append(", parentId=").append(parentId);
        sb.append(", dFlag=").append(dFlag);
        sb.append(", createdTime=").append(createdTime);
        sb.append(", updatedTime=").append(updatedTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}