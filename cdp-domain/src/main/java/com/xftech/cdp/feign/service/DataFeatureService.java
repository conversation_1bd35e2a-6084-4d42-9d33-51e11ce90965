/*
 * Xinfei.com Inc.
 * Copyright (c) 2015-2025. All Rights Reserved.
 */

package com.xftech.cdp.feign.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.xinfei.usergroup.random.client.AbClient;
import cn.xinfei.usergroup.random.client.bo.AbBO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xftech.cdp.feign.DataFeatureCoreFeinClient;
import com.xftech.cdp.feign.model.requset.FeatureParamValue;
import com.xftech.cdp.feign.model.requset.FeatureQueryRequest;
import com.xftech.cdp.feign.model.response.FeatureQueryResponse;
import com.xftech.cdp.infra.client.dingtalk.DingTalkUtil;
import com.xftech.cdp.infra.client.usercenter.CisService;
import com.xftech.cdp.infra.client.usercenter.model.AcctInfoModel;
import com.xftech.cdp.infra.client.usercenter.model.BaseCisResp;
import com.xftech.cdp.infra.client.usercenter.model.RegisterInfoByUserNo;
import com.xftech.cdp.infra.config.ApolloUtil;
import com.xftech.cdp.infra.config.CrowdConfig;
import com.xftech.cdp.infra.utils.WhitelistSwitchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static com.xftech.cdp.infra.client.dingtalk.config.TechDingTalkConfig.*;

/**
 * <AUTHOR>
 * @version $ DataFeatureService, v 0.1 2025/1/7 10:35 xu.fan Exp $
 */
@Slf4j
@Service
public class DataFeatureService {

    @Resource
    private DataFeatureCoreFeinClient dataFeatureCoreFeinClient;

    @Resource
    private CisService cisService;

    @Resource(name = "dataFeatureQueryExecutorWrapper")
    private Executor dataFeatureQueryExecutorWrapper;

    private final static String ACT_USER_DATA_FEATURE_CONFIG = "activity.user.feature.params";

    private final static String ACT_FEATURE_TYPE_CONFIG = "activity.feature.params.type";

    public FeatureQueryResponse getUserLastApplyDate (Long userId, List<String> featureList) {
        try {
            FeatureQueryRequest featureQueryRequest = buildFeatureQueryRequest(userId, featureList);

            FeatureQueryResponse featureQueryResponse = dataFeatureCoreFeinClient.feature_query(featureQueryRequest);
            log.info("DataFeatureService getUserLastApplyDate feature_query {} featureList={} response={}", userId, featureList, featureQueryResponse);
            if (featureQueryResponse != null && featureQueryResponse.isSuc()) {
                return featureQueryResponse;
            }
        } catch (Exception e) {
            log.error("DataFeatureService getUserLastApplyDate error userId={} featureList={} error=",userId,featureList, e);
        }
        return null;
    }

    /**
     * caller自带部分参数的参数补充查询
     * 入参一定要有app_user_id
     * @param inputs
     * @param featureList
     * @param inputs
     * @return
     */
    public FeatureQueryResponse getDateFeatureResponse (Map<String, Object> inputs, List<String> featureList) {

        if(CollectionUtils.isEmpty(featureList) ) {
            return null;
        }

        Long userNo = null;
        if (CollectionUtils.isEmpty(inputs) || inputs.get("app_user_id") == null) {
            // 线上没有发现不需要依赖userNo或其他用户参数的特征Code
            log.error("DataFeatureService getDateFeatureResponse inputs={} featureList={} inputsParams error",JSON.toJSONString(inputs), featureList);
            return null;
        }

        try {
            userNo = Long.parseLong(inputs.get("app_user_id").toString());

            List<List<String>> featureListPage = Lists.partition(featureList, 10);
            if(featureListPage.size() > 1) {
                log.info("DataFeatureService getDateFeatureResponse 分页查询 userId={} featureList={}", userNo, featureList);
            }
            FeatureQueryResponse finalResponse = null;
            for (List<String> featureListPageItem : featureListPage) {
                FeatureQueryRequest featureQueryRequest = buildFeatureQueryRequest(userNo, featureListPageItem, inputs);
                FeatureQueryResponse featureQueryResponse = dataFeatureCoreFeinClient.feature_query(featureQueryRequest);
                if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + userNo, "dataFeatureLogSwitch")) {
                    log.info("DataFeatureService getDateFeatureResponse inputs={} feature_query={} featureList={} response={}",
                            JSONObject.toJSONString(inputs),
                            JSONObject.toJSONString(featureQueryRequest),
                            JSONObject.toJSONString(featureListPageItem),
                            JSONObject.toJSONString(featureQueryResponse));
                }
                if (featureQueryResponse != null && featureQueryResponse.isSuc()) {
                    if(finalResponse == null) {
                        finalResponse = featureQueryResponse;
                    } else {
                        if(finalResponse.getFeatureValues() == null) {
                            finalResponse.setFeatureValues(featureQueryResponse.getFeatureValues());
                        } else {
                            finalResponse.getFeatureValues().putAll(featureQueryResponse.getFeatureValues());
                        }
                    }
                } else {
                    log.error("DataFeatureService getDateFeatureResponse userId={} featureList={} 分页查询失败 response={}",
                            userNo, featureListPageItem, featureQueryResponse);
                }
            }
            return finalResponse;

        } catch (Exception e) {
            log.error("DataFeatureService getDateFeatureResponse error userNo={} featureList={} error=",userNo,featureList, e);
        }
        return null;
    }


    /**
     * 配置的奖励特征过滤，并按特征优先级进行排序
     * @param userId
     * @param featureList
     * @return
     */
    public List<String> getUserHitFeature(Long userId, List<String> featureList) {
        if(CollectionUtils.isEmpty(featureList) || userId == null || !WhitelistSwitchUtil.boolSwitchByApollo("dataFeatureEnable")) {
            return Collections.emptyList();
        }

        try{
            FeatureQueryRequest request = buildFeatureQueryRequest(userId, featureList);
            FeatureQueryResponse response = dataFeatureCoreFeinClient.feature_query(request);
            log.info("DataFeatureService getUserHitFeature feature_query {} featureList={} response={}", userId, featureList, response);
            if (response != null && response.isSuc()) {
                // 解析返回结果
                Map<String, FeatureQueryResponse.FeatureValueModel> featureMap =response.getFeatureValues();
                List<String> featureCode = new ArrayList<>();
                for (String feature : featureList) {
                    if (featureMap.containsKey(feature)) {
                        FeatureQueryResponse.FeatureValueModel model = featureMap.get(feature);
                        if (Integer.parseInt("" + model.getObj()) == 1) {
                            // 命中奖励特征
                            featureCode.add(feature);
                        }
                    }
                }
                log.warn("DataFeatureService checkUserHitFeature result {},featureList={} hitFeature={}",userId, featureList, featureCode);
                return featureCode;
            }
        } catch (Exception e) {
            log.error("DataFeatureService getUserHitFeature error", e);
        }
        return Collections.emptyList();
    }

    private FeatureQueryRequest buildFeatureQueryRequest(Long userId, List<String> featureList) {

        FeatureQueryRequest featureQueryRequest = new FeatureQueryRequest();
        Map<String, Object> inputs = new HashMap<>();
        // featureParams
        Map<String, FeatureParamValue> params = getFeatureParams(userId, featureList, null);
        // build
        inputs.put("featureParams", params);
        inputs.put("featureCode", featureList);
        featureQueryRequest.setInputParams(inputs);
        log.info("DataFeatureService buildFeatureQueryRequest {} params {},featureList={}", userId, params, featureList);
        return featureQueryRequest;
    }

    private FeatureQueryRequest buildFeatureQueryRequest(Long userId, List<String> featureList, Map<String, Object> callerInputs) {

        FeatureQueryRequest featureQueryRequest = new FeatureQueryRequest();
        Map<String, Object> inputs = new HashMap<>();
        // featureParams
        Map<String, FeatureParamValue> params = getFeatureParams(userId, featureList, callerInputs);
        // 补充附带的其他参数
        addCallerInputs(params, callerInputs);
        // build
        inputs.put("featureParams", params);
        inputs.put("featureCode", featureList);
        featureQueryRequest.setInputParams(inputs);
        return featureQueryRequest;
    }

    private void addCallerInputs(Map<String, FeatureParamValue> params, Map<String, Object> callerInputs) {
        if(CollectionUtils.isEmpty(callerInputs)) {
            return;
        }
        callerInputs.forEach((k,v) -> {
            if(!params.containsKey(k)) {
                if("startTime".equals(k)) {
                    params.put(k, new FeatureParamValue(v, "StrValue"));
                } else if("timestamp".equals(k)) {
                    params.put(k, new FeatureParamValue(v, "LongValue"));
                } else if ("app_user_id".equals(k)) {
                    params.put(k, new FeatureParamValue(v, "LongValue"));
                } else if ("strategy_id".equals(k)) {
                    params.put(k, new FeatureParamValue(v, "LongValue"));
                } else if ("strategy_ids".equals(k)) {
                    params.put("strategy_id", new FeatureParamValue(v, "StrValue"));
                }else {
                    params.put(k, new FeatureParamValue(v, "StrValue")); // 默认传字符串类型
                }
            }
        });
    }

    private Map<String, FeatureParamValue> getFeatureParams(Long userId, List<String> featureList, Map<String, Object> callerInputs) {

        if(CollectionUtils.isEmpty(featureList)) {
            return Collections.emptyMap();
        }

        String paramConfig = ApolloUtil.getAppProperty(ACT_USER_DATA_FEATURE_CONFIG);
        if(StringUtils.isEmpty(paramConfig)) {
            return Collections.emptyMap();
        }

        JSONObject paramJson = JSON.parseObject(paramConfig);

        Set<String> paramSet = new HashSet<>();
        paramSet.add("user_no"); // 默认添加user_no参数, 只依赖user_no参数的特征Code不用配置
        featureList.forEach(feature -> {
            if(paramJson.containsKey(feature)) {
                String value = paramJson.getString(feature);
                paramSet.addAll(JSON.parseArray(value, String.class));
            }
        });

        if(CollectionUtils.isEmpty(paramSet)) {
            return Collections.emptyMap();
        }
        List<String> paramList = new ArrayList<>(paramSet);
        // TODO 部分参数从上游callerInputs带过来，可优化为不重复获取
        return getFeatureParamsValue(userId,featureList, paramList);

    }

    private Map<String, FeatureParamValue> getFeatureParamsValue(Long userId, List<String> featureList, List<String> paramList) {

        if(CollectionUtils.isEmpty(paramList)) {
            return Collections.emptyMap();
        }

        // 特征平台特征返回值类型可以自定
//        String paramConfig = ApolloUtil.getAppProperty(ACT_FEATURE_TYPE_CONFIG);
//        if(StringUtils.isEmpty(paramConfig)) {
//            return Collections.emptyMap();
//        }
//        JSONObject paramJson = JSON.parseObject(paramConfig);

        Map<String, FeatureParamValue> paramMap = new HashMap<>();
        paramMap.putIfAbsent("user_no", new FeatureParamValue(userId, "LongValue")); // 默认添加user_no参数

        // 补充其他参数
        callRemote(userId, paramList, paramMap);
        addCustNoDefault(featureList, paramList, paramMap);
        if(paramMap.size() < paramList.size()) {

            String paramListStr = JSON.toJSONString(paramList);
            String paramMapStr = JSON.toJSONString(paramMap);
            String active = SpringUtil.getProperty("spring.profiles.active");
            String content = String.format("环境:%s 特征参数补充出现缺失 getFeatureParamsValue %s paramList=%s paramMap=%s", active,userId,
                    paramListStr, paramMapStr);
            if(WhitelistSwitchUtil.boolSwitchByApollo("getFeatureParamsValueEnable")) {
                DingTalkUtil.sendTextToDingTalk(DingTalkUtil.urlSignProcess(TECH_DINGTALK_ROBOT_URL, TECH_DINGTALK_ROBOT_SECRET),
                        content, TECH_DINGTALK_APP_IDS, false);
            }
            log.error(content);
        }

        return paramMap;
    }

    private void addCustNoDefault(List<String> featureList, List<String> paramList, Map<String, FeatureParamValue> params) {
        if(WhitelistSwitchUtil.boolSwitchByApollo("addCustNoDefaultEnable")) {

            List<String> needCustNoCodes = JSONObject.parseArray(ApolloUtil.getAppProperty("ads.feature.need.custno.default.null", "[]"), String.class);
            if (org.apache.commons.collections4.CollectionUtils.containsAny(featureList, needCustNoCodes)) {
                if (paramList.contains("cust_no") && !params.containsKey("cust_no")) {
                    params.put("cust_no", new FeatureParamValue("null", "StrValue"));
                }
            }
        }
    }

    /**
     * cust_no/id_card_number/app/acct_no 需要配置
     * @param userId
     * @param paramList
     * @param paramMap
     */
    private void callRemote(Long userId, List<String> paramList,  Map<String, FeatureParamValue> paramMap) {

        if(paramList.contains("cust_no") || paramList.contains("id_card_number")
                || paramList.contains("app")) {
            try {
                BaseCisResp<RegisterInfoByUserNo.RespDto> resp = cisService.queryRegisterInfoByUserNo(userId);
                log.info("DataFeatureService#callRemote#queryRegisterInfoByUserNo {} resp={}", userId, resp);
                if (resp != null && resp.isCodeSucceed() && Objects.nonNull(resp.getData())) {
                    // 补充身份证号
                    String idCardNumber = resp.getData().getIdCardNumber();
                    if (StringUtils.isNotBlank(idCardNumber) && paramList.contains("id_card_number")) {
                        paramMap.putIfAbsent("id_card_number", new FeatureParamValue(idCardNumber, "StrValue"));
                    }
                    if (paramList.contains("app")) {
                        paramMap.putIfAbsent("app", new FeatureParamValue(resp.getData().getApp(), "StrValue"));
                    }
                    if (paramList.contains("cust_no")) {
                        if (StringUtils.isNotBlank(resp.getData().getCustNo())) {
                            paramMap.putIfAbsent("cust_no", new FeatureParamValue(resp.getData().getCustNo(), "StrValue"));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("DataFeatureService#callRemote#queryRegisterInfoByUserNo error {} message {}",userId, e.getMessage());
            }
        }
        if(paramList.contains("acct_no")) {
            try {
                AcctInfoModel acctInfo = cisService.getAcctNoByUserNo(userId);
                log.info("DataFeatureService#callRemote#getAcctNoByUserNo {} acctInfo={}", userId, acctInfo);
                if (acctInfo != null) {
                    if (StringUtils.isNotBlank(acctInfo.getAccountNo())) {
                        paramMap.putIfAbsent("acct_no", new FeatureParamValue(acctInfo.getAccountNo(), "StrValue"));
                    }
                }
            } catch (Exception e) {
                log.error("DataFeatureService#callRemote#getAcctNoByUserNo error {} message {}", userId, e.getMessage());
            }
        }
        if(paramList.contains("random_number")) {
            try {
                String bizKey=ApolloUtil.getAppProperty("BizKeyXinKeYunYing2025","xinkeyunying2025");
                AbBO abBO = AbClient.ab(bizKey, String.valueOf(userId),true);
                String randomNumber = String.valueOf(abBO.getRandomNum());
                log.info("获取随机数成功,方法名:ab,场景值:{},用户ID:{},随机数:{}", bizKey, userId, randomNumber);
                if (StringUtils.isNotBlank(randomNumber)) {
                    paramMap.putIfAbsent("random_number", new FeatureParamValue(randomNumber, "StrValue"));
                }
            } catch (Exception e) {
                log.error("DataFeatureService#callRemote#getRandomNum error {} message {}", userId, e.getMessage());
            }
        }
        /* TODO  device_id
         com.alibaba.fastjson2.JSONObject jsonObject = userService.queryLastDeviceByCustNoAndApp(app, custNo);
         if (jsonObject == null || Objects.isNull(jsonObject.get("device_id"))) {
         return false;
         }
         */
    }
}
