package com.xftech.cdp.feign.model.datainsight.response;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
public class CrowdPushResponse {

    private List<CrowdPushItemResult> taskResults;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CrowdPushItemResult {
        private Long crowdId;
        private String crowdSql;
        private Integer crowdStatus;
    }

}