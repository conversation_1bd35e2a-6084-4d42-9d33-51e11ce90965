package com.xftech.cdp.feign.common;

import java.util.Objects;

import lombok.Data;

@Data
public class BizBaseResponse<T> {

    private int code;

    private String message;

    private T data;

    public static boolean isSuccess(BizBaseResponse bizResponse) {
        return bizResponse != null && (Objects.equals(bizResponse.getCode(), 200));
    }

    public static boolean successData(BizBaseResponse bizResponse) {
        return isSuccess(bizResponse) && bizResponse.getData() != null;
    }

}
