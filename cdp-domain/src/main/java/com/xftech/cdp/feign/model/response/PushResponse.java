package com.xftech.cdp.feign.model.response;

import com.xftech.cdp.feign.contants.FeignConstants;
import lombok.Data;

/**
 * Push请求响应信息
 *
 * <AUTHOR>
 * @since 2023/12/18
 */
@Data
public class PushResponse<T> {
    /** 响应状态码 */
    private Integer status;
    /** 响应描述信息 */
    private String message;
    /** 响应结果信息 */
    private T response;

    public boolean isSuccess(){
        return FeignConstants.PUSH_SUCCESS_CODE.equals(status);
    }
}
