/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.feign.interceptor;

import com.xftech.cdp.api.common.AppConstants;
import com.xftech.cdp.infra.config.ApolloUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version $ DpsFeignClientInterceptor, v 0.1 2024/6/14 15:58 lingang.han Exp $
 */

/**
 * dps-service 拦截器
 */
@Slf4j
public class DataFeatureCoreInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        requestTemplate.header("appId", AppConstants.APP_NAME);
        requestTemplate.header("requestId", UUID.randomUUID().toString());
        // TODO 需要找特征平台确认
        requestTemplate.header("appAccessKey", ApolloUtil.getAppProperty("activity.datafeature.app.accesskey", "test"));
    }

}