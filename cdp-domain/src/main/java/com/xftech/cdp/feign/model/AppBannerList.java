package com.xftech.cdp.feign.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * push模板列表信息
 *
 * <AUTHOR>
 * @version $ PushTemplateList, v 0.1 2024/1/16 20:53 qu.lu Exp $
 */
@Data
public class AppBannerList {
    /** 模板列表信息 */
    private List<AppBanner> dataList;
    /** 分页信息 */
    private PageInfo pageInfo;

    @Data
    public static class AppBanner {
        @ApiModelProperty(value = "弹窗ID")
        private Integer id;

        @ApiModelProperty(value = "业务类型：1运营-新客2运营-老客3运营-变现 100产品-新客101产品-老客102产品-变现103产品-通用")
        @JsonProperty(value = "biz_type")
        private Integer bizType;

        @ApiModelProperty(value = "弹窗名称")
        @JsonProperty(value = "resource_name")
        private String resourceName;

        @ApiModelProperty(value = "生效周期，开始时间，yyyy-MM-dd HH:mm:ss")
        @JsonProperty(value = "start_time")
        private LocalDateTime startTime;

        @ApiModelProperty(value = "生效周期，结束时间，yyyy-MM-dd HH:mm:ss")
        @JsonProperty(value = "end_time")
        private LocalDateTime endTime;

        @ApiModelProperty(value = "弹窗说明")
        @JsonProperty(value = "resource_brief")
        private String resourceBrief;

        @ApiModelProperty(value = "生效app，多个app以逗号分隔")
        @JsonProperty(value = "app")
        private String app;

        @ApiModelProperty(value = "展示页面：tab_home首页，tab_me我的，incr_amt提额专区")
        @JsonProperty(value = "page_type")
        public String pageType;

        @ApiModelProperty(value = "系统类型：全部all，安卓android，苹果ios")
        @JsonProperty(value = "os")
        public String os;

        @ApiModelProperty(value = "不展示渠道:多个逗号分隔，huawei,oppo,vivo,yingyongbao,miui,360")
        @JsonProperty(value = "hide_channel")
        public String hideChannel;

        @ApiModelProperty(value = "弹出频率：1每天都弹，2每N天弹一次，3只弹一次")
        @JsonProperty(value = "frequency")
        public Integer frequency;

        @ApiModelProperty(value = "频率详情：对应frequency情况，1每天最多谈N次，每次至少间隔N分钟；2N天弹一次。上行参数：10-2代表每天最多弹10次每次至少间隔2分钟，100代表100天弹一次")
        @JsonProperty(value = "frequency_desc")
        public String frequencyDesc;

        @ApiModelProperty(value = "频次限制：最近N次点击过关闭次数大于M次不弹窗。上传参数：10-20代表最近10次点击过关闭次数大于20次不弹框")
        @JsonProperty(value = "frequency_limitation")
        public String frequencyLimitation;

        @ApiModelProperty(value = "客群类型：1定制化实时标签，2麻雀系统策略配置")
        @JsonProperty(value = "user_type")
        public Integer userType;

        @ApiModelProperty(value = "展示客群（实时标签）标签列表。user_type=1有值，2为null")
        @JsonProperty(value = "tag_list")
        public String tagList;

        @ApiModelProperty(value = "策略备注")
        @JsonProperty(value = "tag_mark")
        public String tagMark;

        @ApiModelProperty(value = "白名单列表，以英文,分割")
        @JsonProperty(value = "white_list")
        public String whiteList;

        @ApiModelProperty(value = "实验名称")
        @JsonProperty(value = "random_name")
        public String randomName;

        @ApiModelProperty(value = "随机数场景key")
        @JsonProperty(value = "random_key")
        public String randomKey;

        @ApiModelProperty(value = "随机数范围")
        @JsonProperty(value = "random_value")
        public String randomValue;

        @ApiModelProperty(value = "展示类型：101额度弹窗-底部，102优惠券弹窗，103额度弹窗-剧中，104好评引导弹窗，105提额专区弹窗-底部，106消息通知弹窗，107纯图片弹窗")
        @JsonProperty(value = "bind_logic")
        public Integer bindLogic;

        @ApiModelProperty(value = "排序 从大到小")
        @JsonProperty(value = "sort")
        public Integer sort;

        @ApiModelProperty(value = "图片地址")
        @JsonProperty(value = "image")
        public String image;

        @ApiModelProperty(value = "额度名称")
        @JsonProperty(value = "amt_name")
        public String amtName;

        @ApiModelProperty(value = "实际额度(自定义)")
        @JsonProperty(value = "amt_freeze")
        public String amtFreeze;

        @ApiModelProperty(value = "宣传文案")
        @JsonProperty(value = "pub_desc")
        public String pubDesc;

        @ApiModelProperty(value = "宣传话术")
        @JsonProperty(value = "pro_desc")
        public String proDesc;

        @ApiModelProperty(value = "主标题")
        @JsonProperty(value = "title")
        public String title;

        @ApiModelProperty(value = "按钮文案")
        @JsonProperty(value = "button_desc")
        public String buttonDesc;

        @ApiModelProperty(value = "链接地址")
        @JsonProperty(value = "jump_url")
        public String jumpUrl;

        @ApiModelProperty(value = "跳转类型")
        @JsonProperty(value = "url_type")
        public Integer urlType;

        @ApiModelProperty(value = "操作人")
        @JsonProperty(value = "operate_name")
        public String operateName;

        @ApiModelProperty(value = "状态：1生效中2已过期3未开启4编辑中")
        @JsonProperty(value = "status")
        public Integer status;

        @ApiModelProperty(value = "优先级最大数")
        @JsonProperty(value = "sort_max")
        public Integer sortMax;

        @ApiModelProperty(value = "更新时间")
        @JsonProperty(value = "updated_at")
        private LocalDateTime updatedAt;

        @ApiModelProperty(value = "创建时间")
        @JsonProperty(value = "created_at")
        private LocalDateTime createdAt;
    }
}
