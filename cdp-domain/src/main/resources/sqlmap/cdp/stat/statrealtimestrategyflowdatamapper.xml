<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statRealtimeStrategyFlowData">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity">
        <id column="id" property="id"/>
        <result column="biz_date" property="bizDate"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="event_sum" property="eventSum"/>
        <result column="user_sum" property="userSum"/>
        <result column="filter_event_num" property="filterEventNum"/>
        <result column="filter_reg_tim_num" property="filterRegTimNum"/>
        <result column="filter_crowd_num" property="filterCrowdNum"/>
        <result column="filter_label_num" property="filterLabelNum"/>
        <result column="filter_exclude_num" property="filterExcludeNum"/>
        <result column="pass_num" property="passNum"/>
        <result column="flow_control_num" property="flowControlNum"/>
        <result column="dispatch_num" property="dispatchNum"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_date, strategy_id, event_sum, user_sum, filter_event_num, filter_reg_tim_num, filter_crowd_num,
        filter_label_num, filter_exclude_num, pass_num, flow_control_num, dispatch_num
    </sql>

    <insert id="saveStatRealtimeStrategyFlowData" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyFlowDataEntity"
            useGeneratedKeys="true">
        insert into stat_realtime_strategy_flow_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizDate != null">biz_date,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="eventSum != null">event_sum,</if>
            <if test="userSum != null">user_sum,</if>
            <if test="filterEventNum != null">filter_event_num,</if>
            <if test="filterRegTimNum != null">filter_reg_tim_num,</if>
            <if test="filterCrowdNum != null">filter_crowd_num,</if>
            <if test="filterLabelNum != null">filter_label_num,</if>
            <if test="filterExcludeNum != null">filter_exclude_num,</if>
            <if test="passNum != null">pass_num,</if>
            <if test="flowControlNum != null">flow_control_num,</if>
            <if test="dispatchNum != null">dispatch_num,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bizDate != null">#{bizDate},</if>
            <if test="strategyId != null">#{strategyId},</if>
            <if test="eventSum != null">#{eventSum},</if>
            <if test="userSum != null">#{userSum},</if>
            <if test="filterEventNum != null">#{filterEventNum},</if>
            <if test="filterRegTimNum != null">#{filterRegTimNum},</if>
            <if test="filterCrowdNum != null">#{filterCrowdNum},</if>
            <if test="filterLabelNum != null">#{filterLabelNum},</if>
            <if test="filterExcludeNum != null">#{filterExcludeNum},</if>
            <if test="passNum != null">#{passNum},</if>
            <if test="flowControlNum != null">#{flowControlNum},</if>
            <if test="dispatchNum != null">#{dispatchNum},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
        </trim>
    </insert>

    <update id="updateStatRealtimeStrategyFlowData">
        update stat_realtime_strategy_flow_data
            set event_sum = #{eventSum},
            user_sum = #{userSum},
            filter_event_num = #{filterEventNum},
            filter_reg_tim_num = #{filterRegTimNum},
            filter_crowd_num = #{filterCrowdNum},
            filter_label_num = #{filterLabelNum},
            filter_exclude_num = #{filterExcludeNum},
            pass_num = #{passNum},
            flow_control_num = #{flowControlNum},
            dispatch_num = #{dispatchNum}
        where strategy_id = #{strategyId} and biz_date = #{bizDate}
    </update>

    <select id="existRealtimeStrategyFlowData" resultType="integer">
        select
        count(*)
        from stat_realtime_strategy_flow_data
        where strategy_id = #{strategyId} and biz_date = #{bizDate}
    </select>

    <select id="listByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_realtime_strategy_flow_data
        where strategy_id = #{strategyId}
        order by biz_date desc
    </select>

    <select id="selectByStrategyIdAndBizDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_realtime_strategy_flow_data
        where strategy_id = #{strategyId} and biz_date = #{bizDate}
    </select>

</mapper>
