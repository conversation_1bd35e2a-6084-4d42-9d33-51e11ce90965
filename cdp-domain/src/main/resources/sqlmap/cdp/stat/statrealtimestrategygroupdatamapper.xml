<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="statRealtimeStrategyGroupData">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyGroupDataEntity">
        <id column="id" property="id"/>
        <result column="biz_date" property="bizDate"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_group_id" property="strategyGroupId"/>
        <result column="group_name" property="groupName"/>
        <result column="flow_control_num" property="flowControlNum"/>
        <result column="dispatch_num" property="dispatchNum"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_date, strategy_id, strategy_group_id, group_name, flow_control_num, dispatch_num
    </sql>

    <insert id="saveStatRealtimeStrategyGroupData" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyGroupDataEntity"
            useGeneratedKeys="true">
        insert into stat_realtime_strategy_group_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="bizDate != null">biz_date,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="flowControlNum != null">flow_control_num,</if>
            <if test="dispatchNum != null">dispatch_num,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="bizDate != null">#{bizDate},</if>
            <if test="strategyId != null">#{strategyId},</if>
            <if test="strategyGroupId != null">#{strategyGroupId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="flowControlNum != null">#{flowControlNum},</if>
            <if test="dispatchNum != null">#{dispatchNum},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
        </trim>
    </insert>

    <update id="updateStatRealtimeStrategyGroupData">
        update stat_realtime_strategy_group_data
        set flow_control_num = #{flowControlNum},
        group_name = #{groupName},
        dispatch_num = #{dispatchNum}
        where strategy_id = #{strategyId}
        and strategy_group_id = #{strategyGroupId}
        and biz_date = #{bizDate}
    </update>

    <select id="existRealtimeStrategyGroupData" resultType="integer">
        select count(*)
        from stat_realtime_strategy_group_data
        where strategy_id = #{strategyId}
        and strategy_group_id = #{strategyGroupId}
        and biz_date = #{bizDate}
    </select>

    <select id="listByStrategyIdAndBizDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from stat_realtime_strategy_group_data
        where strategy_id = #{strategyId}
        and biz_date = #{bizDate}
    </select>

</mapper>
