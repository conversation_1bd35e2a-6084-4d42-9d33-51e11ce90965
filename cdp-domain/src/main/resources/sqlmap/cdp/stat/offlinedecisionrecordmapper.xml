<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="offlineDecisionRecord">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="market_channel" property="marketChannel"/>
        <result column="market_channel_id" property="marketChannelId"/>
        <result column="app" property="app"/>
        <result column="inner_app" property="innerApp"/>
        <result column="app_user_id" property="appUserId"/>
        <result column="trace_id" property="traceId"/>
        <result column="union_id" property="unionId"/>
        <result column="decision_result" property="decisionResult"/>
        <result column="fail_code" property="failCode"/>
        <result column="fail_reason" property="failReason"/>
        <result column="decision_detail" property="decisionDetail"/>
        <result column="created_time" property="createdTime"/>
    </resultMap>

    <select id="countLabelNum" resultType="integer">
        select count(distinct app_user_id) as user_num
        from ${tableName}
        where strategy_id = #{strategyId}
        and market_channel_id in
        <foreach collection="strategyChannelIds" item="strategyChannelId" open="(" close=")" separator=",">
            #{strategyChannelId}
        </foreach>
        and fail_code = #{failCode}
        and created_time &gt;= #{startDate}
        and created_time &lt; #{endDate}
    </select>

    <insert id="saveOfflineDecisionRecord" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.domain.stat.entity.OfflineDecisionRecordEntity"
            useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">strategy_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="marketChannelId != null">market_channel_id,</if>
            <if test="app != null">app,</if>
            <if test="innerApp != null">inner_app,</if>
            <if test="appUserId != null">app_user_id,</if>
            <if test="traceId != null">trace_id,</if>
            <if test="decisionResult != null">decision_result,</if>
            <if test="failCode != null">fail_code,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="decisionDetail != null">decision_detail,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="unionId != null">union_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">#{strategyId},</if>
            <if test="marketChannel != null">#{marketChannel},</if>
            <if test="marketChannelId != null">#{marketChannelId},</if>
            <if test="app != null">#{app},</if>
            <if test="innerApp != null">#{innerApp},</if>
            <if test="appUserId != null">#{appUserId},</if>
            <if test="traceId != null">#{traceId},</if>
            <if test="decisionResult != null">#{decisionResult},</if>
            <if test="failCode != null">#{failCode},</if>
            <if test="failReason != null">#{failReason},</if>
            <if test="decisionDetail != null">#{decisionDetail},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="unionId != null">#{unionId},</if>
        </trim>
    </insert>

</mapper>
