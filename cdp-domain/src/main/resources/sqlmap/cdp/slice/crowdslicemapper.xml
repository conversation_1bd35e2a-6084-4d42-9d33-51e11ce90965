<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdSliceMapper">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="crowd_id" jdbcType="BIGINT" property="crowdId"/>
        <result column="crowd_version" jdbcType="BIGINT" property="crowdVersion"/>
        <result column="oss_uri" jdbcType="VARCHAR" property="ossUri"/>
        <result column="start_pos" jdbcType="BIGINT" property="startPos"/>
        <result column="end_pos" jdbcType="BIGINT" property="endPos"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="created_op" jdbcType="VARCHAR" property="createdOp"/>
        <result column="updated_op" jdbcType="VARCHAR" property="updatedOp"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        id
        , crowd_id, crowd_version, oss_uri, start_pos, end_pos, created_time, updated_time, 
    d_flag, created_op, updated_op
    </sql>
    
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_slice
        where id = #{id,jdbcType=BIGINT}
    </select>
    
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update crowd_slice set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>
    
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo" useGeneratedKeys="true">
        insert into crowd_slice (crowd_id, crowd_version, oss_uri,
                                 start_pos, end_pos, created_time,
                                 updated_time, d_flag, created_op,
                                 updated_op)
        values (#{crowdId,jdbcType=BIGINT}, #{crowdVersion,jdbcType=BIGINT}, #{ossUri,jdbcType=VARCHAR},
                #{startPos,jdbcType=BIGINT}, #{endPos,jdbcType=BIGINT}, #{createdTime,jdbcType=TIMESTAMP},
                #{updatedTime,jdbcType=TIMESTAMP}, #{dFlag,jdbcType=SMALLINT}, #{createdOp,jdbcType=VARCHAR},
                #{updatedOp,jdbcType=VARCHAR})
    </insert>
    
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo"
            useGeneratedKeys="true">
        insert into crowd_slice
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdVersion != null">
                crowd_version,
            </if>
            <if test="ossUri != null">
                oss_uri,
            </if>
            <if test="startPos != null">
                start_pos,
            </if>
            <if test="endPos != null">
                end_pos,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                #{crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="ossUri != null">
                #{ossUri,jdbcType=VARCHAR},
            </if>
            <if test="startPos != null">
                #{startPos,jdbcType=BIGINT},
            </if>
            <if test="endPos != null">
                #{endPos,jdbcType=BIGINT},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    
    <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo">
        update crowd_slice
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                crowd_version = #{crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="ossUri != null">
                oss_uri = #{ossUri,jdbcType=VARCHAR},
            </if>
            <if test="startPos != null">
                start_pos = #{startPos,jdbcType=BIGINT},
            </if>
            <if test="endPos != null">
                end_pos = #{endPos,jdbcType=BIGINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    
    <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdSliceDo">
        update crowd_slice
        set crowd_id      = #{crowdId,jdbcType=BIGINT},
            crowd_version = #{crowdVersion,jdbcType=BIGINT},
            oss_uri       = #{ossUri,jdbcType=VARCHAR},
            start_pos     = #{startPos,jdbcType=BIGINT},
            end_pos       = #{endPos,jdbcType=BIGINT},
            created_time  = #{createdTime,jdbcType=TIMESTAMP},
            updated_time  = #{updatedTime,jdbcType=TIMESTAMP},
            d_flag        = #{dFlag,jdbcType=SMALLINT},
            created_op    = #{createdOp,jdbcType=VARCHAR},
            updated_op    = #{updatedOp,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByCrowdVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_slice
        where crowd_id = #{crowdId,jdbcType=BIGINT} and crowd_version = #{crowdVersion,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="existTodaySliceCrowdVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_slice
        where crowd_id = #{crowdId,jdbcType=BIGINT} and crowd_version >= #{crowdVersion,jdbcType=BIGINT} and d_flag = 0 limit 1
    </select>
</mapper>