<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdInfoVersionMapper">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="crowd_id" jdbcType="BIGINT" property="crowdId"/>
        <result column="crowd_version" jdbcType="BIGINT" property="crowdVersion"/>
        <result column="crowd_oss_folder" jdbcType="VARCHAR" property="crowdOssFolder"/>
        <result column="crowd_oss_file" jdbcType="VARCHAR" property="crowdOssFile"/>
        <result column="crowd_size" jdbcType="BIGINT" property="crowdSize"/>
        <result column="raw_version" jdbcType="VARCHAR" property="rawVersion"/>
        <result column="slice_status" jdbcType="SMALLINT" property="sliceStatus"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="created_op" jdbcType="VARCHAR" property="createdOp"/>
        <result column="updated_op" jdbcType="VARCHAR" property="updatedOp"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , crowd_id, crowd_version, crowd_oss_folder, crowd_oss_file, crowd_size, raw_version,
    slice_status, d_flag, created_op, updated_op, created_time, updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info_version
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update  crowd_info_version set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo" useGeneratedKeys="true">
        insert into crowd_info_version (crowd_id, crowd_version, crowd_oss_folder,
                                        crowd_oss_file, crowd_size, raw_version,
                                        slice_status, d_flag, created_op,
                                        updated_op, created_time, updated_time)
        values (#{crowdId,jdbcType=BIGINT}, #{crowdVersion,jdbcType=BIGINT}, #{crowdOssFolder,jdbcType=VARCHAR},
                #{crowdOssFile,jdbcType=VARCHAR}, #{crowdSize,jdbcType=BIGINT}, #{rawVersion,jdbcType=VARCHAR},
                #{sliceStatus,jdbcType=SMALLINT}, #{dFlag,jdbcType=SMALLINT}, #{createdOp,jdbcType=VARCHAR},
                #{updatedOp,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo" useGeneratedKeys="true">
        insert into crowd_info_version
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                crowd_id,
            </if>
            <if test="crowdVersion != null">
                crowd_version,
            </if>
            <if test="crowdOssFolder != null">
                crowd_oss_folder,
            </if>
            <if test="crowdOssFile != null">
                crowd_oss_file,
            </if>
            <if test="crowdSize != null">
                crowd_size,
            </if>
            <if test="rawVersion != null">
                raw_version,
            </if>
            <if test="sliceStatus != null">
                slice_status,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="crowdId != null">
                #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                #{crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="crowdOssFolder != null">
                #{crowdOssFolder,jdbcType=VARCHAR},
            </if>
            <if test="crowdOssFile != null">
                #{crowdOssFile,jdbcType=VARCHAR},
            </if>
            <if test="crowdSize != null">
                #{crowdSize,jdbcType=BIGINT},
            </if>
            <if test="rawVersion != null">
                #{rawVersion,jdbcType=VARCHAR},
            </if>
            <if test="sliceStatus != null">
                #{sliceStatus,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo">
        update crowd_info_version
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="crowdVersion != null">
                crowd_version = #{crowdVersion,jdbcType=BIGINT},
            </if>
            <if test="crowdOssFolder != null">
                crowd_oss_folder = #{crowdOssFolder,jdbcType=VARCHAR},
            </if>
            <if test="crowdOssFile != null">
                crowd_oss_file = #{crowdOssFile,jdbcType=VARCHAR},
            </if>
            <if test="crowdSize != null">
                crowd_size = #{crowdSize,jdbcType=BIGINT},
            </if>
            <if test="rawVersion != null">
                raw_version = #{rawVersion,jdbcType=VARCHAR},
            </if>
            <if test="sliceStatus != null">
                slice_status = #{sliceStatus,jdbcType=SMALLINT},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.xftech.cdp.distribute.crowd.repository.model.CrowdInfoVersionDo">
        update crowd_info_version
        set crowd_id         = #{crowdId,jdbcType=BIGINT},
            crowd_version    = #{crowdVersion,jdbcType=BIGINT},
            crowd_oss_folder = #{crowdOssFolder,jdbcType=VARCHAR},
            crowd_oss_file   = #{crowdOssFile,jdbcType=VARCHAR},
            crowd_size       = #{crowdSize,jdbcType=BIGINT},
            raw_version      = #{rawVersion,jdbcType=VARCHAR},
            slice_status     = #{sliceStatus,jdbcType=SMALLINT},
            d_flag           = #{dFlag,jdbcType=SMALLINT},
            created_op       = #{createdOp,jdbcType=VARCHAR},
            updated_op       = #{updatedOp,jdbcType=VARCHAR},
            created_time     = #{createdTime,jdbcType=TIMESTAMP},
            updated_time     = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectMaxSliceVersion" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info_version
        where crowd_id = #{crowdId,jdbcType=BIGINT} and d_flag = 0 and slice_status = 2 order by crowd_version desc limit 1
    </select>

    <select id="selectMaxCrowdVersion" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_info_version
        where crowd_id = #{crowdId,jdbcType=BIGINT} and d_flag = 0 order by crowd_version desc limit 1
    </select>
</mapper>