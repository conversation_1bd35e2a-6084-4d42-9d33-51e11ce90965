<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdPushBatch">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo">
        <id column="id" property="id"/>
        <result column="xxl_job_id" property="xxlJobId"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_group_id" property="strategyGroupId"/>
        <result column="strategy_market_channel_id" property="strategyMarketChannelId"/>
        <result column="strategy_exec_log_id" property="strategyExecLogId"/>
        <result column="market_channel" property="marketChannel"/>
        <result column="template_id" property="templateId"/>
        <result column="batch_num" property="batchNum"/>
        <result column="batch_total" property="batchTotal"/>
        <result column="sms_send_count" property="smsSendCount"/>
        <result column="succ_count" property="succCount"/>
        <result column="fail_count" property="failCount"/>
        <result column="used_count" property="usedCount"/>
        <result column="send_code" property="sendCode"/>
        <result column="send_msg" property="sendMsg"/>
        <result column="query_status" property="queryStatus"/>
        <result column="exec_type" property="execType"/>
        <result column="batch_status" property="batchStatus"/>
        <result column="mobile_batch" property="mobileBatch"/>
        <result column="detail_table_no" property="detailTableNo"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
    </resultMap>

    <resultMap id="strategyExecLogDTO" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo">
        <result column="receive_count" property="receiveCount"/>
        <result column="supplier_count" property="supplierCount"/>
        <result column="actual_count" property="actualCount"/>
        <result column="succ_count" property="succCount"/>
        <result column="used_count" property="usedCount"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , xxl_job_id, strategy_id, strategy_group_id, strategy_market_channel_id, strategy_exec_log_id, market_channel, template_id, batch_num, batch_total, sms_send_count, succ_count, fail_count, used_count, send_code, send_msg, query_status, exec_type, batch_status, mobile_batch, detail_table_no, created_time, updated_time
    </sql>

    <sql id="Base_Column_List1">
        id, xxl_job_id, strategy_id, strategy_group_id, strategy_market_channel_id, strategy_exec_log_id, market_channel, template_id, batch_num, batch_total, sms_send_count, succ_count, fail_count, used_count, send_code, send_msg, query_status, exec_type, batch_status, detail_table_no, created_time, updated_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_push_batch
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByStrategyExecLogId" resultMap="BaseResultMap">
        select id,
               strategy_id,
               strategy_exec_log_id,
               succ_count,
               fail_count
        from crowd_push_batch
        where strategy_exec_log_id = #{execLogId}
          and query_status &gt; -1
    </select>

    <select id="selectUnFinishQuery" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List1"/>
        from crowd_push_batch
        where id &gt; #{id}
        and market_channel in (1, 3)
        and query_status in (0, 1)
        limit #{pageSize}
    </select>

    <select id="selectFailBatchByStrategyExecLogId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_push_batch
        where strategy_exec_log_id = #{strategyExecLogId}
        and query_status = #{queryStatus}
        and batch_status = #{batchStatus}
    </select>

    <select id="selectExistUnFinishBatch" resultType="integer">
        select 1
        from crowd_push_batch
        where strategy_exec_log_id = #{strategyExecLogId}
        and query_status in
        <foreach collection="queryStatus" item="status" open="(" separator="," close=")">
            #{status}
        </foreach>
        limit 1
    </select>

    <select id="sumBatchSuccCount" resultType="integer">
        select ifnull(sum(succ_count), 0)
        from crowd_push_batch
        where strategy_exec_log_id = #{strategyExecLogId}
    </select>

    <!--  短信系统接收人数、短信系统推送人数（供应商接收人数）、  -->
    <select id="countCrowdPushCount" resultMap="strategyExecLogDTO">
        select ifnull(sum(batch_total), 0)             as receive_count,
               ifnull(sum(sms_send_count), 0)          as supplier_count,
               ifnull(sum(succ_count + fail_count), 0) as actual_count,
               ifnull(sum(succ_count), 0)              as succ_count,
               ifnull(sum(used_count), 0)              as used_count
        from crowd_push_batch
        where strategy_exec_log_id = #{strategyExecLogId}
          and query_status > -1
    </select>

    <select id="selectTotalByExecLogId" resultType="integer">
        select count(1)
        from crowd_push_batch
        where strategy_exec_log_id = #{strategyExecLogId}
    </select>

    <select id="selectByChannelAndBatchNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_push_batch
        where market_channel = #{channel} and batch_num = #{batchNum}
    </select>

    <select id="selectTableNoByBatchNum" resultType="string">
        select detail_table_no
        from crowd_push_batch
        where batch_num = #{batchNum} limit 1
    </select>

    <select id="selectByBatchNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_push_batch
        where batch_num = #{batchNum} order by id desc limit 1
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo" useGeneratedKeys="true">
        insert into crowd_push_batch
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="xxlJobId != null">xxl_job_id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="strategyMarketChannelId != null">strategy_market_channel_id,</if>
            <if test="strategyExecLogId != null">strategy_exec_log_id,</if>
            <if test="marketChannel != null">market_channel,</if>
            <if test="templateId != null">template_id,</if>
            <if test="batchNum != null">batch_num,</if>
            <if test="batchTotal != null">batch_total,</if>
            <if test="smsSendCount != null">sms_send_count,</if>
            <if test="succCount != null">succ_count,</if>
            <if test="failCount != null">fail_count,</if>
            <if test="usedCount != null">used_count,</if>
            <if test="sendCode != null">send_code,</if>
            <if test="sendMsg != null">send_msg,</if>
            <if test="queryStatus != null">query_status,</if>
            <if test="execType != null">exec_type,</if>
            <if test="batchStatus != null">batch_status,</if>
            <if test="mobileBatch != null">mobile_batch,</if>
            <if test="detailTableNo != null">detail_table_no,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="xxlJobId != null">#{xxlJobId,jdbcType=INTEGER},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="strategyMarketChannelId != null">#{strategyMarketChannelId,jdbcType=BIGINT},</if>
            <if test="strategyExecLogId != null">#{strategyExecLogId,jdbcType=BIGINT},</if>
            <if test="marketChannel != null">#{marketChannel,jdbcType=SMALLINT},</if>
            <if test="templateId != null">#{templateId,jdbcType=VARCHAR},</if>
            <if test="batchNum != null">#{batchNum,jdbcType=VARCHAR},</if>
            <if test="batchTotal != null">#{batchTotal,jdbcType=INTEGER},</if>
            <if test="smsSendCount != null">#{smsSendCount,jdbcType=INTEGER},</if>
            <if test="succCount != null">#{succCount,jdbcType=INTEGER},</if>
            <if test="failCount != null">#{failCount,jdbcType=INTEGER},</if>
            <if test="usedCount != null">#{usedCount,jdbcType=INTEGER},</if>
            <if test="sendCode != null">#{sendCode,jdbcType=VARCHAR},</if>
            <if test="sendMsg != null">#{sendMsg,jdbcType=VARCHAR},</if>
            <if test="queryStatus != null">#{queryStatus,jdbcType=SMALLINT},</if>
            <if test="execType != null">#{execType,jdbcType=SMALLINT},</if>
            <if test="batchStatus != null">#{batchStatus,jdbcType=SMALLINT},</if>
            <if test="mobileBatch != null">#{mobileBatch,jdbcType=VARCHAR},</if>
            <if test="detailTableNo != null">#{detailTableNo,jdbcType=INTEGER},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdPushBatchDo">
        update crowd_push_batch
        <set>
            <if test="xxlJobId != null">
                xxl_job_id = #{xxlJobId,jdbcType=INTEGER},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyMarketChannelId != null">
                strategy_market_channel_id = #{strategyMarketChannelId,jdbcType=BIGINT},
            </if>
            <if test="strategyExecLogId != null">
                strategy_exec_log_id = #{strategyExecLogId,jdbcType=BIGINT},
            </if>
            <if test="marketChannel != null">
                market_channel = #{marketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="batchNum != null">
                batch_num = #{batchNum,jdbcType=VARCHAR},
            </if>
            <if test="batchTotal != null">
                batch_total = #{batchTotal,jdbcType=INTEGER},
            </if>
            <if test="smsSendCount != null">
                sms_send_count = #{smsSendCount,jdbcType=INTEGER},
            </if>
            <if test="succCount != null">
                succ_count = #{succCount,jdbcType=INTEGER},
            </if>
            <if test="failCount != null">
                fail_count = #{failCount,jdbcType=INTEGER},
            </if>
            <if test="usedCount != null">
                used_count = #{usedCount,jdbcType=INTEGER},
            </if>
            <if test="sendCode != null">
                send_code = #{sendCode,jdbcType=VARCHAR},
            </if>
            <if test="sendMsg != null">
                send_msg = #{sendMsg,jdbcType=VARCHAR},
            </if>
            <if test="queryStatus != null">
                query_status = #{queryStatus,jdbcType=SMALLINT},
            </if>
            <if test="execType != null">
                exec_type = #{execType,jdbcType=SMALLINT},
            </if>
            <if test="batchStatus != null">
                batch_status = #{batchStatus,jdbcType=SMALLINT},
            </if>
            <if test="mobileBatch != null">
                mobile_batch = #{mobileBatch,jdbcType=VARCHAR},
            </if>
            <if test="detailTableNo != null">
                detail_table_no = #{detailTableNo,jdbcType=INTEGER},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
