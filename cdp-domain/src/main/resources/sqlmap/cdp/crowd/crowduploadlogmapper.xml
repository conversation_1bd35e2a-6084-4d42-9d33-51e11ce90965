<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="crowdUploadLog">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdUploadLogDo">
        <id column="id" property="id"/>
        <result column="crowd_id" property="crowdId"/>
        <result column="file_name" property="fileName"/>
        <result column="oss_file_name" property="ossFileName"/>
        <result column="oss_url" property="ossUrl"/>
        <result column="parsing_time" property="parsingTime"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, crowd_id, file_name, oss_file_name, oss_url, parsing_time, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_upload_log
        where id = #{id,jdbcType=BIGINT}
        and d_flag = 0
    </select>

    <select id="selectByCrowdId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crowd_upload_log
        where crowd_id = #{crowdId}
        and d_flag = 0 order by id desc limit 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update crowd_upload_log
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="deleteByCrowdId">
        update crowd_upload_log
        set d_flag = 1
        where crowd_id = #{crowdId,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdUploadLogDo" useGeneratedKeys="true">
        insert into crowd_upload_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="crowdId != null">crowd_id,</if>
            <if test="fileName != null">file_name,</if>
            <if test="ossFileName != null">oss_file_name,</if>
            <if test="ossUrl != null">oss_url,</if>
            <if test="parsingTime != null">parsing_time,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="crowdId != null">#{crowdId,jdbcType=BIGINT},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="ossFileName != null">#{ossFileName,jdbcType=VARCHAR},</if>
            <if test="ossUrl != null">#{ossUrl,jdbcType=VARCHAR},</if>
            <if test="parsingTime != null">#{parsingTime,jdbcType=TIMESTAMP},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.crowd.po.CrowdUploadLogDo">
        update crowd_upload_log
        <set>
            <if test="crowdId != null">
                crowd_id = #{crowdId,jdbcType=BIGINT},
            </if>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="ossFileName != null">
                oss_file_name = #{ossFileName,jdbcType=VARCHAR},
            </if>
            <if test="ossUrl != null">
                oss_url = #{ossUrl,jdbcType=VARCHAR},
            </if>
            <if test="parsingTime != null">
                parsing_time = #{parsingTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
