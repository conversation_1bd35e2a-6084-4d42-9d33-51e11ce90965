<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="templateParamMapper">

    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.param.po.TemplateParamDo">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="paramKey" column="param_key" jdbcType="VARCHAR"/>
        <result property="demandSide" column="demand_side" jdbcType="VARCHAR"/>
        <result property="dFlag" column="d_flag" jdbcType="SMALLINT"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="createdOp" column="created_op" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="updatedOp" column="updated_op" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,name,description,
        param_key,demand_side,d_flag,
        created_time,created_op,updated_time,
        updated_op
    </sql>

    <select id="getAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from template_param
        where d_flag = 0
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from template_param
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectByPage" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/> from template_param where d_flag = 0
        <if test="name != null and name != ''">
            <bind name="nameTmp" value='name.replaceAll("%","/%")'/>
            AND name like concat('%',#{nameTmp},'%') escape '/'
        </if>
        <if test="id != null and id != ''">
            AND id = #{id}
        </if>
        <if test="paramKey != null and paramKey != '' ">
            <bind name="paramKeyTmp" value='paramKey.replaceAll("%","/%")'/>
            AND param_key like concat('%',#{paramKeyTmp},'%') escape '/'
        </if>
        <if test="demandSide != null and demandSide != ''">
            <bind name="demandSideTmp" value='demandSide.replaceAll("%","/%")'/>
            AND demand_side like concat('%',#{demandSideTmp},'%') escape '/'
        </if>
        <if test="startTime != null">
            AND created_time &gt;= #{startTime}
        </if>
        <if test="endTime != null">
            AND created_time &lt;= #{endTime}
        </if>
        order by created_time desc
    </select>

    <select id="selectByName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from template_param
        where `name` = #{name,jdbcType=VARCHAR} and d_flag = 0
    </select>

    <select id="selectByKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from template_param
        where `param_key` = #{paramKey,jdbcType=VARCHAR} and d_flag = 0
    </select>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.param.po.TemplateParamDo"
            useGeneratedKeys="true">
        insert into template_param
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="paramKey != null">param_key,</if>
            <if test="demandSide != null">demand_side,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="updatedOp != null">updated_op,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="paramKey != null">#{paramKey,jdbcType=VARCHAR},</if>
            <if test="demandSide != null">#{demandSide,jdbcType=VARCHAR},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.param.po.TemplateParamDo">
        update template_param
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="paramKey != null">
                param_key = #{paramKey,jdbcType=VARCHAR},
            </if>
            <if test="demandSide != null">
                demand_side = #{demandSide,jdbcType=VARCHAR},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
