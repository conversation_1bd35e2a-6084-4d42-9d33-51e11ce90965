<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyExecLog">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo">
        <id column="id" property="id"/>
        <result column="strategy_id" property="strategyId"/>
        <result column="strategy_group_id" property="strategyGroupId"/>
        <result column="strategy_group_name" property="strategyGroupName"/>
        <result column="strategy_market_channel_id" property="strategyMarketChannelId"/>
        <result column="strategy_market_channel" property="strategyMarketChannel"/>
        <result column="template_id" property="templateId"/>
        <result column="exec_status" property="execStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="group_count" property="groupCount"/>
        <result column="exec_count" property="execCount"/>
        <result column="send_count" property="sendCount"/>
        <result column="receive_count" property="receiveCount"/>
        <result column="supplier_count" property="supplierCount"/>
        <result column="actual_count" property="actualCount"/>
        <result column="succ_count" property="succCount"/>
        <result column="used_count" property="usedCount"/>
        <result column="exec_time" property="execTime"/>
        <result column="finish_exec_time" property="finishExecTime"/>
        <result column="retry_id" property="retryId"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="d_flag" property="dFlag"/>
        <result column="created_op" property="createdOp"/>
        <result column="updated_op" property="updatedOp"/>
        <result column="ext_detail" jdbcType="VARCHAR" property="extDetail" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, strategy_id, strategy_group_id, strategy_group_name, strategy_market_channel_id, strategy_market_channel, template_id, exec_status, fail_reason, group_count, exec_count, send_count, receive_count, supplier_count, actual_count, succ_count, used_count, exec_time, finish_exec_time, retry_id, created_time, updated_time, d_flag, created_op, updated_op
    </sql>

    <select id="selectRetryList" resultMap="BaseResultMap">
        SELECT t1.*
        FROM strategy_exec_log t1
                 INNER JOIN (SELECT MAX(id) AS id
                             FROM strategy_exec_log
                             WHERE strategy_id = #{strategyId}
                               AND exec_status = 2
                             GROUP BY strategy_market_channel_id) t2 ON t1.id = t2.id
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </select>

    <select id="selectByStrategyIdAndChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId} and strategy_market_channel = #{strategyMarketChannel} and d_flag = 0
        order by exec_time desc
    </select>

    <select id="selectAllByStrategyIdAndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        and exec_time between #{startTime} and #{endTime}
        and d_flag = 0
    </select>

    <select id="selectByStrategyIdAndChannelAndExecStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        and strategy_market_channel_id = #{marketChannelId}
        and exec_status = #{execStatus}
        <if test="execDate != null">
            and date(exec_time) = #{execDate}
        </if>
        and d_flag = 0
    </select>

    <select id="selectByStrategyIdAndExecTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        <if test="execStatus != null">
            and exec_status = #{execStatus}
        </if>
        and exec_time between #{validityBegin} and #{validityEnd}
        and d_flag = 0
        group by strategy_market_channel_id
    </select>

    <select id="selectByStrategyIdAndExecTimeUngrouped" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        <if test="execStatus != null">
            and exec_status = #{execStatus}
        </if>
        and exec_time between #{validityBegin} and #{validityEnd}
        and d_flag = 0
    </select>

    <select id="selectByGroupIdAndExecTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_group_id = #{groupId}
        <if test="execStatus != null">
            and exec_status = #{execStatus}
        </if>
        and exec_time between #{validityBegin} and #{validityEnd}
        and d_flag = 0
        order by id desc
    </select>

    <select id="selectEventExecutingRecord" resultMap="BaseResultMap">
        select sel.*
        from strategy_exec_log sel
                 inner join strategy s on sel.strategy_id = s.id
        where s.send_ruler = 2
          and sel.d_flag = 0
          and sel.exec_status = 0
          and sel.exec_time between #{validityBegin} and #{validityEnd}
    </select>

    <select id="selectLatestStatusByStrategyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        order by id desc
        limit 1
    </select>

    <select id="selectByGroupIdAndChannelAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_group_id = #{groupId}
        and strategy_market_channel = #{marketChannel}
        and date(created_time) = #{date}
    </select>

    <select id="selectByStrategyIdAndChannelAndDate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        and strategy_market_channel = #{marketChannel}
        and date(created_time) = #{date}
    </select>

    <select id="getByStrategyIdAndExecTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        and date(exec_time) = #{date}
        and d_flag = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update strategy_exec_log
        set d_flag = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo"
            useGeneratedKeys="true">
        insert into strategy_exec_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="strategyId != null">strategy_id,</if>
            <if test="strategyGroupId != null">strategy_group_id,</if>
            <if test="strategyGroupName != null">strategy_group_name,</if>
            <if test="strategyMarketChannelId != null">strategy_market_channel_id,</if>
            <if test="strategyMarketChannel != null">strategy_market_channel,</if>
            <if test="templateId != null">template_id,</if>
            <if test="execStatus != null">exec_status,</if>
            <if test="failReason != null">fail_reason,</if>
            <if test="groupCount != null">group_count,</if>
            <if test="execCount != null">exec_count,</if>
            <if test="sendCount != null">send_count,</if>
            <if test="receiveCount != null">receive_count,</if>
            <if test="supplierCount != null">supplier_count,</if>
            <if test="actualCount != null">actual_count,</if>
            <if test="succCount != null">succ_count,</if>
            <if test="usedCount != null">used_count,</if>
            <if test="execTime != null">exec_time,</if>
            <if test="finishExecTime != null">finish_exec_time,</if>
            <if test="retryId != null">retry_id,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="dFlag != null">d_flag,</if>
            <if test="createdOp != null">created_op,</if>
            <if test="updatedOp != null">updated_op,</if>
            <if test="extDetail != null">ext_detail,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="strategyId != null">#{strategyId,jdbcType=BIGINT},</if>
            <if test="strategyGroupId != null">#{strategyGroupId,jdbcType=BIGINT},</if>
            <if test="strategyGroupName != null">#{strategyGroupName,jdbcType=VARCHAR},</if>
            <if test="strategyMarketChannelId != null">#{strategyMarketChannelId,jdbcType=BIGINT},</if>
            <if test="strategyMarketChannel != null">#{strategyMarketChannel,jdbcType=SMALLINT},</if>
            <if test="templateId != null">#{templateId,jdbcType=VARCHAR},</if>
            <if test="execStatus != null">#{execStatus,jdbcType=SMALLINT},</if>
            <if test="failReason != null">#{failReason,jdbcType=VARCHAR},</if>
            <if test="groupCount != null">#{groupCount,jdbcType=INTEGER},</if>
            <if test="execCount != null">#{execCount,jdbcType=INTEGER},</if>
            <if test="sendCount != null">#{sendCount,jdbcType=INTEGER},</if>
            <if test="receiveCount != null">#{receiveCount,jdbcType=INTEGER},</if>
            <if test="supplierCount != null">#{supplierCount,jdbcType=INTEGER},</if>
            <if test="actualCount != null">#{actualCount,jdbcType=INTEGER},</if>
            <if test="succCount != null">#{succCount,jdbcType=INTEGER},</if>
            <if test="usedCount != null">#{usedCount,jdbcType=INTEGER},</if>
            <if test="execTime != null">#{execTime,jdbcType=TIMESTAMP},</if>
            <if test="finishExecTime != null">#{finishExecTime,jdbcType=TIMESTAMP},</if>
            <if test="retryId != null">#{retryId,jdbcType=BIGINT},</if>
            <if test="createdTime != null">#{createdTime,jdbcType=TIMESTAMP},</if>
            <if test="updatedTime != null">#{updatedTime,jdbcType=TIMESTAMP},</if>
            <if test="dFlag != null">#{dFlag,jdbcType=SMALLINT},</if>
            <if test="createdOp != null">#{createdOp,jdbcType=VARCHAR},</if>
            <if test="updatedOp != null">#{updatedOp,jdbcType=VARCHAR},</if>
            <if test="extDetail != null">#{extDetail,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo">
        update strategy_exec_log
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupId != null">
                strategy_group_id = #{strategyGroupId,jdbcType=BIGINT},
            </if>
            <if test="strategyGroupName != null">
                strategy_group_name = #{strategyGroupName,jdbcType=VARCHAR},
            </if>
            <if test="strategyMarketChannelId != null">
                strategy_market_channel_id = #{strategyMarketChannelId,jdbcType=BIGINT},
            </if>
            <if test="strategyMarketChannel != null">
                strategy_market_channel = #{strategyMarketChannel,jdbcType=SMALLINT},
            </if>
            <if test="templateId != null">
                template_id = #{templateId,jdbcType=VARCHAR},
            </if>
            <if test="execStatus != null">
                exec_status = #{execStatus,jdbcType=SMALLINT},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="groupCount != null">
                group_count = #{groupCount,jdbcType=INTEGER},
            </if>
            <if test="execCount != null">
                exec_count = #{execCount,jdbcType=INTEGER},
            </if>
            <if test="sendCount != null">
                send_count = #{sendCount,jdbcType=INTEGER},
            </if>
            <if test="receiveCount != null">
                receive_count = #{receiveCount,jdbcType=INTEGER},
            </if>
            <if test="supplierCount != null">
                supplier_count = #{supplierCount,jdbcType=INTEGER},
            </if>
            <if test="actualCount != null">
                actual_count = #{actualCount,jdbcType=INTEGER},
            </if>
            <if test="succCount != null">
                succ_count = #{succCount,jdbcType=INTEGER},
            </if>
            <if test="usedCount != null">
                used_count = #{usedCount,jdbcType=INTEGER},
            </if>
            <if test="execTime != null">
                exec_time = #{execTime,jdbcType=TIMESTAMP},
            </if>
            <if test="finishExecTime != null">
                finish_exec_time = #{finishExecTime,jdbcType=TIMESTAMP},
            </if>
            <if test="retryId != null">
                retry_id = #{retryId,jdbcType=BIGINT},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <select id="selectByStrategyIdByChannel" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_id = #{strategyId}
        <if test="strategyMarketChannel != null">
            AND strategy_market_channel = #{strategyMarketChannel}
        </if>
        AND d_flag = 0
        order by exec_time desc
    </select>

    <select id="selectPageByGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where strategy_group_id = #{groupId}
        AND d_flag = 0
        order by exec_time desc
    </select>

    <select id="selectByStrategyIdsAndExecTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_exec_log
        where d_flag = 0
        <if test="strategyIds !=null and strategyIds.size()>0">
            and strategy_id in
            <foreach collection="strategyIds" item="strategyId" open="(" close=")" separator=",">
                #{strategyId}
            </foreach>
        </if>
        and exec_time &gt;= #{startTime}
        and exec_time &lt;= #{endTime}
    </select>
</mapper>
