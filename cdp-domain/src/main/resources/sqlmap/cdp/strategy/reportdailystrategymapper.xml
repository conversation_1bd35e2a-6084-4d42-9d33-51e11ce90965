<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="reportDailyStrategyMapper">
  <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="strategy_id" jdbcType="BIGINT" property="strategyId" />
    <result column="strategy_name" jdbcType="VARCHAR" property="strategyName" />
    <result column="strategy_group_id" jdbcType="BIGINT" property="strategyGroupId" />
    <result column="strategy_group_name" jdbcType="VARCHAR" property="strategyGroupName" />
    <result column="strategy_market_channel_id" jdbcType="BIGINT" property="strategyMarketChannelId" />
    <result column="strategy_market_channel" jdbcType="INTEGER" property="strategyMarketChannel" />
    <result column="exec_start_time" jdbcType="TIMESTAMP" property="execStartTime" />
    <result column="exec_end_time" jdbcType="TIMESTAMP" property="execEndTime" />
    <result column="exec_status" jdbcType="INTEGER" property="execStatus" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="send_count" jdbcType="INTEGER" property="sendCount" />
    <result column="succ_count" jdbcType="INTEGER" property="succCount" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="d_flag" jdbcType="INTEGER" property="dFlag" />
    <result column="created_op" jdbcType="VARCHAR" property="createdOp" />
    <result column="updated_op" jdbcType="VARCHAR" property="updatedOp" />
  </resultMap>
  <sql id="Base_Column_List">
    `id`, `date`, `strategy_id`, `strategy_name`, `strategy_group_id`, `strategy_group_name`, 
    `strategy_market_channel_id`, `strategy_market_channel`, `exec_start_time`, `exec_end_time`, 
    `exec_status`, `fail_reason`, `send_count`, `succ_count`, `created_time`, `updated_time`, 
    `d_flag`, `created_op`, `updated_op`
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_daily_strategy
    where `id` = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insertSelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo">
    insert into report_daily_strategy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        `id`,
      </if>
      <if test="date != null">
        `date`,
      </if>
      <if test="strategyId != null">
        `strategy_id`,
      </if>
      <if test="strategyName != null">
        `strategy_name`,
      </if>
      <if test="strategyGroupId != null">
        `strategy_group_id`,
      </if>
      <if test="strategyGroupName != null">
        `strategy_group_name`,
      </if>
      <if test="strategyMarketChannelId != null">
        `strategy_market_channel_id`,
      </if>
      <if test="strategyMarketChannel != null">
        `strategy_market_channel`,
      </if>
      <if test="execStartTime != null">
        `exec_start_time`,
      </if>
      <if test="execEndTime != null">
        `exec_end_time`,
      </if>
      <if test="execStatus != null">
        `exec_status`,
      </if>
      <if test="failReason != null">
        `fail_reason`,
      </if>
      <if test="sendCount != null">
        `send_count`,
      </if>
      <if test="succCount != null">
        `succ_count`,
      </if>
      <if test="createdTime != null">
        `created_time`,
      </if>
      <if test="updatedTime != null">
        `updated_time`,
      </if>
      <if test="dFlag != null">
        `d_flag`,
      </if>
      <if test="createdOp != null">
        `created_op`,
      </if>
      <if test="updatedOp != null">
        `updated_op`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="strategyId != null">
        #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="strategyGroupId != null">
        #{strategyGroupId,jdbcType=BIGINT},
      </if>
      <if test="strategyGroupName != null">
        #{strategyGroupName,jdbcType=VARCHAR},
      </if>
      <if test="strategyMarketChannelId != null">
        #{strategyMarketChannelId,jdbcType=BIGINT},
      </if>
      <if test="strategyMarketChannel != null">
        #{strategyMarketChannel,jdbcType=INTEGER},
      </if>
      <if test="execStartTime != null">
        #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null">
        #{sendCount,jdbcType=INTEGER},
      </if>
      <if test="succCount != null">
        #{succCount,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        #{updatedOp,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo">
    update report_daily_strategy
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        `strategy_name` = #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="strategyGroupId != null">
        `strategy_group_id` = #{strategyGroupId,jdbcType=BIGINT},
      </if>
      <if test="strategyGroupName != null">
        `strategy_group_name` = #{strategyGroupName,jdbcType=VARCHAR},
      </if>
      <if test="strategyMarketChannelId != null">
        `strategy_market_channel_id` = #{strategyMarketChannelId,jdbcType=BIGINT},
      </if>
      <if test="strategyMarketChannel != null">
        `strategy_market_channel` = #{strategyMarketChannel,jdbcType=INTEGER},
      </if>
      <if test="execStartTime != null">
        `exec_start_time` = #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        `exec_end_time` = #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        `exec_status` = #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        `fail_reason` = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null">
        `send_count` = #{sendCount,jdbcType=INTEGER},
      </if>
      <if test="succCount != null">
        `succ_count` = #{succCount,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where `id` = #{id,jdbcType=BIGINT}
  </update>

  <select id="existReportDailyStrategy" resultType="integer">
    select count(*)
    from report_daily_strategy
    where strategy_market_channel_id = #{strategyMarketChannelId}
      and date(date) = current_date()
  </select>

  <update id="updateByDateAndChannelId" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.ReportDailyStrategyDo">
    update report_daily_strategy
    <set>
      <if test="date != null">
        `date` = #{date,jdbcType=DATE},
      </if>
      <if test="strategyId != null">
        `strategy_id` = #{strategyId,jdbcType=BIGINT},
      </if>
      <if test="strategyName != null">
        `strategy_name` = #{strategyName,jdbcType=VARCHAR},
      </if>
      <if test="strategyGroupId != null">
        `strategy_group_id` = #{strategyGroupId,jdbcType=BIGINT},
      </if>
      <if test="strategyGroupName != null">
        `strategy_group_name` = #{strategyGroupName,jdbcType=VARCHAR},
      </if>
      <if test="strategyMarketChannelId != null">
        `strategy_market_channel_id` = #{strategyMarketChannelId,jdbcType=BIGINT},
      </if>
      <if test="strategyMarketChannel != null">
        `strategy_market_channel` = #{strategyMarketChannel,jdbcType=INTEGER},
      </if>
      <if test="execStartTime != null">
        `exec_start_time` = #{execStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execEndTime != null">
        `exec_end_time` = #{execEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="execStatus != null">
        `exec_status` = #{execStatus,jdbcType=INTEGER},
      </if>
      <if test="failReason != null">
        `fail_reason` = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="sendCount != null">
        `send_count` = #{sendCount,jdbcType=INTEGER},
      </if>
      <if test="succCount != null">
        `succ_count` = #{succCount,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        `created_time` = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        `updated_time` = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dFlag != null">
        `d_flag` = #{dFlag,jdbcType=INTEGER},
      </if>
      <if test="createdOp != null">
        `created_op` = #{createdOp,jdbcType=VARCHAR},
      </if>
      <if test="updatedOp != null">
        `updated_op` = #{updatedOp,jdbcType=VARCHAR},
      </if>
    </set>
    where strategy_market_channel_id = #{strategyMarketChannelId}
    and date(date) = current_date()
  </update>

  <select id="selectTodayFail" resultMap="BaseResultMap">
      select *
      from report_daily_strategy
      where status = 2
        and date (date) = current_date ();
  </select>

  <select id="selectToday" resultMap="BaseResultMap">
    select *
    from report_daily_strategy
    where date (date) = current_date ();
  </select>
</mapper>