<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="decisionRecordMapper">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.DecisionRecordDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="app" jdbcType="VARCHAR" property="app"/>
        <result column="inner_app" jdbcType="VARCHAR" property="innerApp"/>
        <result column="os" jdbcType="VARCHAR" property="os"/>
        <result column="app_user_id" jdbcType="BIGINT" property="appUserId"/>
        <result column="utm_source" jdbcType="VARCHAR" property="utmSource"/>
        <result column="event_name" jdbcType="VARCHAR" property="eventName"/>
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime"/>
        <result column="trigger_datetime" jdbcType="TIMESTAMP" property="triggerDatetime"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount"/>
        <result column="message_id" jdbcType="VARCHAR" property="messageId"/>
        <result column="trace_id" jdbcType="VARCHAR" property="traceId"/>
        <result column="union_id" jdbcType="VARCHAR" property="unionId"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="decision_result" jdbcType="INTEGER" property="decisionResult"/>
        <result column="fail_code" jdbcType="INTEGER" property="failCode"/>
        <result column="fail_reason" jdbcType="VARCHAR" property="failReason"/>
        <result column="decision_detail" jdbcType="VARCHAR" property="decisionDetail"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="decision_time" jdbcType="TIMESTAMP" property="decisionTime"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="current_utm_source" jdbcType="VARCHAR" property="currentUtmSource"/>
        <result column="source_type" jdbcType="VARCHAR" property="sourceType"/>
        <result column="engine_detail" jdbcType="LONGVARCHAR" property="engineDetail" />
        <result column="extr_data" jdbcType="OTHER" property="extrData"/>
        <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
        <result column="group_id" jdbcType="BIGINT" property="groupId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, app, inner_app, os, app_user_id, utm_source, event_name, register_time, trigger_datetime, amount, adjust_amount,
    message_id, trace_id, strategy_id, decision_result, fail_code, fail_reason, decision_detail, decision_time,
    created_time, record_type, current_utm_source, source_type, engine_detail, extr_data, group_name,group_id,union_id
    </sql>

    <insert id="insertDecisionRecord" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DecisionRecordDo" useGeneratedKeys="true">
        insert into ${tableName}
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="app != null">
                app,
            </if>
            <if test="innerApp != null">
                inner_app,
            </if>
            <if test="os != null">
                os,
            </if>
            <if test="appUserId != null">
                app_user_id,
            </if>
            <if test="utmSource != null">
                utm_source,
            </if>
            <if test="eventName != null">
                event_name,
            </if>
            <if test="registerTime != null">
                register_time,
            </if>
            <if test="triggerDatetime != null">
                trigger_datetime,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="adjustAmount != null">
                adjust_amount,
            </if>
            <if test="messageId != null">
                message_id,
            </if>
            <if test="traceId != null">
                trace_id,
            </if>
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="decisionResult != null">
                decision_result,
            </if>
            <if test="failCode != null">
                fail_code,
            </if>
            <if test="failReason != null">
                fail_reason,
            </if>
            <if test="decisionDetail != null">
                decision_detail,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="decisionTime != null">
                decision_time,
            </if>
            <if test="recordType != null">
                record_type,
            </if>
            <if test="currentUtmSource != null">
                current_utm_source,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="engineDetail != null">
                engine_detail,
            </if>
            <if test="extrData != null">
                extr_data,
            </if>
            <if test="groupName != null">
                group_name,
            </if>
            <if test="groupId != null">
                group_id,
            </if>
            <if test="unionId != null">
                union_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="app != null">
                #{app,jdbcType=VARCHAR},
            </if>
            <if test="innerApp != null">
                #{innerApp,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                #{os,jdbcType=VARCHAR},
            </if>
            <if test="appUserId != null">
                #{appUserId,jdbcType=BIGINT},
            </if>
            <if test="utmSource != null">
                #{utmSource,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null">
                #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="registerTime != null">
                #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="triggerDatetime != null">
                #{triggerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="adjustAmount != null">
                #{adjustAmount,jdbcType=DECIMAL},
            </if>
            <if test="messageId != null">
                #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="traceId != null">
                #{traceId,jdbcType=VARCHAR},
            </if>
            <if test="strategyId != null">
                #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="decisionResult != null">
                #{decisionResult,jdbcType=INTEGER},
            </if>
            <if test="failCode != null">
                #{failCode,jdbcType=INTEGER},
            </if>
            <if test="failReason != null">
                #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="decisionDetail != null">
                #{decisionDetail,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="decisionTime != null">
                #{decisionTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recordType != null">
                #{recordType,jdbcType=INTEGER},
            </if>
            <if test="currentUtmSource != null">
                #{currentUtmSource,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="engineDetail != null">
                #{engineDetail,jdbcType=LONGVARCHAR},
            </if>
            <if test="extrData != null">
                #{extrData,jdbcType=OTHER},
            </if>
            <if test="groupName != null">
                #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                #{groupId,jdbcType=BIGINT},
            </if>
            <if test="unionId != null">
                #{unionId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateDecisionRecord" parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.DecisionRecordDo">
        update decision_record_202305
        <set>
            <if test="app != null">
                app = #{app,jdbcType=VARCHAR},
            </if>
            <if test="innerApp != null">
                inner_app = #{innerApp,jdbcType=VARCHAR},
            </if>
            <if test="os != null">
                os = #{os,jdbcType=VARCHAR},
            </if>
            <if test="appUserId != null">
                app_user_id = #{appUserId,jdbcType=BIGINT},
            </if>
            <if test="utmSource != null">
                utm_source = #{utmSource,jdbcType=VARCHAR},
            </if>
            <if test="eventName != null">
                event_name = #{eventName,jdbcType=VARCHAR},
            </if>
            <if test="registerTime != null">
                register_time = #{registerTime,jdbcType=TIMESTAMP},
            </if>
            <if test="triggerDatetime != null">
                trigger_datetime = #{triggerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="adjustAmount != null">
                adjust_amount = #{adjustAmount,jdbcType=DECIMAL},
            </if>
            <if test="messageId != null">
                message_id = #{messageId,jdbcType=VARCHAR},
            </if>
            <if test="traceId != null">
                trace_id = #{traceId,jdbcType=VARCHAR},
            </if>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="decisionResult != null">
                decision_result = #{decisionResult,jdbcType=INTEGER},
            </if>
            <if test="failCode != null">
                fail_code = #{failCode,jdbcType=INTEGER},
            </if>
            <if test="failReason != null">
                fail_reason = #{failReason,jdbcType=VARCHAR},
            </if>
            <if test="decisionDetail != null">
                decision_detail = #{decisionDetail,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="decisionTime != null">
                decisionTime = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="recordType != null">
                record_type = #{recordType,jdbcType=INTEGER},
            </if>
            <if test="currentUtmSource != null">
                current_utm_source = #{currentUtmSource,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="engineDetail != null">
                engine_detail = #{engineDetail,jdbcType=VARCHAR},
            </if>
            <if test="extrData != null">
                extr_data = #{extrData,jdbcType=OTHER},
            </if>
            <if test="groupName != null">
                group_name = #{groupName,jdbcType=VARCHAR},
            </if>
            <if test="groupId != null">
                group_id = #{groupId,jdbcType=BIGINT},
            </if>
            <if test="unionId != null">
                union_id = #{unionId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询决策结果表的策略ID列表 -->
    <select id="queryDecisionRecordStrategyIds" resultType="Long">
        select strategy_id
        from ${tableName}
        where trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
        GROUP BY strategy_id
    </select>

    <!-- 统计策略决策总数 -->
    <select id="countDecisionStrategy" resultType="integer">
        select count(*)
        from ${tableName}
        where strategy_id = #{strategyId}
          and trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
          and record_type != 2
    </select>

    <!-- 统计策略分组 决策总数 -->
    <select id="countCurrentDecisionByGroupId" resultType="integer">
        select count(distinct app_user_id)
        from ${tableName}
        where strategy_id = #{strategyId}
          and group_id =  #{strategyGroupId}
          and trigger_datetime  &gt;= #{timeStart}
          and trigger_datetime  &lt; #{timeEnd}
          and record_type != 2
    </select>

    <!-- 统计决策用户流程数据 -->
    <select id="countDecisionUserFlowData" resultType="integer">
        select count(distinct app_user_id)
        from ${tableName}
        where
        strategy_id = #{strategyId}
        and trigger_datetime &gt;= #{startDate}
        and trigger_datetime &lt; #{endDate}
        <if test="failCode != null">
            and fail_code = #{failCode}
        </if>
    </select>

    <!-- 统计决策结果用户数 -->
    <select id="countDecisionResultUserNum" resultType="integer">
        select count(distinct app_user_id)
        from ${tableName}
        where strategy_id = #{strategyId}
          and trigger_datetime &gt;= #{startDate}
          and trigger_datetime &lt; #{endDate}
          and decision_result = #{decisionResult}
    </select>

</mapper>