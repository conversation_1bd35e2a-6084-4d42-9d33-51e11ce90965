<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="strategyengine">
    <resultMap id="BaseResultMap" type="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="strategy_id" jdbcType="BIGINT" property="strategyId"/>
        <result column="days" jdbcType="INTEGER" property="days"/>
        <result column="quota" jdbcType="INTEGER" property="quota"/>
        <result column="cool_down_period" jdbcType="INTEGER" property="coolDownPeriod"/>
        <result column="d_flag" jdbcType="SMALLINT" property="dFlag"/>
        <result column="created_op" jdbcType="VARCHAR" property="createdOp"/>
        <result column="updated_op" jdbcType="VARCHAR" property="updatedOp"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , strategy_id, `days`, quota, cool_down_period, d_flag, created_op, updated_op, created_time, updated_time
    </sql>

    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo"
            useGeneratedKeys="true">
        insert into strategy_engine_rate_limit (strategy_id, `days`, quota,
                                                cool_down_period, d_flag, created_op,
                                                updated_op, created_time, updated_time)
        values (#{strategyId,jdbcType=BIGINT}, #{days,jdbcType=INTEGER}, #{quota,jdbcType=INTEGER},
                #{coolDownPeriod,jdbcType=INTEGER}, #{dFlag,jdbcType=SMALLINT}, #{createdOp,jdbcType=VARCHAR},
                #{updatedOp,jdbcType=VARCHAR}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP})
    </insert>

    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo"
            useGeneratedKeys="true">
        insert into strategy_engine_rate_limit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                strategy_id,
            </if>
            <if test="days != null">
                `days`,
            </if>
            <if test="quota != null">
                quota,
            </if>
            <if test="coolDownPeriod != null">
                cool_down_period,
            </if>
            <if test="dFlag != null">
                d_flag,
            </if>
            <if test="createdOp != null">
                created_op,
            </if>
            <if test="updatedOp != null">
                updated_op,
            </if>
            <if test="createdTime != null">
                created_time,
            </if>
            <if test="updatedTime != null">
                updated_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="strategyId != null">
                #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="days != null">
                #{days,jdbcType=INTEGER},
            </if>
            <if test="quota != null">
                #{quota,jdbcType=INTEGER},
            </if>
            <if test="coolDownPeriod != null">
                #{coolDownPeriod,jdbcType=INTEGER},
            </if>
            <if test="dFlag != null">
                #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <select id="selectByStrategyId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from strategy_engine_rate_limit
        where strategy_id = #{strategyId,jdbcType=BIGINT} and d_flag = 0 order by id desc limit 1
    </select>

    <update id="updateByPrimaryKey"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo">
        update strategy_engine_rate_limit
        set strategy_id      = #{strategyId,jdbcType=BIGINT},
            `days`           = #{days,jdbcType=INTEGER},
            quota            = #{quota,jdbcType=INTEGER},
            cool_down_period = #{coolDownPeriod,jdbcType=INTEGER},
            d_flag           = #{dFlag,jdbcType=SMALLINT},
            created_op       = #{createdOp,jdbcType=VARCHAR},
            updated_op       = #{updatedOp,jdbcType=VARCHAR},
            created_time     = #{createdTime,jdbcType=TIMESTAMP},
            updated_time     = #{updatedTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </update>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyEngineRateLimitDo">
        update strategy_engine_rate_limit
        <set>
            <if test="strategyId != null">
                strategy_id = #{strategyId,jdbcType=BIGINT},
            </if>
            <if test="days != null">
                `days` = #{days,jdbcType=INTEGER},
            </if>
            <if test="quota != null">
                quota = #{quota,jdbcType=INTEGER},
            </if>
            <if test="coolDownPeriod != null">
                cool_down_period = #{coolDownPeriod,jdbcType=INTEGER},
            </if>
            <if test="dFlag != null">
                d_flag = #{dFlag,jdbcType=SMALLINT},
            </if>
            <if test="createdOp != null">
                created_op = #{createdOp,jdbcType=VARCHAR},
            </if>
            <if test="updatedOp != null">
                updated_op = #{updatedOp,jdbcType=VARCHAR},
            </if>
            <if test="createdTime != null">
                created_time = #{createdTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updatedTime != null">
                updated_time = #{updatedTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT} and d_flag = 0
    </update>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update strategy_engine_rate_limit set d_flag = 1 where id = #{id,jdbcType=BIGINT}
    </delete>

</mapper>
