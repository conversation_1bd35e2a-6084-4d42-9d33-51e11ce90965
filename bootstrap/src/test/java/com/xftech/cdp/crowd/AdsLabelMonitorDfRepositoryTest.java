package com.xftech.cdp.crowd;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.crowd.repository.AdsLabelMonitorDfRepository;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;

/**
 * @<NAME_EMAIL>
 */
public class AdsLabelMonitorDfRepositoryTest extends BaseTest {
    @Autowired
    AdsLabelMonitorDfRepository adsLabelMonitorDfRepository;

    @Test
    public void selectExistByDataDate() {
        Boolean result = adsLabelMonitorDfRepository.selectExistByDataDate(LocalDateTime.now(), "t_table");
        System.out.println(result);
    }
}