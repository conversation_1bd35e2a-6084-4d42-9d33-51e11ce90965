package com.xftech.cdp.domain.stat.repository;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.domain.stat.entity.StatRealtimeStrategyGroupDataEntity;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

public class StatRealtimeStrategyGroupDataRepositoryTest extends BaseTest {

    @Autowired
    private StatRealtimeStrategyGroupDataRepository groupDataRepository;

    @Test
    public void saveStatRealtimeStrategyGroupData() {
        StatRealtimeStrategyGroupDataEntity entity = new StatRealtimeStrategyGroupDataEntity();
        entity.setBizDate(LocalDate.now());
        entity.setStrategyId(1L);
        entity.setStrategyGroupId(1L);
        entity.setGroupName("A组");
        entity.setFlowControlNum(10);
        entity.setDispatchNum(20);
        groupDataRepository.saveStatRealtimeStrategyGroupData(entity);
    }

    @Test
    public void batchSaveStatRealtimeStrategyGroupData() {
        StatRealtimeStrategyGroupDataEntity entity01 = new StatRealtimeStrategyGroupDataEntity();
        entity01.setBizDate(LocalDate.now());
        entity01.setStrategyId(1L);
        entity01.setStrategyGroupId(2L);
        entity01.setGroupName("B组");
        entity01.setFlowControlNum(3);
        entity01.setDispatchNum(45);

        StatRealtimeStrategyGroupDataEntity entity02 = new StatRealtimeStrategyGroupDataEntity();
        entity02.setBizDate(LocalDate.now());
        entity02.setStrategyId(1L);
        entity02.setStrategyGroupId(3L);
        entity02.setGroupName("C组");
        entity02.setFlowControlNum(3);
        entity02.setDispatchNum(45);

        List<StatRealtimeStrategyGroupDataEntity> list = new ArrayList<>();
        list.add(entity01);
        list.add(entity02);
        groupDataRepository.batchSaveStatRealtimeStrategyGroupData(list);
    }

    @Test
    public void listByStrategyIdAndBizDate() {
        List<StatRealtimeStrategyGroupDataEntity> list = groupDataRepository.listByStrategyIdAndBizDate(1L, "2023-06-07");
        logger.info("list={}", list);
    }
}