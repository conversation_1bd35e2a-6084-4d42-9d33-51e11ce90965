package com.xftech.cdp.strategy;

import com.alibaba.fastjson.JSON;
import com.xftech.base.database.Page;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.api.dto.req.InstantStrategyCreateReq;
import com.xftech.cdp.api.dto.req.InstantStrategyUpdateReq;
import com.xftech.cdp.api.dto.req.StrategyListReq;
import com.xftech.cdp.domain.strategy.repository.StrategyMarketEventRepository;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.service.impl.InstantStrategyServiceImpl;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyMarketEventDo;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategyRepositoryTest extends BaseTest {
    @Autowired
    StrategyRepository strategyRepository;

    @Autowired
    StrategyMarketEventRepository strategyMarketEventRepository;

    @Autowired
    InstantStrategyServiceImpl instantStrategyService;

    @Test
    public void selectByStrategyId(){
        List<StrategyMarketEventDo> strategyMarketEventDos = strategyMarketEventRepository.selectByStrategyId(321l);
        System.out.println("res==="+JSON.toJSONString(strategyMarketEventDos));
        InstantStrategyUpdateReq req = new InstantStrategyUpdateReq();
        req.setId(321l);
        req.setDispatchMinUserNum(66);
        req.setDispatchMaxUserNum(88);
        List<InstantStrategyCreateReq.EventSet> eventSet = new ArrayList<>();

        InstantStrategyCreateReq.EventSet e = new InstantStrategyCreateReq.EventSet();
        List<InstantStrategyCreateReq.EventCondition> conditions= new ArrayList<>();
        InstantStrategyCreateReq.EventCondition cd = new InstantStrategyCreateReq.EventCondition(177l,"app","eq","xyf01",1);
        conditions.add(cd);
        //e.setId();
        e.setEventName("RiskT4IncreaseCredit");
        e.setDelay(new InstantStrategyCreateReq.EventSet.Delay(1,1,0));
        e.setEventCondition(conditions);

        eventSet.add(e);
        req.setEventSet(eventSet);
        instantStrategyService.updateStrategyEvent(req,strategyMarketEventDos);

    }

    @Test
    public void selectById() {
        StrategyDo strategyDo = strategyRepository.selectById(53L);
        System.out.println(strategyDo);
    }

    @Test
    public void insert() {
        StrategyDo strategyDo = new StrategyDo();
        strategyDo.setAbTest(0);
        strategyDo.setAbType(0);
        strategyDo.setName("测试哦");
        strategyDo.setSendRuler(1);
        strategyDo.setValidityBegin(LocalDateTime.now());
        strategyDo.setValidityEnd(LocalDateTime.now().plusDays(1));
        strategyDo.setSendFrequency("{\"type\": 1, \"value\": [7]}");
        strategyDo.setStatus(0);
        strategyDo.setCreatedOp("hhh");
        strategyDo.setUpdatedOp("hhh");
        strategyDo.setDetailDescription("");
        boolean insertFlag = strategyRepository.insert(strategyDo);
        System.out.println(insertFlag);
    }

//    @Test
//    public void delete() {
//        strategyRepository.delete(53L);
//    }

    @Test
    public void getByName() {
        StrategyDo strategyDo = strategyRepository.getByNameAndBusinessType("测试哦","new-cust");
        System.out.println(strategyDo);
    }

    @Test
    public void selectPage() {
        StrategyListReq strategyListReq = new StrategyListReq();
        strategyListReq.setName("测试哦");
        Page<StrategyDo> strategyDoPage = strategyRepository.selectPage(strategyListReq);
        System.out.println(strategyDoPage.getList());
    }

    @Test
    public void updateById() {
        StrategyDo strategyDo = new StrategyDo();
        strategyDo.setId(53L);
        strategyDo.setDetailDescription("测试哈哈哈");
        boolean updateFlag = strategyRepository.updateById(strategyDo);
        System.out.println(updateFlag);
    }

    @Test
    public void selectAll() {
        List<StrategyDo> strategyDos = strategyRepository.selectAll();
        System.out.println(strategyDos);
    }

    @Test
    public void getExecutingEventStrategyIds() {
        List<Long> strategyIds = strategyRepository.getExecutingEventStrategyIds();
        logger.info("strategyIds={}", strategyIds);
    }

}