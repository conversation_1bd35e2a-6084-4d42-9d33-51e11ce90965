package com.xftech.cdp.strategy;

import com.alibaba.fastjson.JSON;
import com.xftech.cdp.BaseTest;
import com.xftech.cdp.application.StrategyHandler;
import com.xftech.cdp.domain.flowctrl.repository.FlowCtrlRepository;
import com.xftech.cdp.domain.flowctrl.service.FlowCtrlCoreService;
import com.xftech.cdp.domain.strategy.model.enums.CouponStatusEnum;
import com.xftech.cdp.domain.strategy.repository.StrategyRepository;
import com.xftech.cdp.domain.strategy.repository.UserDispatchDetailRepository;
import com.xftech.cdp.domain.strategy.service.UserDispatchDetailService;
import com.xftech.cdp.domain.strategy.service.dispatch.offline.StrategyDispatchService;
import com.xftech.cdp.infra.client.ads.AdsClient;
import com.xftech.cdp.infra.client.ads.model.req.BaseAdsRequest;
import com.xftech.cdp.infra.client.ads.model.req.ChannelIndexReq;
import com.xftech.cdp.infra.client.ads.model.resp.BaseAdsResponse;
import com.xftech.cdp.infra.client.ads.model.resp.UserDispatchResp;
import com.xftech.cdp.infra.client.ads.model.resp.UserIndexResp;
import com.xftech.cdp.infra.client.coupon.CouponClient;
import com.xftech.cdp.infra.client.coupon.model.req.BaseCouponRequester;
import com.xftech.cdp.infra.client.coupon.model.req.CouponActivityListReq;
import com.xftech.cdp.infra.client.coupon.model.req.CouponSendBatchReq;
import com.xftech.cdp.infra.client.coupon.model.resp.BaseCouponResponse;
import com.xftech.cdp.infra.client.coupon.model.resp.CouponActivityListResp;
import com.xftech.cdp.infra.client.sms.SmsClient;
import com.xftech.cdp.infra.client.sms.model.SmsBatchSendArgs;
import com.xftech.cdp.infra.client.sms.model.SmsItemArgs;
import com.xftech.cdp.infra.client.sms.model.SmsItemRequester;
import com.xftech.cdp.infra.client.sms.model.SmsSendRequester;
import com.xftech.cdp.infra.client.sms.model.resp.BaseSmsResp;
import com.xftech.cdp.infra.client.sms.model.resp.SmsItemResp;
import com.xftech.cdp.infra.client.telemarketing.TelemarketingClient;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameListArgs;
import com.xftech.cdp.infra.client.telemarketing.model.TeleNameListRequest;
import com.xftech.cdp.infra.client.telemarketing.model.resp.TeleNameListResp;
import com.xftech.cdp.infra.constant.TransConstants;
import com.xftech.cdp.infra.repository.cdp.flowctrl.po.FlowCtrlDo;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.repository.cdp.strategy.po.UserDispatchDetailDo;
import com.xftech.cdp.infra.utils.JsonUtil;
import com.xftech.cdp.infra.utils.SerialNumberUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2023/2/9 01:10
 */
public class ATest extends BaseTest {


    @Resource
    private StrategyRepository strategyRepository;

    @Autowired
    private StrategyDispatchService strategyDispatchForTeleService;

    @Autowired
    private TelemarketingClient telemarketingClient;
    @Autowired
    private SmsClient smsClient;

    @Autowired
    private StrategyHandler strategyHandler;

    @Autowired
    private CouponClient couponClient;

    @Autowired
    private UserDispatchDetailService userDispatchDetailService;

    @Autowired
    private FlowCtrlRepository flowCtrlRepository;
    @Autowired
    private SerialNumberUtil serialNumberUtil;

    @Autowired
    private FlowCtrlCoreService flowCtrlCoreService;

    @Autowired
    private UserDispatchDetailRepository userDispatchDetailRepository;


    @Test
    public void t1() {

        List<FlowCtrlDo> flowCtrlRule = flowCtrlCoreService.getFlowCtrlRule(141L, 1, "");
        System.out.println(flowCtrlRule);

        // FlowCtrlDo flowCtrlDo = flowCtrlRepository.selectById(15L);
        //
        // List<UserDispatchIndexDto> userIndex2 = userDispatchDetailService.getUserIndex2("202304", Triple.of(141L, 2, flowCtrlDo), Arrays.asList(
        //         21111111155201L,
        //         21111111155202L,
        //         21111111155203L,
        //         21111111155204L,
        //         21111111155205L
        // ));
        //
        // System.out.println(userIndex2);


        // strategyHandler.refreshStatus();


        //     TeleNameListArgs args = new TeleNameListArgs();
        //     args.setPage(1);
        //     args.setPageSize(10);
        //     TeleNameListRequest requester = new TeleNameListRequest();
        //     requester.setArgs(args);
        //     TeleNameListResp teleNameListResp = telemarketingClient.queryNameList(requester);
        //     System.out.println(teleNameListResp);

        // strategyDispatchForTeleService.execute(1638418976184824014L);

        // TeleSaveBatchRequest requester = new TeleSaveBatchRequest();
        // TeleSaveBatchArgs args = new TeleSaveBatchArgs();
        // args.setCreditIdArr(Lists.newArrayList(111111111282124L));
        // args.setUserType(380);
        // args.setFlowNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS")));
        // args.setBatchCount(1);
        // args.setCurrentBatch(1);
        //
        //
        //
        // requester.setArgs(args);
        //
        //
        // TeleSaveBatchResp teleSaveBatchResp = telemarketingClient.saveBatch(requester);
        // System.out.println(teleSaveBatchResp);
    }


    @Test
    public void t2() {
        List<StrategyDo> strategyDos = strategyRepository.selectAll();
        System.out.println(strategyDos);
    }

    @Test
    public void teleNodeList() {
        TeleNameListArgs args = new TeleNameListArgs();
        args.setPage(1);
        args.setPageSize(10);
        TeleNameListRequest requester = new TeleNameListRequest();
        requester.setArgs(args);
        TeleNameListResp teleNameListResp = telemarketingClient.queryNameList(requester);
        System.out.println(teleNameListResp);
    }

    @Test
    public void smsTempList() {
        SmsItemArgs args = new SmsItemArgs();
        args.setPageSize(10);
        args.setPage(1);
        args.setGroup("marketing,notify");
        args.setTemplateId(null);

        SmsItemRequester requester = new SmsItemRequester();
        requester.setUa("");
        requester.setArgs(args);
        requester.setSign("");
        SmsItemResp smsItemResp = smsClient.queryDistinctItem(requester);
        System.out.println(JSON.toJSONString(smsItemResp));
    }

    @Test
    public void smsSend() {
        SmsBatchSendArgs args = new SmsBatchSendArgs();
        args.setMobiles(Collections.singletonList("18178422289"));
        args.setTemplateId("smstpl711002");
        args.setApp("xyf");
        args.setInnerApp("xyf");
        args.setBatchNum(serialNumberUtil.batchNum());
        SmsSendRequester requester = new SmsSendRequester();
        requester.setArgs(args);
        BaseSmsResp<?> resp = smsClient.doBatchSendSms(requester);
        System.out.println(JSON.toJSONString(resp));
    }

    @Test
    public void couponList() {
        CouponActivityListReq req = new CouponActivityListReq();
        req.setActivityId(null);
        req.setActivityName(null);
        req.setCouponType(Arrays.asList(4));
        req.setCouponId(null);
        req.setCouponName(null);
        req.setPage(1);
        req.setPageSize(10);
        BaseCouponResponse<CouponActivityListResp> response = couponClient.activityList(new BaseCouponRequester<>(req));
        System.out.println(JsonUtil.toJson(response));
    }

    @Test
    public void couponSend() {
        CouponSendBatchReq.User user = new CouponSendBatchReq.User();
        user.setUserId(111111111283768L);
        user.setMobile("17090090002");
        user.setName("17090090002");

        CouponSendBatchReq req = new CouponSendBatchReq();
        req.setActivityId(636L);
        req.setBatchNum(serialNumberUtil.batchNum());
        req.setApp("xyf");
        req.setInnerApp("xyf");
        req.setAllBatchCount(1);
        req.setUserList(Collections.singletonList(user));
        BaseCouponResponse<Object> response = couponClient.sendBatch(new BaseCouponRequester<>(req));
        System.out.println(response);
    }


    @Test
    public void  testUpdateDispatch(){
        List<UserDispatchDetailDo> userDispatchDetailDoList = new ArrayList<>();
        UserDispatchDetailDo userDispatchDetailDo = new UserDispatchDetailDo();
        userDispatchDetailDo.setTableName(TransConstants.userDispatchDetailTableName("202405"));
        userDispatchDetailDo.setBatchNum("788022491317563392");
        userDispatchDetailDo.setUserId(Long.parseLong("1939303089098106848"));
        userDispatchDetailDo.setStatus(CouponStatusEnum.getEnum("successed").getStatus());
        userDispatchDetailDo.setUsedStatus(CouponStatusEnum.getEnum("successed").getUsedStatus());
        userDispatchDetailDo.setDispatchTime(LocalDateTime.parse("2024-05-10 10:18:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        userDispatchDetailDoList.add(userDispatchDetailDo);
        boolean isUpdate = userDispatchDetailRepository.updateByBatchNumAndUserNo(userDispatchDetailDoList);
        System.out.println(isUpdate);
    }


    @Autowired
    private AdsClient adsClient;
    @Test
    public void adsTest() {
        List<Long> appUserId = new ArrayList<>();
        appUserId.add(666666L);
        appUserId.add(888888L);
        appUserId.add(13010275681L);
        List<String> marketChannel = new ArrayList<>();
        marketChannel.add("999");
        ChannelIndexReq req = new ChannelIndexReq(appUserId, marketChannel, LocalDate.parse("2023-06-05"), LocalDate.parse("2023-06-06"));
        BaseAdsResponse<List<UserDispatchResp>> listBaseAdsResponse = adsClient.queryUserDispatchCnt(new BaseAdsRequest<>(req));
        System.out.println(JSON.toJSONString(listBaseAdsResponse));
        Map<Long, Integer> indexMap = new HashMap<>();
        Map<Long, Integer> wkMonthMap = this.getDataCenterUserIndex(listBaseAdsResponse);
        indexMap.putAll(wkMonthMap);
        System.out.println(indexMap);
    }

    @Test
    public void testdispatch(){
        UserDispatchDetailDo userDispatchDetailDo = userDispatchDetailRepository.selectByBatchNoAndUserId("202405", "789175801101692928", "1939303089098209190");
        System.out.println(JsonUtil.toJson(userDispatchDetailDo));
    }

    private Map<Long, Integer> getDataCenterUserIndex(BaseAdsResponse<List<UserDispatchResp>> response) {
        Map<Long, Integer> indexMap = new HashMap<>();
        if (!response.isSuccess()) {
            return indexMap;
        }

        for (UserDispatchResp index : response.getPayload()) {
            for (UserDispatchResp.Params param : index.getParams()) {
                indexMap.put(param.getAppUserId(), param.getDispatchCnt());
            }
        }
        return indexMap;
    }
}
