package com.xftech.cdp.strategy;

import com.xftech.cdp.BaseTest;
import com.xftech.cdp.application.StrategyHandler;
import com.xftech.cdp.domain.strategy.repository.StrategyExecLogRepository;
import com.xftech.cdp.domain.strategy.service.ReportDailyStrategyService;
import com.xftech.cdp.domain.strategy.service.StrategyExecLogService;
import com.xftech.cdp.infra.repository.cdp.strategy.po.StrategyExecLogDo;
import io.lettuce.core.ScriptOutputType;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

/**
 * @<NAME_EMAIL>
 */
public class StrategyExecLogRepositoryTest extends BaseTest {

    @Autowired
    private StrategyExecLogService strategyExecLogService;
    @Autowired
    private StrategyHandler strategyHandler;

    @Test
    public void selectPage() {
    }

    @Test
    public void selectByStrategyIdAndChannel() {
    }

    @Test
    public void selectByStrategyId() {
    }

    @Test
    public void selectByStrategyIdAndChannelAndExecStatus() {
    }

    @Test
    public void selectByStrategyIdAndExecTime() {
    }

    @Test
    public void selectLatestStatusByStrategyId() {
    }

    @Test
    public void selectRetryList() {
    }

    @Test
    public void selectById() {
    }

    @Test
    public void updateById() {
    }

    @Test
    public void insert() {
    }

    @Test
    public void selectToday(){
        List<Long> strategyIds = Arrays.asList(96L, 399L, 400L, 404L, 419L);
        List<StrategyExecLogDo> strategyExecLogDos = strategyExecLogService.selectTodayByStrategyIds(strategyIds);
        System.out.println(strategyExecLogDos);
    }
    @Test
    public void testReportStrategy(){
        strategyHandler.reportDailyStrategyExecute();
    }
}
