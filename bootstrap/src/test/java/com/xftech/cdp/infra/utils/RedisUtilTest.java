package com.xftech.cdp.infra.utils;

import javax.annotation.Resource;

import com.xftech.cdp.BaseTest;

import org.junit.Test;

public class RedisUtilTest extends BaseTest {

    @Resource
    private RedisUtils redisUtils;

    @Test
    public void test01() {
        // 分布式锁
        String lockKey = String.format("ReDecision_%s_%s_%s", "123", "lgd", HashUtils.crc32Hash("messageId"));
        boolean lock = redisUtils.tryLock(lockKey, 60, 5, 50);

        if (lock) {
            String lockValue = redisUtils.get(lockKey);
            logger.info("RedisUtilTest 获取分布式锁成功, lockKey={}, lockValue={}", lockKey, lockValue);
        } else {
            logger.info("RedisUtilTest 获取分布式锁失败, lockKey={}", lockKey);
        }
    }

    @Test
    public void test02() {
        // 分布式锁
        String key = String.format("HyperLogLog_%s_%s_%s", "123", "lgd", HashUtils.crc32Hash("messageId"));
        redisUtils.pfAddTwoDay(key, "HyperLogLog");

        String value = redisUtils.get(key);
        logger.info("RedisUtilTest HyperLogLog, key={}, value={}", key, value);
    }

}