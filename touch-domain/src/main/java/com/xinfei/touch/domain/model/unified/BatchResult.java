package com.xinfei.touch.domain.model.unified;

import lombok.Data;
import java.util.List;
import java.util.ArrayList;

/**
 * 批量处理结果模型
 * 
 * <AUTHOR> Assistant
 * @since 2025-06-23
 */
@Data
public class BatchResult {
    
    /**
     * 总处理数
     */
    private Integer totalCount;
    
    /**
     * 成功数
     */
    private Integer successCount;
    
    /**
     * 失败数
     */
    private Integer failedCount;
    
    /**
     * 流控拦截数
     */
    private Integer flowControlledCount;
    
    /**
     * 处理中数
     */
    private Integer pendingCount;
    
    /**
     * 失败原因列表
     */
    private List<String> failedReasons;
    
    /**
     * 批次开始时间
     */
    private Long startTime;
    
    /**
     * 批次结束时间
     */
    private Long endTime;
    
    /**
     * 处理耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 创建批量结果
     */
    public static BatchResult create(Integer totalCount) {
        BatchResult result = new BatchResult();
        result.setTotalCount(totalCount);
        result.setSuccessCount(0);
        result.setFailedCount(0);
        result.setFlowControlledCount(0);
        result.setPendingCount(0);
        result.setFailedReasons(new ArrayList<>());
        result.setStartTime(System.currentTimeMillis());
        return result;
    }
    
    /**
     * 从用户结果列表创建批量结果
     */
    public static BatchResult fromUserResults(List<TouchUserResult> userResults) {
        BatchResult result = create(userResults.size());
        
        for (TouchUserResult userResult : userResults) {
            switch (userResult.getStatus()) {
                case SUCCESS:
                    result.incrementSuccess();
                    break;
                case FAILED:
                    result.incrementFailed();
                    if (userResult.getErrorMessage() != null) {
                        result.addFailedReason(userResult.getErrorMessage());
                    }
                    break;
                case FLOW_CONTROLLED:
                    result.incrementFlowControlled();
                    break;
                case PENDING:
                    result.incrementPending();
                    break;
                default:
                    break;
            }
        }
        
        result.complete();
        return result;
    }
    
    /**
     * 增加成功数
     */
    public void incrementSuccess() {
        successCount = successCount != null ? successCount + 1 : 1;
    }
    
    /**
     * 增加失败数
     */
    public void incrementFailed() {
        failedCount = failedCount != null ? failedCount + 1 : 1;
    }
    
    /**
     * 增加流控数
     */
    public void incrementFlowControlled() {
        flowControlledCount = flowControlledCount != null ? flowControlledCount + 1 : 1;
    }
    
    /**
     * 增加处理中数
     */
    public void incrementPending() {
        pendingCount = pendingCount != null ? pendingCount + 1 : 1;
    }
    
    /**
     * 添加失败原因
     */
    public void addFailedReason(String reason) {
        if (failedReasons == null) {
            failedReasons = new ArrayList<>();
        }
        if (reason != null && !failedReasons.contains(reason)) {
            failedReasons.add(reason);
        }
    }
    
    /**
     * 完成处理
     */
    public void complete() {
        this.endTime = System.currentTimeMillis();
        if (startTime != null) {
            this.duration = endTime - startTime;
        }
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        int success = successCount != null ? successCount : 0;
        return (double) success / totalCount * 100;
    }
    
    /**
     * 获取失败率
     */
    public double getFailedRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        int failed = failedCount != null ? failedCount : 0;
        return (double) failed / totalCount * 100;
    }
    
    /**
     * 获取流控率
     */
    public double getFlowControlledRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        int flowControlled = flowControlledCount != null ? flowControlledCount : 0;
        return (double) flowControlled / totalCount * 100;
    }
    
    /**
     * 判断是否全部成功
     */
    public boolean isAllSuccess() {
        return successCount != null && successCount.equals(totalCount);
    }
    
    /**
     * 判断是否全部失败
     */
    public boolean isAllFailed() {
        int failed = (failedCount != null ? failedCount : 0) + 
                    (flowControlledCount != null ? flowControlledCount : 0);
        return failed == totalCount;
    }
    
    /**
     * 判断是否部分成功
     */
    public boolean isPartialSuccess() {
        int success = successCount != null ? successCount : 0;
        return success > 0 && success < totalCount;
    }
    
    /**
     * 判断是否还有处理中的
     */
    public boolean hasPending() {
        return pendingCount != null && pendingCount > 0;
    }
    
    /**
     * 获取已处理数量
     */
    public int getProcessedCount() {
        int success = successCount != null ? successCount : 0;
        int failed = failedCount != null ? failedCount : 0;
        int flowControlled = flowControlledCount != null ? flowControlledCount : 0;
        return success + failed + flowControlled;
    }
    
    /**
     * 获取处理进度（0-100）
     */
    public int getProgress() {
        if (totalCount == null || totalCount == 0) {
            return 100;
        }
        return (getProcessedCount() * 100) / totalCount;
    }
    
    /**
     * 验证数据一致性
     */
    public boolean isValid() {
        int sum = (successCount != null ? successCount : 0) +
                  (failedCount != null ? failedCount : 0) +
                  (flowControlledCount != null ? flowControlledCount : 0) +
                  (pendingCount != null ? pendingCount : 0);
        return totalCount != null && sum <= totalCount;
    }
    
    /**
     * 转换为字符串（用于日志）
     */
    @Override
    public String toString() {
        return String.format("BatchResult{total=%d, success=%d, failed=%d, flowControlled=%d, pending=%d, successRate=%.2f%%}", 
                           totalCount, successCount, failedCount, flowControlledCount, pendingCount, getSuccessRate());
    }
}
