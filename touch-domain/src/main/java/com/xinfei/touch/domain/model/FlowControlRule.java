package com.xinfei.touch.domain.model;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 频控规则领域模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class FlowControlRule {
    
    /**
     * 规则ID
     */
    private Long ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 频控类型：EVENT, TOUCH, DISTRIBUTED, BATCH
     */
    private FlowControlType type;
    
    /**
     * 频控范围：USER, STRATEGY, CHANNEL, GLOBAL
     */
    private FlowControlScope scope;
    
    /**
     * 触达渠道（可选）
     */
    private TouchChannel channel;
    
    /**
     * 策略ID（可选）
     */
    private Long strategyId;
    
    /**
     * 业务事件类型（可选）
     */
    private String bizEventType;
    
    /**
     * 限制次数
     */
    private Integer limitTimes;
    
    /**
     * 限制时间（秒）
     */
    private Integer limitSeconds;
    
    /**
     * 优先级：数字越小优先级越高
     */
    private Integer priority;
    
    /**
     * 规则状态
     */
    private RuleStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
    
    /**
     * 构建频控键
     */
    public String buildFlowControlKey(Long userId) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("flow_control:");
        keyBuilder.append(type.name().toLowerCase()).append(":");
        keyBuilder.append(scope.name().toLowerCase()).append(":");
        
        switch (scope) {
            case USER:
                keyBuilder.append(userId);
                break;
            case STRATEGY:
                keyBuilder.append("strategy:").append(strategyId);
                if (userId != null) {
                    keyBuilder.append(":user:").append(userId);
                }
                break;
            case CHANNEL:
                keyBuilder.append("channel:").append(channel.getIdentifier());
                if (userId != null) {
                    keyBuilder.append(":user:").append(userId);
                }
                break;
            case GLOBAL:
                keyBuilder.append("global");
                break;
        }
        
        if (bizEventType != null) {
            keyBuilder.append(":event:").append(bizEventType);
        }
        
        return keyBuilder.toString();
    }
    
    /**
     * 检查规则是否匹配
     */
    public boolean matches(TouchChannel touchChannel, Long touchStrategyId, String touchBizEventType) {
        // 检查渠道匹配
        if (channel != null && !channel.equals(touchChannel)) {
            return false;
        }
        
        // 检查策略匹配
        if (strategyId != null && !strategyId.equals(touchStrategyId)) {
            return false;
        }
        
        // 检查事件类型匹配
        if (bizEventType != null && !bizEventType.equals(touchBizEventType)) {
            return false;
        }
        
        // 检查规则状态
        return status == RuleStatus.ENABLED;
    }
    
    /**
     * 是否为用户级规则
     */
    public boolean isUserLevel() {
        return scope == FlowControlScope.USER;
    }
    
    /**
     * 是否为策略级规则
     */
    public boolean isStrategyLevel() {
        return scope == FlowControlScope.STRATEGY;
    }
    
    /**
     * 是否为渠道级规则
     */
    public boolean isChannelLevel() {
        return scope == FlowControlScope.CHANNEL;
    }
    
    /**
     * 是否为全局级规则
     */
    public boolean isGlobalLevel() {
        return scope == FlowControlScope.GLOBAL;
    }

    /**
     * 验证规则
     */
    public void validate() {
        if (ruleId == null) {
            throw new IllegalArgumentException("ruleId不能为空");
        }
        if (type == null) {
            throw new IllegalArgumentException("type不能为空");
        }
        if (scope == null) {
            throw new IllegalArgumentException("scope不能为空");
        }
        if (limitTimes == null || limitTimes <= 0) {
            throw new IllegalArgumentException("limitTimes必须大于0");
        }
        if (limitSeconds == null || limitSeconds <= 0) {
            throw new IllegalArgumentException("limitSeconds必须大于0");
        }
        if (status == null) {
            throw new IllegalArgumentException("status不能为空");
        }
    }
}
