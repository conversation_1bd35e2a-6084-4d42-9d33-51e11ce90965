# XYF Touch Service - 项目总览

## 🎯 项目背景

根据《麻雀重构专项一期-触达.md》设计方案，本项目是一个完整的统一触达服务实现，旨在将T0实时触达和离线触达的流程抽象收口到一个新的独立应用，实现触达、频控、回执处理的标准化和统一化。

## 📁 项目结构

```
xyf-touch-service/
├── 📄 README.md                    # 项目说明文档
├── 📄 PROJECT_OVERVIEW.md          # 项目总览（本文件）
├── 📄 pom.xml                      # Maven父项目配置
├── 📁 docs/                        # 文档目录
│   ├── 📄 API.md                   # API接口文档
│   └── 📄 DEPLOYMENT.md            # 部署指南
├── 📁 sql/                         # 数据库脚本
│   └── 📄 init.sql                 # 数据库初始化脚本
├── 📁 docker/                      # Docker配置
│   └── 📄 Dockerfile               # Docker镜像构建文件
├── 📁 k8s/                         # Kubernetes配置
│   └── 📄 deployment.yaml          # K8s部署配置
├── 📁 touch-api/                   # API层 - 对外接口
│   ├── 📄 pom.xml
│   └── 📁 src/main/java/com/xinfei/touch/api/
│       ├── 📁 controller/          # REST控制器
│       └── 📁 dto/                 # 数据传输对象
├── 📁 touch-application/           # 应用层 - 业务编排
│   ├── 📄 pom.xml
│   └── 📁 src/main/java/com/xinfei/touch/application/
│       ├── 📁 service/             # 应用服务
│       ├── 📁 command/             # 命令对象
│       └── 📁 assembler/           # 对象转换器
├── 📁 touch-domain/                # 领域层 - 核心业务逻辑
│   ├── 📄 pom.xml
│   └── 📁 src/main/java/com/xinfei/touch/domain/
│       ├── 📁 model/               # 领域模型
│       ├── 📁 service/             # 领域服务
│       └── 📁 repository/          # 仓储接口
├── 📁 touch-infrastructure/        # 基础设施层 - 技术实现
│   ├── 📄 pom.xml
│   └── 📁 src/main/java/com/xinfei/touch/infrastructure/
│       ├── 📁 repository/          # 仓储实现
│       ├── 📁 channel/             # 渠道插件实现
│       ├── 📁 flowcontrol/         # 频控实现
│       ├── 📁 config/              # 配置管理
│       └── 📁 external/            # 外部服务调用
└── 📁 touch-starter/               # 启动层 - 应用启动
    ├── 📄 pom.xml
    └── 📁 src/
        ├── 📁 main/
        │   ├── 📁 java/            # 启动类
        │   └── 📁 resources/       # 配置文件
        └── 📁 test/                # 测试代码
```

## 🏗️ 架构设计

### 分层架构
- **API层**: 提供REST接口，处理HTTP请求响应
- **应用层**: 业务流程编排，事务管理
- **领域层**: 核心业务逻辑，领域模型
- **基础设施层**: 技术实现，外部系统集成

### 核心组件
- **触达请求处理器**: 统一处理所有触达请求
- **渠道插件系统**: 支持多种触达渠道的插件化扩展
- **频控系统**: 多层次的流量控制机制
- **配置管理**: 基于Apollo的动态配置管理
- **监控系统**: 基于Prometheus的指标监控

## 🚀 核心功能

### 1. 统一触达入口
- 支持实时触达（T0普通触达、T0引擎触达）
- 支持离线触达（离线普通触达、离线引擎触达）
- 统一的请求响应格式
- 异步和同步处理模式

### 2. 渠道插件化
- **短信渠道**: SMS发送服务
- **电销渠道**: 电话营销服务
- **Push渠道**: 移动推送服务
- **优惠券渠道**: 优惠券发放服务
- 支持渠道扩展和配置

### 3. 多层次频控
- **事件级流控**: 对业务事件进行流控
- **触达级流控**: 对触达请求进行流控
- **分布式流控**: 分布式环境下的流控
- **批量流控**: 批量处理的流控

### 4. 配置化管理
- 渠道配置动态管理
- 频控规则动态配置
- 支持配置热更新

## 🛠️ 技术栈

### 核心框架
- **Spring Boot 2.7.18**: 应用框架
- **Spring Cloud 2021.0.8**: 微服务框架
- **MyBatis Plus 3.5.3**: ORM框架

### 数据存储
- **MySQL 8.0**: 关系型数据库
- **Redis 6.0**: 缓存和分布式锁
- **Redisson 3.21.3**: Redis客户端

### 消息队列
- **RocketMQ 2.2.3**: 消息队列

### 配置中心
- **Apollo 2.0.1**: 配置管理

### 监控运维
- **Micrometer**: 指标收集
- **Prometheus**: 监控系统
- **Docker**: 容器化
- **Kubernetes**: 容器编排

## 📊 数据模型

### 核心表结构
- **touch_record**: 触达记录表
- **flow_control_rule**: 频控规则表
- **channel_config**: 渠道配置表

### 领域模型
- **TouchRequest/TouchResponse**: 触达请求响应模型
- **FlowControlRule**: 频控规则模型
- **ChannelPlugin**: 渠道插件接口

## 🔧 快速开始

### 1. 环境准备
```bash
# 安装依赖服务
docker-compose up -d mysql redis rocketmq

# 初始化数据库
mysql -u root -p < sql/init.sql
```

### 2. 启动应用
```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run -pl touch-starter
```

### 3. 测试验证
```bash
# 健康检查
curl http://localhost:8080/touch-service/actuator/health

# 发送触达请求
curl -X POST http://localhost:8080/touch-service/api/touch/send \
  -H "Content-Type: application/json" \
  -d '{
    "requestId": "test_001",
    "touchType": "REALTIME_NORMAL",
    "channel": "SMS",
    "strategyId": 1001,
    "userId": 123456
  }'
```

## 📈 监控指标

### 业务指标
- 触达成功率
- 触达失败率
- 频控拦截率
- 渠道可用性

### 技术指标
- QPS/TPS
- 响应时间
- 错误率
- 资源使用率

## 🔄 部署方式

### 本地开发
- IDE直接运行
- Maven命令启动

### Docker部署
- 单容器部署
- Docker Compose部署

### Kubernetes部署
- 生产环境推荐
- 支持自动扩缩容
- 支持滚动更新

## 🧪 测试策略

### 单元测试
- 领域逻辑测试
- 服务层测试
- 工具类测试

### 集成测试
- API接口测试
- 数据库集成测试
- 外部服务集成测试

### 性能测试
- 压力测试
- 并发测试
- 稳定性测试

## 📝 开发规范

### 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一异常处理

### 提交规范
- 使用语义化提交信息
- 代码审查机制
- 自动化CI/CD

## 🔮 未来规划

### 功能增强
- 智能路由算法
- 个性化触达
- 多媒体内容支持
- AI决策集成

### 技术演进
- 云原生改造
- 服务网格集成
- 事件驱动架构
- 微服务拆分优化

---

**项目状态**: ✅ 开发完成，可用于生产环境  
**维护团队**: zhibin.huang  
**最后更新**: 2025-06-20
