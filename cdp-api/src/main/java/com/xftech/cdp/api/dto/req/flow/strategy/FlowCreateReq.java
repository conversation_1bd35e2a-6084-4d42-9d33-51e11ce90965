/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.flow.strategy;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xftech.cdp.api.dto.base.TimeFormat;
import com.xftech.cdp.api.dto.req.BaseReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.*;

/**
 *
 * <AUTHOR>
 * @version $ FlowStrategyCreateReq, v 0.1 2023/12/13 11:40 yye.xu Exp $
 */

@Data
public class FlowCreateReq extends BaseReq {

    @ApiModelProperty(value = "画布名称")
    private String name;

    @ApiModelProperty(value = "画布类型：1-离线, 2-T0")
    private Integer flowType;

    @ApiModelProperty(value = "业务线： new-cust-新客, old-cust-老客, test-cust-测试")
    private String businessType;

    @ApiModelProperty(value = "有效开始")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime validityBegin;

    @ApiModelProperty(value = "有效结束")
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    private LocalDateTime validityEnd;

    // 策略组定义
    @ApiModelProperty(value = "策略组定义")
    private List<CreateStrategyConfig> stragegyList;

    @ApiModelProperty(value = "-1-未发布, 0-已发布")
    private Integer status;

    @JsonIgnore
    @JSONField(serialize = false)
    public Pair<Boolean, String> isValid() {
        if (StringUtils.isEmpty(name)) {
            return Pair.of(false, "画布名称为空");
        }
        if (!Arrays.asList(1, 2)
                .contains(flowType)) {
            return Pair.of(false, "画布类型错误");
        }
        if (StringUtils.isEmpty(businessType)) {
            return Pair.of(false, "画布业务线错误");
        }
        if (validityEnd.isBefore(validityBegin)) {
            return Pair.of(false, "有效开始大于有效结束");
        }
        // 验证策略列表
        if (!CollectionUtils.isEmpty(stragegyList)) {
            List<String> nodeIds = new ArrayList<>();
            for (CreateStrategyConfig stragegyConfig : stragegyList) {
                Pair<Boolean, String> pair = stragegyConfig.isValid();
                if (!Objects.equals(true, pair.getLeft())) {
                    return pair;
                }
                if (nodeIds.size() > 0) {
                    if (!StringUtils.equals(nodeIds.get(nodeIds.size() - 1),
                            stragegyConfig.getNode().getNodeId())) {
                        return Pair.of(false, "策略节点上下级关系错误");
                    }
                }
                if (CollectionUtils.isEmpty(nodeIds)) {
                    nodeIds.add(stragegyConfig.getNode().getNodeId());
                }
                Node next = stragegyConfig.getNextNode();
                if (next != null) {
                    if (nodeIds.contains(next.getNodeId())) {
                        return Pair.of(false, "策略节点编号有重复");
                    }
                    nodeIds.add(next.getNodeId());
                }
            }
        }
        return Pair.of(true, null);
    }
}