package com.xftech.cdp.api.dto.req.flowctrl;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.PageRequestDto;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;


/**
 * @<NAME_EMAIL>
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class FlowCtrlListReq extends PageRequestDto {
    @Size(max = 30)
    @ApiModelProperty(value = "规则名称")
    private String name;

    @ApiModelProperty(value = "规则编号")
    private String ruleId;

    @ApiModelProperty(value = "规则状态 0-初始化 1-生效中 2-已关闭")
    private Integer status;

    @ApiModelProperty(value = "策略名称")
    private String strategyName;

    @ApiModelProperty(value = "渠道名称 1-短信 2-电销 3-优惠券")
    private Integer channel;

    @ApiModelProperty(value = "规则类型 1-策略 2-渠道 3-多策略共享 4-业务线")
    private Integer type;

    @ApiModelProperty(value = "业务线类型 old-cust new-cust")
    private String bizType;

    @ApiModelProperty(value = "更新人")
    private String updatedOp;

    @ApiModelProperty(value = "创建人")
    private String createdOp;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime endTime;


    @ApiModelProperty(value = "策略id")
    private List<Long> strategyIds;
}
