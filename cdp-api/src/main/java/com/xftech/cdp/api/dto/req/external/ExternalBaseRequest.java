package com.xftech.cdp.api.dto.req.external;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @<NAME_EMAIL>
 * @date 2023/4/4 11:26
 */
@Data
public class ExternalBaseRequest<T> {

    @JsonProperty("ua")
    @JSONField(name = "ua")
    private String ua;

    @JsonProperty("args")
    @JSONField(name = "args")
    private T args;

    @JsonProperty("sign")
    @JSONField(name = "sign")
    private String sign;

    @JsonProperty("timestamp")
    @JSONField(name = "timestamp")
    private Long timestamp = System.currentTimeMillis() / 1000L;
}
