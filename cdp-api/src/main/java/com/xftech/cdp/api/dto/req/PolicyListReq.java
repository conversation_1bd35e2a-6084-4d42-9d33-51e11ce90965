/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ PolicyListReq, v 0.1 2024/1/12 15:55 ****.**** Exp $
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PolicyListReq extends PageRequestDto implements Serializable {
    
    @ApiModelProperty(value = "策略ID")
    private Integer policyId;
    
    @ApiModelProperty(value = "策略名称")
    private String policyName;
    
    @ApiModelProperty("标签类型 tag=标签策略 case=案件策略")
    private String type;
    
    @NotBlank(message = "业务线不能为空")
    @ApiModelProperty(value = "麻雀策略业务线")
    private String cdpBusinessLine;
    
    
}