/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version $ AppBannerReq, v 0.1 2024/4/18 17:43 benlin.wang Exp $
 */

@Data
public class AppBannerReq {

    @ApiModelProperty(value = "用户Id")
    private Long userId;

    @ApiModelProperty(value = "app")
    private String app;

    @ApiModelProperty(value = "mobile")
    private String mobile;

    @ApiModelProperty(value = "弹窗id列表")
    private List<String> appBannerIdList;

    @ApiModelProperty(value = "查询类型：1=普通弹窗决策；2=弹窗实时提额；3=借款挽留弹窗")
    private Integer queryType;

    @ApiModelProperty(value = "借款挽留弹窗参数")
    private LoanRetentionRequest loanRetentionRequest;

    @ApiModelProperty(value = "结清挽留弹窗参数")
    private SettlementRequest settlementRequest;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LoanRetentionRequest {
        @ApiModelProperty(value = "选项(中文名称)")
        private String choiceTitle;

        @ApiModelProperty(value = "期数")
        private Integer installments;

        @ApiModelProperty(value = "金额 单位:元")
        private Double amount;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SettlementRequest {
        @ApiModelProperty(value = "客户号")
        private String custNo;

        @ApiModelProperty(value = "借款订单号")
        private String orderNo;
    }

}