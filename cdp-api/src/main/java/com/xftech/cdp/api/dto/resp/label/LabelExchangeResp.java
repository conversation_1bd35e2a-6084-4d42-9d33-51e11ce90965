/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.label;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ LabelExchangeResp, v 0.1 2024/6/20 15:08 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class LabelExchangeResp implements Serializable {
    private static final long serialVersionUID = 2325127407251740138L;

    @ApiModelProperty(value = "交互方式id")
    private Integer code;

    @ApiModelProperty(value = "交互方式描述")
    private String desc;

}