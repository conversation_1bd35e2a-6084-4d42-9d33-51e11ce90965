package com.xftech.cdp.api.dto.req.flowctrl;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @<NAME_EMAIL>
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class FlowCtrlUpdateReq extends FlowCtrlCreateReq{
    @NotNull(message = "规则编号不能为空")
    @ApiModelProperty(value = "规则编号", required = true)
    private Long id;

}
