package com.xftech.cdp.api.dto.base;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @since 2022/8/8 5:24 PM
 */
public interface TimeFormat {

    String DATE_TIME = "yyyy-MM-dd HH:mm:ss";

    String TIME = "HH:mm:ss";

    String DATE = "yyyy-MM-dd";

    String TIME_SEC = "HH:mm";


    DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(DATE_TIME);

    DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(TIME);

    DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(DATE);

    static String dateTimeFormat(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return "";
        }
        return DATE_TIME_FORMATTER.format(localDateTime);
    }

    static String timeFormat(LocalTime localTime) {
        if (localTime == null) {
            return "";
        }
        return TIME_FORMATTER.format(localTime);
    }

    static String timeFormat(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return "";
        }
        return TIME_FORMATTER.format(localDateTime.toLocalTime());
    }

    static String dateFormat(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        return DATE_FORMATTER.format(localDate);
    }

    static String dateFormat(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return "";
        }
        return DATE_FORMATTER.format(localDateTime.toLocalDate());
    }


}
