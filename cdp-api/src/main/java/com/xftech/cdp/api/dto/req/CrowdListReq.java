package com.xftech.cdp.api.dto.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.PageRequestDto;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/2/14
 */
@Getter
@Setter
public class CrowdListReq extends PageRequestDto {

    @Size(max = 20)
    @NotEmpty
    @ApiModelProperty(value = "人群名称")
    private String crowdName;

    @ApiModelProperty(value = "人群编号")
    private String id;

    @ApiModelProperty("更新人")
    private String updateOp;

    @ApiModelProperty("状态，0: 初始化、1: 刷新中、2: 刷新成功、3: 刷新失败、4: 暂停中、5: 已完成")
    private Integer status;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "筛选方式,0:文件上传,1:标签圈选")
    private Integer filterMethod;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "刷新机制,0:手动刷新，1:例行刷新")
    private Integer refreshType;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime startTime;

    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    @NotBlank(message = "业务线不能为空")
    private String businessType;

    @ApiModelProperty(value = "关联策略编号 -1:无")
    private Long strategyIds;

    @ApiModelProperty(value = "人群分组")
    private Integer groupType;

    private List<Long> crowdPackIds;
}
