/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp.external;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ ExistDispatchRecordResp, v 0.1 2024/4/1 17:24 lingang.han Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExistDispatchRecordResp implements Serializable {
    private static final long serialVersionUID = -9116251660692992379L;

    /**
     * "1-存在下发记录
     * 0-不存在下发记录"
     */
    private Integer result;


}