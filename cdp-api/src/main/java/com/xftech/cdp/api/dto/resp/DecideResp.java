/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version $ AppBannerResp, v 0.1 2024/4/18 17:44 benlin.wang Exp $
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DecideResp implements Serializable {
    private static final long serialVersionUID = -6579121183015731468L;

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "是否命中")
    private Boolean isHit;

    @ApiModelProperty(value = "决策结果")
    private Map<String, Object> result;


    @Getter
    @AllArgsConstructor
    public static enum CodeEnum {
        Success(0, "OK"),
        NotHitStrategy(1, "未命中策略"),
        HitMultiStrategy(2, "配置了多个策略"),
        HitStrategyNotExecute(3, "命中了策略，但不执行"),
        CdpInnerError(4, "服务内部错误"),
        InvalidParam(5, "非法的参数"),

        ;

        private final Integer code;
        private final String message;
    }
}