/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.flow.strategy;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xftech.cdp.api.dto.base.PageRequestDto;
import com.xftech.cdp.api.dto.base.TimeFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ FlowListReq, v 0.1 2023/12/20 17:00 yye.xu Exp $
 */

@Data
public class FlowListReq extends PageRequestDto {

    @ApiModelProperty(value = "业务线 new-cust-新客  old-cust-老客")
    private String businessType;
    @ApiModelProperty(value = "画布id")
    private Long id;
    @ApiModelProperty(value = "画布名称")
    private String name;
    @ApiModelProperty(value = "更新人")
    private String updatedOp;
    @ApiModelProperty(value = "状态")
    private Integer status;
    @ApiModelProperty(value = "触达渠道,数组对象:[1,2]")
    private List<Integer> marketChannel;
    @ApiModelProperty(value = "发送规则 发送类型：0-单次, 1-例行, 2-事件, 3-周期, 9-引擎")
    private Integer sendRuler;
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新开始时间")
    private LocalDateTime updateStartTime;
    @JsonFormat(pattern = TimeFormat.DATE_TIME)
    @ApiModelProperty(value = "更新结束时间")
    private LocalDateTime updateEndTime;
}