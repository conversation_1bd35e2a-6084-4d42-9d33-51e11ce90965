/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req;

import com.xftech.cdp.api.dto.base.PageRequestDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version $ NameTypeReq, v 0.1 2023/10/13 11:25 wancheng.qu Exp $
 */
@Data
public class NameTypeReq  extends PageRequestDto implements Serializable {

    @ApiModelProperty(value = "名单类型ID")
    private Integer nameTypeId;
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    @ApiModelProperty(value = "归属业务(first_loan:首贷,compound_loan:复贷)")
    private List<String> businessType;
    @ApiModelProperty(value = "场景筛选 AI=ai语音;AT=人工")
    private List<String> scenes;

}