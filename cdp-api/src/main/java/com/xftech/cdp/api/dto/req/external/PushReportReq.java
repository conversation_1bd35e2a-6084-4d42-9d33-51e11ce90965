/**
 * Xinfei.com Inc.
 * Copyright (c) 2004-2024 All Rights Reserved.
 */
package com.xftech.cdp.api.dto.req.external;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version $ PushReportReq, v 0.1 2024/2/22 14:21 lingang.han Exp $
 */

@Data
public class PushReportReq implements Serializable {

    private static final long serialVersionUID = 379292023216332695L;

    /**
     * app
     */
    private String app;

    /**
     * 用户id
     */
    private Long userNo;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 批次号
     */
    private String batchNum;

    /**
     * 模板id
     */
    private String templateId;

    /**
     * 1-失败， 2-未知， 3-成功
     */
    private Integer status;

    /**
     * 厂商，如HUAWEI
     */
    private String agent;

    /**
     * 送达时间，yyyy-MM-dd HH:mm:ss，可能由的渠道厂商不支持或者发送失败，该值可能为空
     */
    private String deliveryTime;
}