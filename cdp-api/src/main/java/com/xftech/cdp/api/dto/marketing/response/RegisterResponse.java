package com.xftech.cdp.api.dto.marketing.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version v1.0 2024/10/11
 * @description RegisterRequest
 */
@Data
@ApiModel("活动报名 响应体")
public class RegisterResponse {

    @ApiModelProperty(value = "是否展示报名模块 true=展示；false=不展示")
    private boolean show;

    @ApiModelProperty(value = "报名状态 0=未报名；1=已报名")
    private Integer status;

}
