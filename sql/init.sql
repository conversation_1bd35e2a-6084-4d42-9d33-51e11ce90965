-- 触达服务数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `touch_service` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `touch_service`;

-- 触达记录主表
CREATE TABLE `touch_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `request_id` varchar(64) NOT NULL COMMENT '请求唯一标识',
  `batch_no` varchar(64) NOT NULL COMMENT '批次号',
  `touch_type` tinyint(4) NOT NULL COMMENT '触达类型：1-实时普通，2-实时引擎，3-离线普通，4-离线引擎',
  `channel` tinyint(4) NOT NULL COMMENT '触达渠道：1-短信，2-电销，3-Push，4-优惠券',
  `strategy_id` bigint(20) NOT NULL COMMENT '策略ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `biz_event_type` varchar(64) DEFAULT NULL COMMENT '业务事件类型',
  `template_params` text COMMENT '模板参数JSON',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待处理，1-成功，2-失败，3-流控',
  `error_code` varchar(32) DEFAULT NULL COMMENT '错误码',
  `error_message` varchar(512) DEFAULT NULL COMMENT '错误信息',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `callback_time` datetime DEFAULT NULL COMMENT '回执时间',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_request_id` (`request_id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_user_channel_time` (`user_id`, `channel`, `send_time`),
  KEY `idx_strategy_time` (`strategy_id`, `send_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触达记录表';

-- 频控规则表
CREATE TABLE `flow_control_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `rule_name` varchar(128) NOT NULL COMMENT '规则名称',
  `rule_type` tinyint(4) NOT NULL COMMENT '规则类型：1-事件级，2-触达级，3-分布式，4-批量',
  `scope_type` tinyint(4) NOT NULL COMMENT '范围类型：1-用户级，2-策略级，3-渠道级，4-全局级',
  `channel` tinyint(4) DEFAULT NULL COMMENT '渠道：1-短信，2-电销，3-Push，4-优惠券',
  `strategy_id` bigint(20) DEFAULT NULL COMMENT '策略ID',
  `biz_event_type` varchar(64) DEFAULT NULL COMMENT '业务事件类型',
  `limit_times` int(11) NOT NULL COMMENT '限制次数',
  `limit_seconds` int(11) NOT NULL COMMENT '限制时间（秒）',
  `priority` int(11) NOT NULL DEFAULT '0' COMMENT '优先级，数字越小优先级越高',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_type_scope` (`rule_type`, `scope_type`),
  KEY `idx_channel_strategy` (`channel`, `strategy_id`),
  KEY `idx_event_type` (`biz_event_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='频控规则表';

-- 渠道配置表
CREATE TABLE `channel_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel` tinyint(4) NOT NULL COMMENT '渠道：1-短信，2-电销，3-Push，4-优惠券',
  `config_key` varchar(64) NOT NULL COMMENT '配置键',
  `config_value` text NOT NULL COMMENT '配置值',
  `config_type` varchar(32) NOT NULL COMMENT '配置类型：STRING, INTEGER, BOOLEAN, JSON',
  `description` varchar(256) DEFAULT NULL COMMENT '配置描述',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_key` (`channel`, `config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道配置表';

-- 插入初始频控规则数据
INSERT INTO `flow_control_rule` (`rule_name`, `rule_type`, `scope_type`, `channel`, `strategy_id`, `biz_event_type`, `limit_times`, `limit_seconds`, `priority`, `status`) VALUES
('短信用户级频控', 2, 1, 1, NULL, NULL, 5, 3600, 1, 1),
('电销用户级频控', 2, 1, 2, NULL, NULL, 3, 86400, 1, 1),
('Push用户级频控', 2, 1, 3, NULL, NULL, 10, 3600, 1, 1),
('优惠券用户级频控', 2, 1, 4, NULL, NULL, 2, 86400, 1, 1),
('全局事件级频控', 1, 4, NULL, NULL, NULL, 1000, 60, 1, 1);

-- 插入初始渠道配置数据
INSERT INTO `channel_config` (`channel`, `config_key`, `config_value`, `config_type`, `description`, `status`) VALUES
(1, 'service_url', 'http://sms.xinfei.io', 'STRING', '短信服务地址', 1),
(1, 'timeout', '30000', 'INTEGER', '短信服务超时时间（毫秒）', 1),
(1, 'retry_times', '3', 'INTEGER', '短信服务重试次数', 1),
(1, 'enabled', 'true', 'BOOLEAN', '短信服务是否启用', 1),
(2, 'service_url', 'http://telemkt.xinfei.io', 'STRING', '电销服务地址', 1),
(2, 'timeout', '60000', 'INTEGER', '电销服务超时时间（毫秒）', 1),
(2, 'retry_times', '2', 'INTEGER', '电销服务重试次数', 1),
(2, 'enabled', 'true', 'BOOLEAN', '电销服务是否启用', 1),
(3, 'service_url', 'http://sms.xinfei.io', 'STRING', 'Push服务地址', 1),
(3, 'timeout', '30000', 'INTEGER', 'Push服务超时时间（毫秒）', 1),
(3, 'retry_times', '3', 'INTEGER', 'Push服务重试次数', 1),
(3, 'enabled', 'true', 'BOOLEAN', 'Push服务是否启用', 1),
(4, 'service_url', 'http://userassetcore.xinfei.io', 'STRING', '优惠券服务地址', 1),
(4, 'timeout', '30000', 'INTEGER', '优惠券服务超时时间（毫秒）', 1),
(4, 'retry_times', '3', 'INTEGER', '优惠券服务重试次数', 1),
(4, 'enabled', 'true', 'BOOLEAN', '优惠券服务是否启用', 1);
