server:
  port: 8080
  servlet:
    context-path: /touch-service

spring:
  application:
    name: xyf-touch-service
  profiles:
    active: dev

  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: ******************************************************************************************************************************************************
      username: root
      password: password
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      filters: stat,wall,slf4j
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000

  # Redis配置
  redis:
    host: localhost
    port: 6379
    password:
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
    banner: false
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# RocketMQ配置
rocketmq:
  name-server: localhost:9876
  producer:
    group: touch-service-producer
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
  consumer:
    group: touch-service-consumer

# Apollo配置
apollo:
  meta: http://localhost:8080
  bootstrap:
    enabled: true
    namespaces: application,touch-service

# 日志配置
logging:
  level:
    com.xinfei.touch: DEBUG
    org.springframework.web: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/touch-service.log

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 触达服务配置
touch:
  # 渠道配置
  channel:
    sms:
      service-url: http://sms.xinfei.io
      timeout: 30000
      retry-times: 3
      enabled: true
    voice:
      service-url: http://telemkt.xinfei.io
      timeout: 60000
      retry-times: 2
      enabled: true
    push:
      service-url: http://sms.xinfei.io
      timeout: 30000
      retry-times: 3
      enabled: true
    coupon:
      service-url: http://userassetcore.xinfei.io
      timeout: 30000
      retry-times: 3
      enabled: true

  # 频控配置
  flow-control:
    event:
      enabled: true
      default-limit-seconds: 600
    touch:
      enabled: true
      lock-timeout-seconds: 360
    distributed:
      enabled: true
      lock-prefix: "touch:lock:"

  # 线程池配置
  executor:
    core-pool-size: 20
    max-pool-size: 100
    queue-capacity: 1000
    keep-alive-seconds: 60
