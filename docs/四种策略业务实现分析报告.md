# 四种策略业务实现分析报告

## 概述

本报告详细分析了CDP系统中四种核心策略的业务实现逻辑：T0实时策略、离线策略、T0引擎策略、离线引擎策略。每种策略都有其独特的执行模式、触发机制和业务场景。

## 1. T0实时策略 (EVENT策略)

### 1.1 业务定义
T0实时策略是基于事件触发的实时营销策略，当用户行为事件发生时立即进行决策和触达。

### 1.2 核心实现逻辑

#### 1.2.1 策略类型定义
**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/StrategyRulerEnum.java`
```java
/**
 * 事件
 */
EVENT(2, "事件", StrategyRulerEnum::getRealtime),
```

#### 1.2.2 执行流程
**主要实现类**: `StrategyEventDispatchServiceImpl`
**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/service/dispatch/event/impl/StrategyEventDispatchServiceImpl.java`

**三阶段处理流程**:

1. **预筛阶段** (prescreen)
   - 检查是否存在对应事件的策略配置
   - 进行事件预筛和人群预筛
   - 检查引擎次数限制
   - 处理延迟逻辑

2. **复筛阶段** (rescreen)
   - 实时标签查询和筛选
   - 策略复筛验证
   - 根据策略类型决定是否进入触达

3. **触达阶段** (dispatch)
   - 验证触达时间有效性
   - 执行流控检查
   - 进行实际的营销触达

#### 1.2.3 事件处理机制
**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/infra/rocketmq/EventMessageBaseProcessor.java`
```java
BizEventMessageVO bizEventMessage = eventMessageProcessor.doMessageProcessor(messageListener.topic(),
        messageListener.consumerGroup(),getConsumerTag(messageListener),message);
mqConsumeService.bizEventProcess(messageExt.getMsgId(),bizEventMessage);
```

### 1.3 业务特点
- **实时性**: 事件发生后立即处理，延迟在毫秒级
- **事件驱动**: 基于用户行为事件触发
- **个性化**: 针对单个用户进行实时决策
- **高并发**: 需要处理大量并发事件

## 2. 离线策略 (CYCLE/ONCE策略)

### 2.1 业务定义
离线策略是基于定时调度的批量营销策略，按照预设的时间周期对目标人群进行批量处理。

### 2.2 核心实现逻辑

#### 2.2.1 策略类型定义
**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/strategy/model/enums/StrategyRulerEnum.java`
```java
/**
 * 单次
 */
ONCE(0, "单次", StrategyRulerEnum::getOffline),

/**
 * 例行
 */
CYCLE(1, "例行", StrategyRulerEnum::getOffline),

/**
 * 按循环周期天数平均分组
 */
CYCLE_DAY(3, "每日循环周期", StrategyRulerEnum::getOffline);
```

#### 2.2.2 执行流程
**主要实现类**: `StrategyDispatchForOfflineServiceImpl` 等离线策略实现类

**批量处理流程**:
1. **初始化执行上下文** - 准备策略执行环境
2. **人群包校验** - 验证目标人群数据
3. **核心逻辑执行** - 分页查询、分组过滤、批量下发
4. **成功处理** - 更新执行状态和统计信息

#### 2.2.3 分组处理机制
**代码位置**: `StrategyDispatchForOfflineServiceImpl.coreLogicExecute()`
```java
// 获取分组匹配规则
for (StrategyGroupDo group : strategyGroupDoList) {
    matchFunctions.put(group.getId(), group.match(StrategyGroupTypeEnum.getInstance(strategyDo.getAbType())));
}
// 分页查询+按分组规则过滤+下发
batchDispatch(strategyContext, Triple.of(strategyDo.getBizKey(), matchFunctions, strategyGroupDoList));
```

### 2.3 业务特点
- **批量处理**: 一次处理大量用户数据
- **定时调度**: 基于XXL-JOB等调度系统触发
- **资源优化**: 在业务低峰期执行，避免影响实时业务
- **数据一致性**: 基于快照数据保证处理一致性

## 3. T0引擎策略 (EVENT_ENGINE策略)

### 3.1 业务定义
T0引擎策略是结合了实时事件触发和AI决策引擎的智能营销策略，通过机器学习模型进行实时决策。

### 3.2 核心实现逻辑

#### 3.2.1 引擎决策流程
**代码位置**: `StrategyEventDispatchServiceImpl.rescreenWithEngine()`
```java
// 调用决策引擎
JSONObject resp = modelPlatformService.prediction(modelPredictionReq);
PredictDecisionDto predictDecisionDto = JsonUtil.parse(JsonUtil.toJson(decisionVal), PredictDecisionDto.class);

if (predictDecisionDto != null && predictDecisionDto.isSucced()) {
    // 处理引擎决策结果
    event.setIfMarket(predictDecisionDto.ifMarket());
    // 执行营销动作
    for (PredictDecisionDto.DecisionData.Action action : actions) {
        // 营销触达逻辑
    }
}
```

#### 3.2.2 灰度控制机制
**代码位置**: `StrategyEventDispatchServiceImpl.prescreen()`
```java
// 如果是决策引擎策略，则进行灰度判定
if (eventContext.getStrategyDo().isEngineStrategy()) {
    if (!isInEngineGrayGroup(eventContext, group)) {
        eventContext.getBizEventVO().setIfIntoEngine(false);
    }
}
```

### 3.3 业务特点
- **AI驱动**: 基于机器学习模型进行决策
- **实时响应**: 保持事件触发的实时性
- **智能化**: 根据用户特征和行为模式智能决策
- **灰度发布**: 支持策略的灰度上线和AB测试

## 4. 离线引擎策略 (OFFLINE_ENGINE策略)

### 4.1 业务定义
离线引擎策略是结合了批量处理和AI决策引擎的智能营销策略，通过分布式计算和机器学习模型进行大规模用户决策。

### 4.2 核心实现逻辑

#### 4.2.1 分布式执行架构
**主要实现类**: `DistributeOfflineEngineDispatchServiceImpl`
**代码位置**: `cdp-domain/src/main/java/com/xftech/cdp/distribute/offline/engine/DistributeOfflineEngineDispatchServiceImpl.java`

#### 4.2.2 分片处理机制
**代码位置**: `StrategyTaskDistributeHandler`
```java
// 生成分片任务
public void createStrategyDistributeSliceTasks(StrategyDo strategyDo) {
    // 创建分布式待执行任务
    createDistributeTasks(strategyDo, crowdSliceDoList, LocalDateTime.of(LocalDate.now(), sendTime));
    // 写入策略exec_log
    createStrategyDispatchExecLog(strategyDo.getId());
}
```

#### 4.2.3 引擎调用逻辑
**代码位置**: `DispatchOfflineEngineService.pushEngine()`
```java
ModelPredictionReq modelPredictionReq = ModelPredictionReq.builder()
        .model_name(engineCode)
        .biz_data(ModelPredictionReq.BizData.builder()
                .requestId(SerialNumberUtil.nextId())
                .mobile(crowdDetailDo.getMobile())
                .user_no(crowdDetailDo.getUserId())
                .build())
        .build();
```

### 4.3 业务特点
- **大规模处理**: 支持千万级用户的批量智能决策
- **分布式架构**: 通过分片和分布式计算提高处理效率
- **引擎集成**: 每个用户都通过AI模型进行个性化决策
- **资源调度**: 通过XXL-JOB进行任务调度和资源管理

## 5. 策略对比总结

| 策略类型 | 触发方式 | 处理模式 | 决策方式 | 适用场景 |
|---------|---------|---------|---------|---------|
| T0实时策略 | 事件触发 | 单用户实时 | 规则决策 | 实时营销、即时响应 |
| 离线策略 | 定时调度 | 批量处理 | 规则决策 | 定期营销、批量触达 |
| T0引擎策略 | 事件触发 | 单用户实时 | AI决策 | 智能实时营销 |
| 离线引擎策略 | 定时调度 | 分布式批量 | AI决策 | 大规模智能营销 |

## 6. 技术架构要点

### 6.1 消息队列集成
- 使用RocketMQ处理实时事件消息
- 支持消息幂等和重试机制

### 6.2 分布式计算
- 基于XXL-JOB的任务调度
- 支持分片和并行处理

### 6.3 AI引擎集成
- 通过ModelPredictionFacade调用决策引擎
- 支持多种模型和灰度发布

### 6.4 流控和监控
- 实现多层级流控机制
- 完善的监控和告警体系

---
*本报告基于CDP系统代码分析生成，详细展示了四种策略的实现逻辑和业务特点。*
