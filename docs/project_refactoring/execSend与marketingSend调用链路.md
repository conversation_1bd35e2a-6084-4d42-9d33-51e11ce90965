# execSend与marketingSend调用链路分析报告

## 1. 概述

本文档详细分析`StrategyEventDispatchServiceImpl`中`execSend`和`marketingSend`两个核心方法的调用链路、业务场景和实现差异。通过深入分析可以明确：**execSend方法绝对不会调用marketingSend方法**，它们是两个完全独立的执行路径，服务于不同的业务场景。

## 2. 方法基本信息对比

### 2.1 execSend方法
- **方法位置**: `StrategyEventDispatchServiceImpl.java:1148-1191`
- **业务场景**: T0-普通触达链路
- **调用方式**: 私有方法，内部调用
- **返回值**: `ImmutableTriple<Integer, EventPushBatchDo, Boolean>`

### 2.2 marketingSend方法
- **方法位置**: `StrategyEventDispatchServiceImpl.java:1229-1647`
- **业务场景**: T0-引擎触达链路
- **调用方式**: 公有方法，可外部调用
- **返回值**: `int`

## 3. 调用链路对比分析

### 3.1 T0-普通触达链路 (execSend)

#### 3.1.1 完整调用链路
```
MQ消息消费 → prescreen预筛 → rescreen复筛 → dispatchHandler分发处理 →
mqProducerService.channelDelivery → dispatch触达 → execSend执行发送
```

#### 3.1.2 关键调用位置
```java
// 位置: StrategyEventDispatchServiceImpl.java:1075
dispatchResult = this.execSend(dispatchDto, newCrowd, marketChannelEnum, triple.getMiddle(), event);
```

#### 3.1.3 execSend方法签名
```java
// 位置: StrategyEventDispatchServiceImpl.java:1148
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(
    DispatchDto dispatchDto,
    CrowdDetailDo crowdDetail,
    StrategyMarketChannelEnum channelEnum,
    StrategyMarketChannelDo channelDo,
    BizEventVO bizEvent)
```

### 3.2 T0-引擎触达链路 (marketingSend)

#### 3.2.1 完整调用链路
```
MQ消息消费 → prescreen预筛 → rescreenWithEngine引擎复筛 →
引擎决策调用 → marketingSend营销发送
```

#### 3.2.2 关键调用位置
```java
// 位置: StrategyEventDispatchServiceImpl.java:655
int sendRet = marketingSend(dispatchDto, crowdDetailDo, strategyMarketChannelEnum,
    action.getGroup_id(), dispatch.getDetail_info(), eventCopy);
```

#### 3.2.3 marketingSend方法签名
```java
// 位置: StrategyEventDispatchServiceImpl.java:1229
public int marketingSend(
    DispatchDto dispatchDto,
    CrowdDetailDo crowdDetailDo,
    StrategyMarketChannelEnum channelEnum,
    String groupName,
    Map detailInfo,
    @Nullable BizEventVO bizEventVO)
```

## 4. 渠道处理方式对比

### 4.1 execSend渠道处理方式

#### 4.1.1 通过EventDispatchService适配层
```java
// 位置: StrategyEventDispatchServiceImpl.java:1151-1184
switch (channelEnum) {
    case SMS:
        dispatchResult = eventDispatchService.sendSmsEvent(dispatchDto,
            crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent, channelDo.getExtInfo());
        break;
    case VOICE:
        dispatchResult = eventDispatchService.sendTeleEvent(dispatchDto,
            crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail);
        break;
    case PUSH:
        dispatchResult = eventDispatchService.sendPushEvent(dispatchDto,
            crowdDetail.getApp(), crowdDetail.getInnerApp(), crowdDetail, bizEvent);
        break;
    // ... 其他渠道
}
```

#### 4.1.2 支持的渠道类型
- `SMS`: 短信渠道
- `VOICE`: 电销渠道
- `VOICE_NEW`: 新电销渠道
- `SALE_TICKET`: 优惠券渠道
- `PUSH`: 推送渠道
- `INCREASE_AMOUNT`: 提额渠道
- `LIFE_RIGHTS`: 生活权益渠道
- `X_DAY_INTEREST_FREE`: X天免息渠道
- `AI_PRONTO`: AI智能外呼渠道
- `NONE`: 空白对照组

### 4.2 marketingSend渠道处理方式

#### 4.2.1 直接构建外部服务调用参数
```java
// 位置: StrategyEventDispatchServiceImpl.java:1239-1647
switch (channelEnum) {
    case SMS:
        // 直接构建短信发送参数
        SmsSingleSendArgs smsSingleSendArgs = new SmsSingleSendArgs();
        // ... 参数设置
        SmsSingleSendResp resp = eventDispatchService.sendSms(smsSingleSendRequester);
        break;
    case AI_PRONTO:
        // 直接构建AI外呼参数
        AiSendArgs aiSendArgs = new AiSendArgs();
        // ... 参数设置
        AiSendResp aiSendResp = eventDispatchService.sendAiPronto(aiSendArgs);
        break;
    // ... 其他渠道
}
```

#### 4.2.2 支持的渠道类型
- `REMAINING_SUM_CHARGING`: 余额充值渠道 (独有)
- `SMS`: 短信渠道
- `AI_PRONTO`: AI智能外呼渠道
- `PUSH`: 推送渠道
- `INCREASE_AMOUNT`: 提额渠道
- `VOICE`: 电销渠道
- `VOICE_NEW`: 新电销渠道
- `SALE_TICKET`: 优惠券渠道
- `X_DAY_INTEREST_FREE`: X天免息渠道
- `NONE`: 空白对照组

## 5. 核心差异分析

### 5.1 参数来源差异

#### 5.1.1 execSend参数来源
- **策略配置**: 来自`strategy_market_channel`等配置表
- **模板参数**: 通过`TemplateParamService`生成
- **用户信息**: 经过用户转换处理

#### 5.1.2 marketingSend参数来源
- **引擎决策**: 来自AI决策引擎的实时决策结果
- **动态参数**: `detailInfo`包含引擎返回的动态参数
- **模板信息**: 从引擎决策结果中获取

### 5.2 流控机制差异

#### 5.2.1 execSend流控方式
```java
// 位置: StrategyEventDispatchServiceImpl.java:1060
boolean flcRet = dispatchFlcService.dispatchFlc(event, newCrowd, this);
```
- **预先流控**: 在调用execSend之前进行流控检查
- **统一流控**: 使用统一的流控服务

#### 5.2.2 marketingSend流控方式
```java
// 位置: StrategyEventDispatchServiceImpl.java:1278
boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo,
    dispatchDetail, batchNum, crowdDetailDo, this);
```
- **分布式锁流控**: 使用分布式锁进行流控
- **渠道内流控**: 在每个渠道处理内部进行流控

### 5.3 返回值处理差异

#### 5.3.1 execSend返回值
```java
ImmutableTriple<Integer, EventPushBatchDo, Boolean>
// Integer: 发送数量
// EventPushBatchDo: 批次记录对象
// Boolean: 是否发生限流
```

#### 5.3.2 marketingSend返回值
```java
int
// -999: 发生流控错误
// -1: 无需发送
// 其他值: 表示发送的数量
```

## 6. 业务场景对比

### 6.1 T0-普通触达业务场景 (execSend)

#### 6.1.1 触发条件
- 用户行为事件触发 (如登录、还款等)
- 基于预配置的策略规则
- 通过预筛、复筛后进入触达阶段

#### 6.1.2 决策方式
- **规则驱动**: 基于预配置的业务规则
- **标签匹配**: 通过用户标签进行条件匹配
- **静态配置**: 策略配置相对固定

#### 6.1.3 适用场景
- 常规营销活动
- 基于用户行为的自动化营销
- 大规模批量触达

### 6.2 T0-引擎触达业务场景 (marketingSend)

#### 6.2.1 触发条件
- 用户行为事件触发
- 通过AI决策引擎实时决策
- 引擎返回具体的营销动作

#### 6.2.2 决策方式
- **AI驱动**: 基于机器学习模型的实时决策
- **动态参数**: 引擎返回动态的营销参数
- **个性化**: 每个用户的营销内容可能不同

#### 6.2.3 适用场景
- 智能化营销
- 个性化推荐
- 实时决策场景

## 7. 架构设计优势

### 7.1 职责分离
- **execSend**: 专注于基于规则的标准化触达流程
- **marketingSend**: 专注于基于AI决策的智能化触达流程
- **清晰边界**: 两种模式互不干扰，便于维护和扩展

### 7.2 扩展性
- **独立演进**: 两种模式可以独立演进和优化
- **技术栈**: 可以采用不同的技术栈和优化策略
- **性能调优**: 针对不同场景进行专门的性能优化

### 7.3 风险隔离
- **故障隔离**: 一种模式的故障不会影响另一种模式
- **流控独立**: 不同的流控策略适应不同的业务特点
- **监控分离**: 可以针对不同模式设置不同的监控策略

## 8. 调用链路流程图

### 8.1 T0-普通触达execSend调用链路图

```mermaid
graph TD
    A[MQ消息消费] --> B[prescreen预筛]
    B --> C[rescreen复筛]
    C --> D[dispatchHandler分发处理]
    D --> E[mqProducerService.channelDelivery]
    E --> F[dispatch触达]
    F --> G[流控检查dispatchFlc]
    G --> H[execSend执行发送]
    H --> I[EventDispatchService适配层]
    I --> J1[sendSmsEvent短信]
    I --> J2[sendTeleEvent电销]
    I --> J3[sendCouponEvent优惠券]
    I --> J4[sendPushEvent推送]
    I --> J5[sendAiProntoEvent AI外呼]

    style H fill:#e1f5fe
    style I fill:#f3e5f5
```

### 8.2 T0-引擎触达marketingSend调用链路图

```mermaid
graph TD
    A[MQ消息消费] --> B[prescreen预筛]
    B --> C[rescreenWithEngine引擎复筛]
    C --> D[引擎决策调用]
    D --> E[marketingSend营销发送]
    E --> F[分布式锁流控检查]
    F --> G[直接调用外部服务]
    G --> H1[短信服务SMS]
    G --> H2[电销服务VOICE]
    G --> H3[优惠券服务COUPON]
    G --> H4[推送服务PUSH]
    G --> H5[AI外呼服务AI_PRONTO]
    G --> H6[提额服务INCREASE_AMOUNT]
    G --> H7[余额充值REMAINING_SUM_CHARGING]

    style E fill:#fff3e0
    style G fill:#e8f5e8
```

## 9. 调用链路对比表

| 对比维度 | execSend方法 | marketingSend方法 |
|---------|-------------|------------------|
| **调用场景** | T0-普通触达 | T0-引擎触达 |
| **调用入口** | `dispatch()` | `rescreenWithEngine()` |
| **参数来源** | 策略配置表 | 引擎决策结果 |
| **流控方式** | 预先流控检查 | 分布式锁流控 |
| **外部调用** | 通过EventDispatchService | 直接调用外部服务 |
| **返回值** | `ImmutableTriple<Integer, EventPushBatchDo, Boolean>` | `int` |
| **支持渠道** | 10种渠道 | 10种渠道(含REMAINING_SUM_CHARGING) |
| **决策方式** | 规则驱动 | AI驱动 |
| **个性化程度** | 标准化 | 个性化 |
| **实时性** | 准实时 | 实时 |

## 10. 关键代码位置总结

### 10.1 execSend相关代码位置
- **方法定义**: `StrategyEventDispatchServiceImpl.java:1148-1191`
- **调用位置**: `StrategyEventDispatchServiceImpl.java:1075`
- **流控检查**: `StrategyEventDispatchServiceImpl.java:1060`
- **用户转换**: `StrategyEventDispatchServiceImpl.java:1742-1767`

### 10.2 marketingSend相关代码位置
- **方法定义**: `StrategyEventDispatchServiceImpl.java:1229-1647`
- **调用位置**: `StrategyEventDispatchServiceImpl.java:655`
- **引擎复筛**: `StrategyEventDispatchServiceImpl.java:602-655`
- **分布式锁流控**: 各渠道case内部调用

## 11. 总结

通过深入分析`execSend`和`marketingSend`两个方法的实现和调用链路，可以得出以下结论：

### 11.1 独立性
- **完全独立**: 两个方法在调用链路上完全独立，execSend绝不会调用marketingSend
- **不同入口**: 分别从不同的业务入口进入，服务于不同的业务场景
- **独立演进**: 可以独立进行功能迭代和性能优化

### 11.2 互补性
- **场景互补**: 普通触达和引擎触达覆盖了不同的营销场景
- **技术互补**: 规则驱动和AI驱动形成技术上的互补
- **能力互补**: 标准化和个性化能力的互补

### 11.3 架构价值
- **清晰职责**: 每个方法都有明确的职责边界
- **高内聚**: 相关功能聚合在一起
- **低耦合**: 不同模式之间耦合度很低
- **易维护**: 便于理解、维护和扩展

这种设计体现了良好的软件架构原则，为业务的快速发展和技术的持续演进提供了坚实的基础。