# 流控重构抽象设计方案

## 1. 现状分析

### 1.1 流控方式调用链分析

根据代码分析，当前系统的流控实现可以分为两大类：

| 流控类型 | 实现方式 | 是否调用FlowCtrlCoreServiceImpl.flowCtrl | 存储方式 | 控制粒度 |
|---------|---------|----------------------------------------|---------|---------|
| **事件级流控** | `MqConsumeServiceImpl.isReject()` | ❌ **否** - 独立Redis锁实现 | Redis分布式锁 | 事件+用户维度 |
| **触达级流控** | `DispatchFlcService.dispatchFlc()` | ✅ **是** - 通过AbstractStrategyEventDispatchService.flowCtrl() | 数据库查询 | 策略+渠道+用户维度 |
| **分布式流控** | `DispatchFlcService.dispatchFlcLock()` | ✅ **是** - 通过AbstractStrategyEventDispatchService.flowCtrl() | Redis锁+数据库查询 | 策略+渠道+用户维度 |
| **离线批量流控** | 直接调用 | ✅ **是** - 直接调用FlowCtrlCoreServiceImpl.flowCtrl() | 数据库查询 | 批量用户维度 |
| **引擎内置流控** | `marketingSend()内的dispatchFlcLock()` | ✅ **是** - 通过dispatchFlcLock()调用链 | Redis锁+数据库查询 | 策略+渠道+用户维度 |

### 1.2 问题识别

1. **实现分散**: 事件级流控独立实现，其他流控都依赖FlowCtrlCoreServiceImpl.flowCtrl()
2. **逻辑重复**: 多个地方都有相似的流控前置检查逻辑
3. **配置分散**: 不同流控方式的开关配置分散在不同地方
4. **难以扩展**: 新增流控类型需要修改多个地方

## 2. 统一抽象设计

### 2.1 核心抽象接口

```java
/**
 * 统一流控策略接口
 */
public interface FlowControlStrategy {
    
    /**
     * 执行流控检查
     * @param context 流控上下文
     * @return 流控结果
     */
    FlowControlResult execute(FlowControlContext context);
    
    /**
     * 获取流控类型
     */
    FlowControlType getType();
    
    /**
     * 是否支持该触达类型
     */
    boolean supports(TouchType touchType);
}
```

### 2.2 流控上下文模型

```java
/**
 * 流控上下文 - 包含所有流控所需信息
 */
@Data
public class FlowControlContext {
    
    // 基础信息
    private Long userId;
    private Long strategyId;
    private Integer marketChannel;
    private String eventType;
    private String messageId;
    private LocalDateTime triggerTime;
    
    // 用户信息
    private CrowdDetailDo crowdDetail;
    private List<CrowdDetailDo> crowdDetailList;
    
    // 策略信息
    private StrategyMarketChannelDo channelDo;
    private List<FlowCtrlDo> flowCtrlRules;
    
    // 流控配置
    private FlowControlConfig config;
    
    // 分表信息
    private Integer tableNo;
    
    // 触达类型
    private TouchType touchType;
    
    // 批次信息（批量流控使用）
    private String batchNum;
    private EventPushBatchDo eventPushBatch;
    private UserDispatchDetailDo dispatchDetail;
}
```

### 2.3 流控结果模型

```java
/**
 * 流控结果
 */
@Data
public class FlowControlResult {
    
    // 是否通过流控
    private boolean passed;
    
    // 被拦截的原因
    private String rejectReason;
    
    // 流控类型
    private FlowControlType flowControlType;
    
    // 通过流控的用户列表（批量流控使用）
    private List<CrowdDetailDo> passedUsers;
    
    // 被拦截的用户列表（批量流控使用）
    private List<CrowdDetailDo> rejectedUsers;
    
    // 流控耗时
    private long costTime;
    
    // 静态工厂方法
    public static FlowControlResult passed() {
        FlowControlResult result = new FlowControlResult();
        result.setPassed(true);
        return result;
    }
    
    public static FlowControlResult rejected(String reason, FlowControlType type) {
        FlowControlResult result = new FlowControlResult();
        result.setPassed(false);
        result.setRejectReason(reason);
        result.setFlowControlType(type);
        return result;
    }
}
```

### 2.4 流控类型枚举

```java
/**
 * 流控类型枚举
 */
public enum FlowControlType {
    
    EVENT_LEVEL("事件级流控", "基于事件类型和用户的Redis锁流控"),
    DISPATCH_LEVEL("触达级流控", "基于策略渠道的数据库查询流控"),
    DISTRIBUTED_LOCK("分布式流控", "基于Redis分布式锁的流控"),
    BATCH_LEVEL("批量流控", "离线批量处理的流控"),
    ENGINE_INTERNAL("引擎内置流控", "引擎策略内置的流控");
    
    private final String description;
    private final String detail;
    
    FlowControlType(String description, String detail) {
        this.description = description;
        this.detail = detail;
    }
}
```

## 3. 具体策略实现

### 3.1 事件级流控策略

```java
/**
 * 事件级流控策略 - 对应原isReject()逻辑
 */
@Component
public class EventLevelFlowControlStrategy implements FlowControlStrategy {
    
    @Autowired
    private AppConfigService appConfigService;
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Override
    public FlowControlResult execute(FlowControlContext context) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 参数校验
            if (StringUtils.isEmpty(context.getEventType()) || context.getUserId() == null || context.getUserId() <= 0) {
                return FlowControlResult.passed();
            }
            
            // 2. 获取事件流控配置
            EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
            if (eventFlcConfig == null) {
                return FlowControlResult.passed();
            }
            
            // 3. 获取该事件类型的限制秒数
            Integer limitSeconds = eventFlcConfig.getLimitSeconds(context.getEventType());
            if (limitSeconds == null) {
                return FlowControlResult.passed();
            }
            
            // 4. 配置为0或负数，直接拦截
            if (limitSeconds <= 0) {
                return FlowControlResult.rejected("事件级流控配置禁止", FlowControlType.EVENT_LEVEL);
            }
            
            // 5. 构造Redis Key并尝试获取锁
            String limitKey = String.format("eventflc:%s:%s", context.getEventType(), context.getUserId());
            boolean lockSuccess = redisUtils.lock(limitKey, "1", limitSeconds);
            
            if (!lockSuccess) {
                return FlowControlResult.rejected("事件级流控频次限制", FlowControlType.EVENT_LEVEL);
            }
            
            return FlowControlResult.passed();
            
        } catch (Exception e) {
            log.error("事件级流控异常: eventType={}, userId={}", context.getEventType(), context.getUserId(), e);
            // 异常情况下放行，避免影响业务
            return FlowControlResult.passed();
        } finally {
            long costTime = System.currentTimeMillis() - startTime;
            log.debug("事件级流控耗时: {}ms", costTime);
        }
    }
    
    @Override
    public FlowControlType getType() {
        return FlowControlType.EVENT_LEVEL;
    }
    
    @Override
    public boolean supports(TouchType touchType) {
        // 事件级流控支持所有触达类型
        return true;
    }
}
```

### 3.2 数据库流控策略（统一触达级、分布式、批量流控）

```java
/**
 * 数据库流控策略 - 统一原dispatchFlc、dispatchFlcLock、flowCtrl逻辑
 */
@Component
public class DatabaseFlowControlStrategy implements FlowControlStrategy {
    
    @Autowired
    private FlowCtrlCoreService flowCtrlCoreService;
    
    @Autowired
    private RedisUtils redisUtils;
    
    @Override
    public FlowControlResult execute(FlowControlContext context) {
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 检查是否需要分布式锁
            boolean needDistributedLock = needDistributedLock(context);
            String lockValue = null;
            
            if (needDistributedLock) {
                lockValue = String.valueOf(System.currentTimeMillis());
                tryLock(context.getUserId(), lockValue);
            }
            
            try {
                // 2. 构造FlowCtrlDto
                FlowCtrlDto flowCtrlDto = buildFlowCtrlDto(context);
                
                // 3. 执行核心流控逻辑
                List<Integer> statusList = Arrays.asList(-1, 1);
                List<CrowdDetailDo> passedUsers = flowCtrlCoreService.flowCtrl(flowCtrlDto, statusList);
                
                // 4. 构造结果
                FlowControlResult result = new FlowControlResult();
                result.setPassed(!CollectionUtils.isEmpty(passedUsers));
                result.setPassedUsers(passedUsers);
                result.setCostTime(System.currentTimeMillis() - startTime);
                
                if (!result.isPassed()) {
                    result.setRejectReason("数据库流控规则拦截");
                    result.setFlowControlType(getFlowControlType(context));
                }
                
                return result;
                
            } finally {
                // 5. 释放分布式锁
                if (needDistributedLock && lockValue != null) {
                    unLock(context.getUserId(), lockValue);
                }
            }
            
        } catch (Exception e) {
            log.error("数据库流控异常: userId={}, strategyId={}", context.getUserId(), context.getStrategyId(), e);
            return FlowControlResult.passed(); // 异常情况下放行
        }
    }
    
    private boolean needDistributedLock(FlowControlContext context) {
        // 根据触达类型和配置决定是否需要分布式锁
        return context.getTouchType() == TouchType.T0_ENGINE || 
               context.getTouchType() == TouchType.T0_NORMAL;
    }
    
    private FlowCtrlDto buildFlowCtrlDto(FlowControlContext context) {
        FlowCtrlDto flowCtrlDto = new FlowCtrlDto();
        flowCtrlDto.setTableNo(context.getTableNo());
        flowCtrlDto.setMarketChannelDo(context.getChannelDo());
        flowCtrlDto.setFlowCtrlRuleList(context.getFlowCtrlRules());
        flowCtrlDto.setMessageId(context.getMessageId());
        flowCtrlDto.setTriggerDatetime(context.getTriggerTime());
        
        // 根据是否批量设置用户列表
        if (context.getCrowdDetailList() != null) {
            flowCtrlDto.setList(context.getCrowdDetailList());
        } else {
            flowCtrlDto.setList(Collections.singletonList(context.getCrowdDetail()));
        }
        
        return flowCtrlDto;
    }
    
    private FlowControlType getFlowControlType(FlowControlContext context) {
        switch (context.getTouchType()) {
            case T0_NORMAL:
                return FlowControlType.DISPATCH_LEVEL;
            case T0_ENGINE:
            case OFFLINE_ENGINE:
                return FlowControlType.DISTRIBUTED_LOCK;
            case OFFLINE_NORMAL:
                return FlowControlType.BATCH_LEVEL;
            default:
                return FlowControlType.DISPATCH_LEVEL;
        }
    }
}
```

## 4. 统一流控服务

### 4.1 流控编排服务

```java
/**
 * 统一流控服务 - 负责流控策略的编排和执行
 */
@Service
public class UnifiedFlowControlService {

    @Autowired
    private List<FlowControlStrategy> flowControlStrategies;

    @Autowired
    private FlowControlConfigService configService;

    /**
     * 执行完整的流控检查
     */
    public FlowControlResult executeFlowControl(FlowControlContext context) {
        log.info("开始执行统一流控: userId={}, strategyId={}, touchType={}",
                context.getUserId(), context.getStrategyId(), context.getTouchType());

        // 1. 获取流控配置
        FlowControlConfig config = configService.getFlowControlConfig(context.getTouchType());
        context.setConfig(config);

        // 2. 按顺序执行各种流控策略
        List<FlowControlStrategy> applicableStrategies = getApplicableStrategies(context);

        for (FlowControlStrategy strategy : applicableStrategies) {
            if (!isStrategyEnabled(strategy, config)) {
                log.debug("流控策略已禁用: {}", strategy.getType());
                continue;
            }

            FlowControlResult result = strategy.execute(context);

            // 记录流控结果
            recordFlowControlResult(strategy.getType(), result, context);

            // 如果被拦截，直接返回
            if (!result.isPassed()) {
                log.warn("流控拦截: type={}, reason={}, userId={}, strategyId={}",
                        strategy.getType(), result.getRejectReason(),
                        context.getUserId(), context.getStrategyId());
                return result;
            }
        }

        log.info("流控检查通过: userId={}, strategyId={}", context.getUserId(), context.getStrategyId());
        return FlowControlResult.passed();
    }

    /**
     * 获取适用的流控策略
     */
    private List<FlowControlStrategy> getApplicableStrategies(FlowControlContext context) {
        return flowControlStrategies.stream()
                .filter(strategy -> strategy.supports(context.getTouchType()))
                .sorted(this::compareStrategyPriority)
                .collect(Collectors.toList());
    }

    /**
     * 流控策略优先级排序
     */
    private int compareStrategyPriority(FlowControlStrategy s1, FlowControlStrategy s2) {
        // 定义执行顺序: 事件级 -> 数据库级
        Map<FlowControlType, Integer> priorityMap = Map.of(
                FlowControlType.EVENT_LEVEL, 1,
                FlowControlType.DISPATCH_LEVEL, 2,
                FlowControlType.DISTRIBUTED_LOCK, 2,
                FlowControlType.BATCH_LEVEL, 2,
                FlowControlType.ENGINE_INTERNAL, 2
        );

        return priorityMap.get(s1.getType()).compareTo(priorityMap.get(s2.getType()));
    }
}
```

### 4.2 配置管理服务

```java
/**
 * 流控配置管理服务
 */
@Service
public class FlowControlConfigService {

    @Autowired
    private AppConfigService appConfigService;

    /**
     * 根据触达类型获取流控配置
     */
    public FlowControlConfig getFlowControlConfig(TouchType touchType) {
        FlowControlConfig config = new FlowControlConfig();

        switch (touchType) {
            case T0_NORMAL:
                config.setEnableEventFlowControl(true);
                config.setEnableTouchFlowControl(appConfigService.getSingleDispatchFlcSwitch(getChannelEnum(touchType)));
                config.setEnableDistributedFlowControl(false);
                config.setEnableBatchFlowControl(false);
                break;

            case T0_ENGINE:
                config.setEnableEventFlowControl(true);
                config.setEnableTouchFlowControl(false);
                config.setEnableDistributedFlowControl(appConfigService.getSingleDispatchFlcLockSwitch(getChannelEnum(touchType)));
                config.setEnableBatchFlowControl(false);
                break;

            case OFFLINE_NORMAL:
                config.setEnableEventFlowControl(false);
                config.setEnableTouchFlowControl(false);
                config.setEnableDistributedFlowControl(false);
                config.setEnableBatchFlowControl(true);
                break;

            case OFFLINE_ENGINE:
                config.setEnableEventFlowControl(false);
                config.setEnableTouchFlowControl(false);
                config.setEnableDistributedFlowControl(true);
                config.setEnableBatchFlowControl(false);
                break;
        }

        return config;
    }
}
```

## 5. 重构实施方案

### 5.1 渐进式重构策略

**阶段一：抽象层建设**
1. 创建统一的流控接口和模型
2. 实现事件级流控策略（替换isReject）
3. 实现数据库流控策略（统一其他流控）
4. 创建统一流控服务

**阶段二：逐步替换**
1. 在新的触达统一服务中使用统一流控
2. 保持原有流控逻辑作为备用
3. 通过开关控制新旧流控的使用

**阶段三：完全切换**
1. 验证新流控的稳定性和性能
2. 逐步下线原有分散的流控逻辑
3. 清理冗余代码

### 5.2 兼容性保证

```java
/**
 * 流控适配器 - 保证向后兼容
 */
@Component
public class FlowControlAdapter {

    @Autowired
    private UnifiedFlowControlService unifiedFlowControlService;

    /**
     * 适配原isReject方法
     */
    public boolean isReject(BizEventMessageVO bizEventMessageVO) {
        FlowControlContext context = buildContextFromBizEvent(bizEventMessageVO);
        FlowControlResult result = unifiedFlowControlService.executeFlowControl(context);
        return !result.isPassed();
    }

    /**
     * 适配原dispatchFlc方法
     */
    public boolean dispatchFlc(BizEventVO bizEventVO, CrowdDetailDo crowdDetail,
                              AbstractStrategyEventDispatchService abstractStrategyEventDispatchService) {
        FlowControlContext context = buildContextFromDispatch(bizEventVO, crowdDetail);
        FlowControlResult result = unifiedFlowControlService.executeFlowControl(context);
        return !result.isPassed();
    }

    /**
     * 适配原flowCtrl方法
     */
    public List<CrowdDetailDo> flowCtrl(FlowCtrlDto flowCtrlDto, List<Integer> statusList) {
        FlowControlContext context = buildContextFromFlowCtrlDto(flowCtrlDto);
        FlowControlResult result = unifiedFlowControlService.executeFlowControl(context);
        return result.isPassed() ? result.getPassedUsers() : Collections.emptyList();
    }
}
```

## 6. 重构收益

### 6.1 架构收益

1. **统一抽象**: 所有流控逻辑统一到一个抽象层
2. **策略模式**: 不同流控方式作为独立策略，易于扩展
3. **配置统一**: 所有流控配置集中管理
4. **代码复用**: 消除重复的流控逻辑

### 6.2 维护收益

1. **逻辑清晰**: 每种流控策略职责单一
2. **易于测试**: 每个策略可以独立测试
3. **问题定位**: 流控问题可以快速定位到具体策略
4. **监控统一**: 统一的流控监控和指标

### 6.3 扩展收益

1. **新增流控**: 只需实现FlowControlStrategy接口
2. **组合流控**: 可以灵活组合不同的流控策略
3. **动态配置**: 支持运行时动态调整流控策略
4. **A/B测试**: 支持不同流控策略的A/B测试

## 7. 总结

通过统一抽象设计，我们可以：

1. **确认您的判断基本正确**: 除了事件级流控(isReject)独立实现外，其他流控方式确实都最终调用了`FlowCtrlCoreServiceImpl.flowCtrl()`

2. **解决现有问题**:
   - 统一分散的流控实现
   - 消除重复的流控逻辑
   - 提供清晰的扩展机制

3. **提供渐进式重构路径**:
   - 保证向后兼容
   - 支持灰度切换
   - 降低重构风险

4. **建立可扩展架构**:
   - 策略模式支持新增流控类型
   - 统一配置管理
   - 完善的监控和测试体系
```
