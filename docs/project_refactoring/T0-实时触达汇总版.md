# T0-实时触达汇总版分析报告

## 1. 概述

本文档汇总分析T0实时触达的完整链路，包括T0-普通触达(execSend)和T0-引擎触达(marketingSend)两种模式。通过对比分析发现，两种模式的核心差异在于**是否调用引擎进行决策**，其他流程具有高度相似性。

## 2. 整体架构对比

### 2.1 完整调用链路图

```mermaid
graph TD
    A[业务事件MQ] --> B[消息消费层]
    B --> C[MessageHandlerSelector路由]
    C --> D[StrategyEventDispatchService.prescreen]
    D --> E[策略预筛]
    E --> F[引擎灰度判定]
    F --> G[事件预筛]
    G --> H[人群预筛]
    H --> I[延迟处理]
    I --> J[StrategyEventDispatchService.rescreen]
    J --> K[实时标签查询]
    K --> L[策略复筛]
    L --> M{引擎策略判断}
    
    M -->|普通策略| N[rescreeningHandler复筛]
    N --> O[dispatchHandler触达分发]
    O --> P[mqProducerService.channelDelivery]
    P --> Q[MQ异步队列]
    Q --> R[dispatch方法]
    R --> S[execSend执行]
    S --> T[EventDispatchService渠道适配]

    M -->|引擎策略| U[rescreenWithEngine引擎决策]
    U --> V[引擎预测调用]
    V --> W[解析引擎决策结果]
    W --> X[直接调用marketingSend]
    X --> T
    
    T --> Y[外部服务调用]
    Y --> Z[短信/电销/优惠券/Push等]
    
    style M fill:#ffeb3b
    style N fill:#e3f2fd
    style U fill:#f3e5f5
    style S fill:#e3f2fd
    style X fill:#f3e5f5
```

### 2.2 关键类和方法对应关系

| 序号 | 类名 | 关键方法 | 代码位置 | 功能说明 | 触达类型 |
|------|------|----------|----------|----------|----------|
| 1 | `LifeRightsRocketMqConsumer` | `doMessage()` | `cdp-infrastructure/.../LifeRightsRocketMqConsumer.java:45` | MQ消息消费入口 | 通用 |
| 2 | `StrategyEventDispatchServiceImpl` | `prescreen()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:193` | 预筛阶段入口 | 通用 |
| 3 | `StrategyEventDispatchServiceImpl` | `rescreen()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:483` | 复筛阶段入口 | 通用 |
| 4 | `StrategyEventDispatchServiceImpl` | `rescreenWithEngine()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:602` | 引擎决策处理 | 引擎触达 |
| 5 | `StrategyEventDispatchServiceImpl` | `rescreeningHandler()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:608` | 普通策略复筛 | 普通触达 |
| 6 | `StrategyEventDispatchServiceImpl` | `dispatch()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:1040` | 触达分发入口 | 普通触达 |
| 7 | `StrategyEventDispatchServiceImpl` | `execSend()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:1148` | 普通触达执行 | 普通触达 |
| 8 | `StrategyEventDispatchServiceImpl` | `marketingSend()` | `cdp-domain/.../StrategyEventDispatchServiceImpl.java:1229` | 营销发送统一入口 | 通用 |
| 9 | `EventDispatchServiceImpl` | `sendSmsEvent()` | `cdp-domain/.../EventDispatchServiceImpl.java:118` | 短信渠道发送 | 通用 |
| 10 | `EventDispatchServiceImpl` | `sendTeleEvent()` | `cdp-domain/.../EventDispatchServiceImpl.java:154` | 电销渠道发送 | 通用 |

### 2.3 方法调用时序图

```mermaid
sequenceDiagram
    participant MQ as MQ消费者
    participant SEDS as StrategyEventDispatchServiceImpl
    participant Engine as 决策引擎
    participant EDS as EventDispatchService
    participant External as 外部服务

    Note over MQ,External: 通用流程：预筛和复筛
    MQ->>SEDS: doMessage() → prescreen()
    SEDS->>SEDS: 策略预筛 → 事件预筛 → 人群预筛 → 延迟处理
    SEDS->>SEDS: rescreen() → queryLabelHandler()
    
    Note over MQ,External: 分支1：普通触达流程
    SEDS->>SEDS: rescreeningHandler() (普通策略复筛)
    SEDS->>SEDS: dispatchHandler() → mqProducerService.channelDelivery()
    SEDS->>MQ: 投递到MQ队列 (异步)
    MQ->>SEDS: dispatch() → execSend()
    SEDS->>EDS: sendSmsEvent()/sendTeleEvent()等
    EDS->>External: 外部服务调用
    External-->>EDS: 发送结果
    EDS-->>SEDS: 返回结果

    Note over MQ,External: 分支2：引擎触达流程
    SEDS->>SEDS: rescreenWithEngine() (引擎策略处理)
    SEDS->>Engine: 引擎预测调用
    Engine-->>SEDS: 决策结果
    SEDS->>SEDS: marketingSend() (直接同步调用)
    SEDS->>EDS: sendSmsEvent()/sendTeleEvent()等
    EDS->>External: 外部服务调用
    External-->>EDS: 发送结果
    EDS-->>SEDS: 返回结果
```

### 2.4 核心差异对比分析

| 对比维度 | T0-普通触达(execSend) | T0-引擎触达(marketingSend) | 差异说明 |
|----------|----------------------|---------------------------|----------|
| **决策方式** | 基于配置规则的标签匹配 | AI引擎智能决策 | 核心差异：是否调用引擎 |
| **复筛逻辑** | `rescreeningHandler()` | `rescreenWithEngine()` | 不同的复筛处理方法 |
| **触达路径** | rescreen → dispatchHandler → MQ → dispatch → execSend | rescreen → rescreenWithEngine → marketingSend | 普通触达经过MQ异步，引擎触达直接同步 |
| **灰度控制** | 无引擎灰度判定 | 有引擎灰度判定(`isInEngineGrayGroup`) | 引擎触达支持灰度发布 |
| **决策结果** | 通过/不通过二元结果 | 营销动作、渠道、参数等详细决策 | 引擎提供更丰富的决策信息 |
| **执行时机** | 复筛通过后异步执行 | 引擎决策后同步执行 | 普通触达有MQ延迟，引擎触达立即执行 |
| **最终执行** | execSend → EventDispatchService | marketingSend → EventDispatchService | 两种不同的执行入口，但都调用相同的渠道服务 |
| **外部服务** | 相同的EventDispatchService | 相同的EventDispatchService | 外部服务调用逻辑完全相同 |

## 3. 共同流程详细分析

### 3.1 预筛阶段(prescreen) - 通用流程

#### 3.1.1 策略预筛处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:228
this.strategyPrescreenHandler(eventContext);
```
- **策略状态检查**: 验证策略是否处于执行状态
- **有效期检查**: 验证策略是否在有效期内
- **流控检查**: 检查策略级别的流控规则

#### 3.1.2 引擎灰度判定(仅引擎策略)
```java
// 位置: StrategyEventDispatchServiceImpl.java:230-243
if (eventContext.getStrategyDo().isEngineStrategy()) {
    String engineCode = eventContext.getBizEventVO().getEngineCode();
    if (!isInEngineGrayGroup(eventContext, group)) {
        eventContext.getBizEventVO().setIfIntoEngine(false);
    }
}
```

#### 3.1.3 事件预筛处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:245
this.eventPrescreenHandler(eventContext);
```
- **数据表**: `strategy_market_sub_event`
- **业务逻辑**: 支持一个主事件下配置多个子事件类型

#### 3.1.4 人群预筛处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:247
this.crowdPrescreenHandler(eventContext, bizEventMessageVO);
```
- **数据表**: `crowd_pack`, `strategy_crowd_pack`, `crowd_detail_*`
- **外部接口**: 洞察平台人群包查询接口

### 3.2 复筛阶段(rescreen) - 通用入口，分支处理

#### 3.2.1 实时标签查询(通用)
```java
// 位置: StrategyEventDispatchServiceImpl.java:606
this.queryLabelHandler(eventContext);
```
- **外部接口**: 数仓实时标签查询接口
- **配置**: `cdp.ads.host = http://api-dps.xinfei.io`

#### 3.2.2 分支处理逻辑
```java
// 位置: StrategyEventDispatchServiceImpl.java:602-608
if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT_ENGINE) {
    rescreenWithEngine(event);  // 引擎策略分支
} else {
    rescreeningHandler(eventContext);  // 普通策略分支
}
```

## 4. 差异化流程详细分析

### 4.1 普通触达流程(execSend路径)

#### 4.1.1 普通策略复筛
```java
// 位置: StrategyEventDispatchServiceImpl.java:608
this.rescreeningHandler(eventContext);
```
- **数据表**: `strategy_market_event_condition`
- **筛选条件**: 基于实时标签的条件表达式
- **执行引擎**: 使用规则引擎进行条件匹配

#### 4.1.2 触达分发处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:968
mqProducerService.channelDelivery(bizEventVO);
```
- **MQ投递**: 将事件投递到对应渠道的MQ队列
- **异步处理**: 通过MQ实现异步处理
- **渠道路由**: 根据渠道类型投递到不同队列

#### 4.1.3 dispatch方法
```java
// 位置: StrategyEventDispatchServiceImpl.java:1040
public void dispatch(BizEventVO event)
```
- **时间校验**: 检查触达时间窗口
- **用户转换**: 支持fxk老客转xyf01的用户转换
- **流控检查**: 多维度流控保护

#### 4.1.4 execSend执行
```java
// 位置: StrategyEventDispatchServiceImpl.java:1148
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(...)
```
- **用户信息转换**: 跨app用户转换
- **渠道分发**: Switch语句处理不同渠道
- **直接调用**: EventDispatchService的各种渠道方法

### 4.2 引擎触达流程(marketingSend路径)

#### 4.2.1 引擎决策处理
```java
// 位置: StrategyEventDispatchServiceImpl.java:602
rescreenWithEngine(event);
```
- **引擎调用**: 调用决策引擎获取营销动作
- **决策解析**: 解析引擎返回的营销方案
- **直接执行**: 跳过dispatch阶段，直接调用marketingSend

#### 4.2.2 引擎预测调用
```java
// 位置: StrategyEngineService.predict()
PredictDecisionDto predictDecisionDto = strategyEngineService.predict(modelPredictionReq);
```
- **外部接口**: 决策引擎预测接口
- **返回结果**: 营销动作、渠道配置、模板参数等

## 5. 渠道分发层对比

### 5.1 两种不同的执行入口

#### 5.1.1 普通触达：execSend方法
```java
// 位置: StrategyEventDispatchServiceImpl.java:1148
private ImmutableTriple<Integer, EventPushBatchDo, Boolean> execSend(...)
```
- **调用方式**: 私有方法，内部调用
- **返回值**: `ImmutableTriple<Integer, EventPushBatchDo, Boolean>`
- **业务场景**: T0-普通触达链路

#### 5.1.2 引擎触达：marketingSend方法
```java
// 位置: StrategyEventDispatchServiceImpl.java:1229
public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                        StrategyMarketChannelEnum channelEnum, String groupName,
                        Map detailInfo, @Nullable BizEventVO bizEventVO)
```
- **调用方式**: 公有方法，可外部调用
- **返回值**: `int` (-999:流控错误, -1:无需发送, 其他:发送数量)
- **业务场景**: T0-引擎触达链路

### 5.2 统一的EventDispatchService调用

**关键发现**: 虽然execSend和marketingSend是两个不同的入口方法，但它们最终都调用相同的`EventDispatchService`渠道服务：

#### 5.2.1 execSend的渠道调用
```java
// 位置: StrategyEventDispatchServiceImpl.java:1151-1184
switch (channelEnum) {
    case SMS:
        dispatchResult = eventDispatchService.sendSmsEvent(...);
        break;
    case VOICE:
        dispatchResult = eventDispatchService.sendTeleEvent(...);
        break;
    // ... 其他渠道
}
```

#### 5.2.2 marketingSend的渠道调用
```java
// 位置: StrategyEventDispatchServiceImpl.java:1248-1647
switch (channelEnum) {
    case SMS:
        // 参数验证和流控检查
        SmsSingleSendResp resp = eventDispatchService.sendSms(...);
        break;
    // ... 其他渠道
}
```

### 5.3 渠道处理逻辑差异

#### 5.2.1 短信渠道 (SMS)
- **参数验证**: 手机号、模板ID验证
- **模板处理**: 模板参数构建和内容验证
- **流控检查**: 分布式流控保护
- **外部调用**: SMS短信服务

#### 5.2.2 电销渠道 (VOICE/VOICE_NEW)
- **参数验证**: 用户类型、策略ID验证
- **参数构建**: 电销参数构建
- **服务调用**: 电销系统MQ

#### 5.2.3 优惠券渠道 (COUPON/X_DAY_INTEREST_FREE)
- **批次解析**: 优惠券批次信息解析
- **批量发送**: 支持多券批量发放
- **流控保护**: 每个券独立流控检查

#### 5.2.4 Push推送渠道 (PUSH)
- **模板验证**: Push模板内容验证
- **参数处理**: Push参数构建
- **服务调用**: Push推送服务

#### 5.2.5 AI智能外呼渠道 (AI_PRONTO)
- **参数验证**: 业务来源码、名单类型ID验证
- **标签过滤**: 特殊标签过滤处理
- **服务调用**: AI触达服务

## 6. 数据表流转关系

### 6.1 通用数据表操作

```mermaid
graph TD
    A["strategy表<br/>SELECT查询策略配置"] --> B["strategy_market_event表<br/>SELECT查询事件配置"]
    B --> C["strategy_market_sub_event表<br/>SELECT查询子事件"]
    C --> D["strategy_market_event_condition表<br/>SELECT查询标签条件"]
    D --> E["strategy_group表<br/>SELECT查询分组配置"]
    E --> F["strategy_market_channel表<br/>SELECT查询渠道配置"]
    
    F --> G{触达类型判断}
    G -->|普通触达| H["user_dispatch_detail表<br/>INSERT记录触达明细"]
    G -->|引擎触达| I["引擎决策调用<br/>外部接口"]
    
    H --> J["event_push_batch表<br/>INSERT记录批次信息"]
    I --> J
    
    K["flow_ctrl表<br/>SELECT查询流控规则"] --> L["Redis流控计数<br/>INCR/PFADD操作"]
    L --> M["流控拦截日志<br/>INSERT记录拦截信息"]
    
    style G fill:#ffeb3b
    style H fill:#e3f2fd
    style I fill:#f3e5f5
```

### 6.2 详细数据库操作说明

| 表名 | 操作类型 | 普通触达 | 引擎触达 | 业务场景 |
|------|----------|----------|----------|----------|
| **strategy** | SELECT | ✓ | ✓ | 查询策略配置 |
| **strategy_market_event** | SELECT | ✓ | ✓ | 查询事件配置 |
| **strategy_market_sub_event** | SELECT | ✓ | ✓ | 查询子事件条件 |
| **strategy_market_event_condition** | SELECT | ✓ | ✓ | 查询标签条件 |
| **strategy_group** | SELECT | ✓ | ✓ | 查询分组配置 |
| **strategy_market_channel** | SELECT | ✓ | ✓ | 查询渠道配置 |
| **user_dispatch_detail** | INSERT | ✓ | ✓ | 记录触达明细 |
| **user_dispatch_detail** | SELECT | ✓ | ✓ | 流控检查查询历史 |
| **event_push_batch** | INSERT | ✓ | ✓ | 记录批次信息 |
| **flow_ctrl** | SELECT | ✓ | ✓ | 查询流控规则 |
| **flow_ctrl_interception_log** | INSERT | ✓ | ✓ | 记录流控拦截 |

## 7. 外部接口调用对比

### 7.1 共同的外部接口

| 接口类型 | 接口地址 | 普通触达 | 引擎触达 | 调用场景 |
|----------|----------|----------|----------|----------|
| **数仓标签查询** | `http://api-dps.xinfei.io` | ✓ | ✓ | 实时标签查询 |
| **用户中心** | `http://api.xinfei.io/user` | ✓ | ✓ | 用户信息查询 |
| **短信服务** | `http://sms.xinfei.io` | ✓ | ✓ | 短信发送 |
| **电销服务** | `http://telemkt.xinfei.io` | ✓ | ✓ | 电销触达 |
| **优惠券服务** | `http://userassetcore.xinfei.io` | ✓ | ✓ | 优惠券发放 |
| **Push服务** | `http://sms.xinfei.io` | ✓ | ✓ | Push推送 |
| **AI服务** | `http://call.xinfei.io` | ✓ | ✓ | AI外呼 |

### 7.2 引擎触达特有接口

| 接口类型 | 接口地址 | 功能说明 |
|----------|----------|----------|
| **决策引擎** | `http://enginepredictcenter.xinfei.io` | AI引擎决策预测 |
| **引擎管理** | `http://business-engine-manage.xinfei.io` | 模型列表查询 |

## 8. 流控机制统一分析

### 8.1 三层流控架构(通用)

```mermaid
graph TD
    A[用户事件] --> B[事件级流控]
    B --> C{是否通过}
    C -->|否| D[拦截并记录]
    C -->|是| E[预筛阶段]
    E --> F[复筛阶段]
    F --> G[触达级流控]
    G --> H{是否通过}
    H -->|否| I[拦截并记录]
    H -->|是| J[分布式流控]
    J --> K{是否通过}
    K -->|否| L[拦截并记录]
    K -->|是| M[执行营销触达]
```

### 8.2 流控配置管理(通用)

```properties
# 触达流控开关 (按渠道)
singleDispatchFlc.1 = false  # 短信渠道
singleDispatchFlc.2 = false  # 电销渠道
singleDispatchFlc.3 = false  # 优惠券渠道
singleDispatchFlc.5 = false  # Push渠道

# 事件级流控配置
eventFlcConfig = {
    "Notify_LifeRights": 300,
    "Notify_XDayInterestFree": 600
}
```

## 9. 性能优化策略(通用)

### 9.1 缓存策略
- **策略配置缓存**: Caffeine缓存减少数据库查询
- **人群包缓存**: 缓存人群包配置提高查询性能
- **标签数据缓存**: Redis缓存用户实时标签数据

### 9.2 线程池配置
```properties
# 人群处理线程池
crowd.pool.corePoolSize = 8
crowd.pool.maximumPoolSize = 20

# 流控处理线程池
dispatchFlcExecutor.pool.coreSize = 8
dispatchFlcExecutor.pool.maxSize = 20
```

### 9.3 批处理优化
- **标签批量查询**: 一次查询多个用户的标签数据
- **人群包批量验证**: 批量验证用户是否在人群包中
- **数据库批量操作**: 减少数据库连接开销

## 10. 总结

### 10.1 核心发现

**您的判断完全正确**：T0触达流程上最核心的差异确实是**是否调用引擎**，其他流程相似程度非常高。

### 10.2 架构优势

1. **统一的基础架构**: 两种模式共享预筛、复筛、渠道分发等核心组件
2. **灵活的决策机制**: 支持规则驱动和AI驱动两种决策方式
3. **统一的渠道适配**: 无论哪种模式，最终都通过相同的渠道分发逻辑
4. **完善的流控保护**: 统一的三层流控机制保障系统稳定性

### 10.3 技术特点

1. **高度复用**: 90%以上的代码逻辑在两种模式间复用
2. **清晰分层**: 决策层差异化，执行层统一化
3. **易于扩展**: 新增触达模式只需实现决策逻辑，复用执行逻辑
4. **运维友好**: 统一的监控、日志、配置管理

### 10.4 业务价值

1. **降低开发成本**: 高度复用减少重复开发
2. **提升维护效率**: 统一的执行逻辑便于维护
3. **支持业务创新**: 灵活的决策机制支持多样化营销策略
4. **保障系统稳定**: 统一的流控和异常处理机制

T0实时触达系统通过精心设计的架构，实现了**决策层的差异化**和**执行层的统一化**，既满足了不同业务场景的需求，又保持了系统的简洁性和可维护性。
