<h2 id="GKgRg">1. 概述</h2>
本文档详细分析T0引擎触达的完整链路，从消息队列消费开始，到最终的营销触达执行，重点分析`StrategyEventDispatchServiceImpl.marketingSend`方法的调用链路和业务逻辑。

<h2 id="Bq6K8">2. 整体架构图</h2>
```mermaid
graph TD
    A[MQ消息] --> B[MQ消费者]
    B --> C[EventMessageProcessor]
    C --> D[StrategyEventDispatchService.prescreen]
    D --> E[策略预筛]
    E --> F[引擎灰度判定]
    F --> G[事件预筛]
    G --> H[人群预筛]
    H --> I[延迟处理]
    I --> J[StrategyEventDispatchService.rescreen]
    J --> K[实时标签查询]
    K --> L[策略复筛]
    L --> M[引擎决策]
    M --> N[marketingSend方法]
    N --> O[渠道分发]
    O --> P[外部服务调用]
```

<h2 id="KwssZ">3. 入口分析</h2>
<h3 id="KSPUr">3.1 MQ消息消费入口</h3>
T0引擎触达的入口主要通过多个RocketMQ消费者：

<h4 id="IKGgb">3.1.1 主要消费者类</h4>
+ **LifeRightsRocketMqConsumer**: 生活权益事件消费
    - Topic: `tp_xyf_cdp_notify`
    - Tag: `tg_liferights`
    - 消费组: `cg_xyf_cdp_notify_tg_liferights`
+ **XDayInterestFreeRocketMqConsumer**: X天免息事件消费
    - Topic: `tp_xyf_cdp_notify`
    - Tag: `tg_xdayinterestfree`
+ **ExtractQuotaCardGuideEventRocketMq**: 提额卡引导事件消费
    - Topic: `tp_rcspt_risk_pre_amount_change_message`

<h4 id="jbXDF">3.1.2 消息处理流程</h4>
```java
// 代码位置: LifeRightsRocketMqConsumer.doMessage()
BizEventRocketMessageVO messageVO = JSONObject.parseObject(s1, BizEventRocketMessageVO.class);
BizEventMessageVO bizEventMessageVO = transform(messageVO);
bizEventMessageVO.setBizEventType("Notify_LifeRights");
strategyEventDispatchService.prescreen(messageExt.getMsgId(), bizEventMessageVO);
```

<h3 id="w0ff5">3.2 事件消息处理器</h3>
<h4 id="IGibd">3.2.1 EventMessageProcessor</h4>
+ **位置**: `cdp-domain/src/main/java/com/xftech/cdp/domain/event/EventMessageProcessor.java`
+ **作用**: 统一处理MQ消息，进行字段映射和数据转换
+ **配置表**: `mq_event_field_mapping_config` - 存储消息字段映射配置

```java
// 核心处理方法
public BizEventMessageVO doMessageProcessor(String topic, String consumer, String tag, String messageBody) {
    MqEventFieldMappingConfig mappingConfig = mqEventFieldConfigService.getMqEventFieldMappingConfig(topic, consumer, tag);
    BizEventMessageVO bizEventMessage = messageProcess(mappingConfig, messageBody);
    return bizEventMessage;
}
```

<h2 id="CV96b">4. 预筛阶段 (prescreen)</h2>
<h3 id="dUv5e">4.1 入口方法</h3>
**位置**: `StrategyEventDispatchServiceImpl.prescreen()`

<h3 id="GVDsh">4.2 核心处理流程</h3>
<h4 id="oC43V">4.2.1 策略配置查询</h4>
```java
// 查询事件对应的策略配置
List<StrategyMarketEventDo> marketEventList = cacheStrategyMarketEventService.getByEventName(bizEventMessageVO.getBizEventType());
```

**数据表**: `strategy_market_event`

+ 存储事件名称与策略ID的映射关系
+ 包含延迟配置、触达用户数限制等

<h4 id="GT3Y2">4.2.2 策略预筛处理</h4>
+ **策略状态检查**: 验证策略是否处于执行状态
+ **有效期检查**: 验证策略是否在有效期内
+ **流控检查**: 检查策略级别的流控规则

<h4 id="qioUw">4.2.3 引擎灰度判定</h4>
```java
if (eventContext.getStrategyDo().isEngineStrategy()) {
    String engineCode = eventContext.getBizEventVO().getEngineCode();
    if (!isInEngineGrayGroup(eventContext, group)) {
        eventContext.getBizEventVO().setIfIntoEngine(false);
    }
}
```

<h5 id="xNIRB">4.2.3.1 灰度判定规则</h5>
**判定逻辑**:

```plain
1. 检查策略是否为引擎策略 (isEngineStrategy())
2. 获取用户匹配的策略分组 (getAbGroup())
3. 解析分组配置中的selected字段:
   - selected = 1: 用户进入引擎处理 (setIfIntoEngine保持true)
   - selected = 0: 用户不进入引擎 (setIfIntoEngine(false))
4. 如果没有匹配到分组，默认进入引擎
```

**分组配置结构** (`strategy_group`表):

```sql
-- 策略分组表
id: 分组ID
strategy_id: 策略ID
name: 分组名称
group_config: 分组配置(JSON格式)
is_executable: 是否可执行
```

**GroupConfig JSON结构**:

```json
{
    "selected": 1,           // 是否选中: 1-进入引擎, 0-不进入引擎
    "groupType": "random",   // 分组类型: random-随机, whitelist-白名单
    "percent": 50,           // 分组比例(随机分组时使用)
    "whitelist": "123,456"   // 白名单用户ID列表(白名单分组时使用)
}
```

<h5 id="dZQz4">4.2.3.2 灰度控制的作用</h5>
1. **风险控制**: 新引擎版本的逐步发布，降低上线风险
2. **AB测试**: 对比引擎策略和普通策略的营销效果
3. **性能保护**: 在引擎异常时快速切换到普通策略处理
4. **精确控制**: 通过用户分组实现精确的灰度流量控制

<h4 id="r0awd">4.2.4 事件预筛</h4>
+ **子事件匹配**: 检查事件子类型是否匹配
+ **数据表**: `strategy_market_sub_event`

<h4 id="WdQNM">4.2.5 人群预筛</h4>
+ **人群类型**: T0_REGISTER（T0用户注册）、CROWD_PACK（离线人群包）等
+ **用户信息补全**: 注册时间、用户ID等信息补全

<h4 id="ewDW2">4.2.6 引擎次数限制</h4>
```java
strategyEngineService.checkEngineRateLimitThrowException(event.getAppUserId(), event.getStrategyId());
```

```sql
SELECT `s`.`name` ,s.`engine_code` ,slimit.* FROM `strategy_engine_rate_limit`  slimit join strategy s ON   slimit.strategy_id = `s`.`id`  WHERE s.`id` IN (2443,2444,2070,2458,2465,2565)  limit 20;

```

![](https://cdn.nlark.com/yuque/0/2025/png/29641697/1750143048332-d160e52a-e710-453d-98f1-7fe00b393757.png)

<h5 id="K0SBE">4.2.6.1 逻辑现状</h5>
+ 目前所有策略都会进行次数限制检查（apollo配置ReDecisionWhiteList）
+ 但是线上目前只有 APP_settle_t0 (APP结清T0)、API撞库营销-引擎test（API_reLoan_check_T0）在用
+ [详细逻辑](https://www.yuque.com/ruintime/kv2wp9/shf50du89hs912hs)  （通过查询“推入特征引擎”的相关特征， 获得特征返回结果的featureCount和featureTime与strategy_engine_rate_limit配置的quota、coolDownPeriod）

<h3 id="bDOL1">4.3 延迟处理</h3>
根据策略配置的延迟时间，将事件放入延迟队列或立即进入复筛阶段。

```mermaid
graph TD
    A[XDayInterestFreeRocketMqConsumer.doMessage] --> B[strategyEventDispatchService.prescreen]
    B --> C[预筛各阶段处理]
    C --> D[delayHandler延迟处理]
    D --> E[mqProducerService.bizEventDelay]
    E --> F[bizEventMqService.sendXXXDelayMessage]
    F --> G[RabbitMQ延迟队列]
    G --> H[延迟时间到达]
    H --> I[延迟消息消费者]
    I --> J[mqConsumeService.bizEventDelayProcess]
    J --> K[strategyEventDispatchService.rescreen]
    K --> L[复筛阶段处理]
```

从prescreen到rescreen的完整跳转流程：

1. XDayInterestFreeRocketMqConsumer.doMessage()
    - 接收MQ消息，解析并转换
    - 调用 `strategyEventDispatchService.prescreen()`
2. StrategyEventDispatchServiceImpl.prescreen()
    - 执行预筛逻辑（策略预筛、事件预筛、人群预筛等）
    - 最后调用 `delayHandler()` 方法
3. delayHandler()
    - 根据策略配置计算延迟时间
    - 调用 `mqProducerService.bizEventDelay()` 发送延迟消息
4. MqProducerServiceImpl.bizEventDelay()
    - [根据延迟时间选择不同优先级的队列](https://www.yuque.com/ruintime/kv2wp9/ypxxwhw104hhuzzd)
    - 调用 `bizEventMqService.sendXXXDelayMessage()` 发送到RabbitMQ
5. RabbitMQ延迟队列
    - 消息在队列中等待指定的延迟时间
6. 延迟消息消费者
    - 高/中/低优先级延迟消费者消费消息
    - 调用 `mqConsumeService.bizEventDelayProcess()`
7. MqConsumeServiceImpl.bizEventDelayProcess()
    - 调用 `strategyEventDispatchService.rescreen()`
8. StrategyEventDispatchServiceImpl.rescreen()
    - 开始复筛阶段处理

关键点：

+ 异步处理：通过RabbitMQ延迟队列实现异步处理
+ 延迟控制：支持立即处理（delayTime=0）和延迟处理
+ 优先级队列：根据延迟时间分配到不同优先级的队列
+ 消息幂等：通过Redis确保消息不重复消费

<h2 id="RlxTO">5. 复筛阶段 (rescreen)</h2>
```mermaid
graph TD
    A[延迟队列消息] --> B[rescreen方法入口]
    B --> C{策略类型判断}
    C -->|引擎策略| D[rescreenWithEngine]
    C -->|普通策略| E[queryLabelHandler]
    D --> F[构建引擎请求参数]
    F --> G[调用决策引擎]
    G --> H[解析引擎决策结果]
    H --> I{引擎决策成功?}
    I -->|成功| J[引擎内部queryLabelHandler]
    I -->|失败| K[引擎决策失败处理]
    J --> L[引擎内部rescreeningHandler]
    L --> M{复筛结果}
    M -->|成功| N[引擎直接调用marketingSend]
    M -->|失败| O[复筛失败但继续营销]
    E --> P[普通策略queryLabelHandler]
    P --> Q[普通策略rescreeningHandler]
    Q --> R{复筛结果}
    R -->|成功| S[dispatchHandler触达分发]
    R -->|失败| T[记录失败原因]
    S --> U[投递到触达队列]
    U --> V[dispatch方法处理]
    V --> W[最终调用marketingSend]
```

<h3 id="fuoNW">5.1 入口方法</h3>
**位置**: `StrategyEventDispatchServiceImpl.rescreen()`

<h3 id="dbaJD">5.2 处理流程</h3>
<h4 id="cejWS">5.2.1 实时标签查询</h4>
```java
this.queryLabelHandler(eventContext);
```

+ **外部服务**: ADS数仓服务
+ **查询内容**: 用户实时标签数据
+ **数据表**: `strategy_market_event_condition` - 存储标签查询条件

<h4 id="p5kcF">5.2.2 策略复筛</h4>
+ **条件匹配**: 根据配置的标签条件进行用户筛选
+ **表达式计算**: 支持复杂的逻辑表达式

<h4 id="HXagZ">5.2.3 引擎决策调用</h4>
对于引擎策略，调用决策引擎获取营销动作：

```java
if (StrategyTypeEnum.getInstance(event.getStrategyType()) == StrategyTypeEnum.EVENT_ENGINE) {
    rescreenWithEngine(event);
}
```

<h2 id="r9SU5">6. marketingSend方法详细分析</h2>
<h3 id="CIgsq">6.1 方法签名</h3>
```java
public int marketingSend(DispatchDto dispatchDto, CrowdDetailDo crowdDetailDo,
                        StrategyMarketChannelEnum channelEnum, String groupName,
                        Map detailInfo, @Nullable BizEventVO bizEventVO)
```

<h3 id="lLOQP">6.2 返回值说明</h3>
+ **-999**: 发生流控错误
+ **-1**: 无需发送
+ **其他正值**: 表示发送的数量

<h3 id="SAksy">6.3 渠道处理逻辑</h3>
`marketingSend`方法通过switch语句处理不同的渠道类型，每种渠道都有特定的参数验证、流控检查和外部服务调用逻辑。

<h4 id="DoZ9T">6.3.1 余额充值渠道 (REMAINING_SUM_CHARGING)</h4>
```java
case REMAINING_SUM_CHARGING:
    BigDecimal amount = BigDecimalUtils.toBigDecimal(detailInfo.get("balanceIncreaseAmount"), 0);
    BalanceOptReq balanceOptReq = BalanceOptReq.builder()
            .userNo(crowdDetailDo.getUserId())
            .amount(amount)
            .description((String) detailInfo.get("balanceDesc"))
            .build();
    return eventDispatchService.increaseBalance(balanceOptReq, orderNumber) ? 1 : 0;
```

**功能**: 用户余额充值
**外部服务**: AppCore余额服务
**必需参数**: `balanceIncreaseAmount`(充值金额), `balanceDesc`(充值描述)
**返回值**: 成功返回1，失败返回0

<h4 id="tX45E">6.3.2 短信渠道 (SMS)</h4>
```java
case SMS:
    // 1. 参数验证
    if (StringUtils.isEmpty(crowdDetailDo.getMobile())) {
        log.error("引擎策略发送营销短信无手机号");
        return 0;
    }
    if (null == detailInfo.get("template_id")) {
        log.error("引擎策略发送营销短信无短信模板");
        return 0;
    }

    // 2. 模板参数处理
    String templateId = detailInfo.get("template_id").toString();
    Map dataMap = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));

    // 3. 特殊标签过滤和模板内容验证
    boolean removeFlag = adsStrategyLabelService.filterLabelMinValue(dataMap, crowdDetailDo.getUserId(), strategyId);
    boolean checkRet = templateParamService.checkTemplateContent(crowdDetailDo.getApp(), templateId, dataMap, removeFlag, strategyId);

    // 4. 分布式流控检查
    boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
    if (flcLockRet) {
        return -999; // 流控拦截
    }

    // 5. 构建短信发送请求
    SmsSingleSendArgs smsSingleSendArgs = new SmsSingleSendArgs();
    smsSingleSendArgs.setMobile(crowdDetailDo.getMobile());
    smsSingleSendArgs.setTemplateId(templateId);
    smsSingleSendArgs.setData(dataMap);
    if (detailInfo.containsKey("signatureKey")) {
        smsSingleSendArgs.setSignatureKey((String) detailInfo.get("signatureKey"));
    }

    // 6. 调用短信服务
    SmsSingleSendResp resp = eventDispatchService.sendSms(smsSingleSendRequester);
```

**功能**: 发送营销短信
**外部服务**: SMS短信服务
**必需参数**: `template_id`(模板ID), `template_params`(模板参数)
**可选参数**: `signatureKey`(签名密钥)
**特殊处理**: 标签过滤、模板内容验证、分布式流控
**返回值**: 成功返回1，失败返回0，流控返回-999

<h4 id="AI_PRONTO">6.3.3 AI即时触达渠道 (AI_PRONTO)</h4>
```java
case AI_PRONTO:
    // 1. 参数验证
    if (StringUtils.isBlank((String) detailInfo.get("biz_source_code"))
            && StringUtils.isBlank((String) detailInfo.get("name_type_id"))) {
        log.error("引擎策略发送ai无业务来源&名单类型");
        return 0;
    }

    // 2. 参数提取和处理
    String bizSourceCode = Optional.ofNullable(detailInfo.get("biz_source_code")).orElse(StringUtils.EMPTY).toString();
    String nameTypeId = Optional.ofNullable(detailInfo.get("name_type_id")).orElse(StringUtils.EMPTY).toString();
    Map aiParams = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));

    // 3. 特殊标签过滤
    adsStrategyLabelService.filterLabelMinValue(aiParams, crowdDetailDo.getUserId(), strategyId);

    // 4. 分布式流控检查
    boolean aiFlcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
    if (aiFlcLockRet) {
        return -999;
    }

    // 5. 构建AI发送请求
    AiSendArgs aiSendArgs = new AiSendArgs();
    if (StringUtils.isNotBlank(bizSourceCode)) {
        aiSendArgs.setBizSourceCode(bizSourceCode);
    }
    if (StringUtils.isNotBlank(nameTypeId)) {
        aiSendArgs.setAiChannelType(nameTypeId);
    }
    aiSendArgs.setUserDataList(Collections.singletonList(new AiUserData(crowdDetailDo.getUserId(), crowdDetailDo.getApp(), aiParams)));

    // 6. 调用AI服务
    AiSendResp aiSendResp = eventDispatchService.sendAiPronto(aiSendArgs);
```

**功能**: AI即时触达
**外部服务**: AI触达服务
**必需参数**: `biz_source_code`(业务来源码) 或 `name_type_id`(名单类型ID)
**可选参数**: `template_params`(模板参数)
**特殊处理**: 标签过滤、分布式流控
**返回值**: 成功返回1，失败返回0，流控返回-999

<h4 id="GpSP6">6.3.4 Push推送渠道 (PUSH)</h4>
```java
case PUSH:
    // 1. 参数验证
    if (StringUtils.isEmpty((String) detailInfo.get("template_id"))) {
        log.error("引擎策略发送营销push无push模板");
        return 0;
    }

    // 2. 模板参数处理
    String pushTemplateId = detailInfo.get("template_id").toString();
    Map<String, Object> tempParam = JsonUtil.toMap(JsonUtil.toJson(detailInfo.get("template_params")));

    // 3. 模板内容验证
    if (!MapUtil.isEmpty(tempParam)) {
        boolean removePushFlag = adsStrategyLabelService.filterLabelMinValue(tempParam, crowdDetailDo.getUserId(), strategyId);
        boolean checkPushRet = templateParamService.checkPushTemplateContent(crowdDetailDo.getApp(), pushTemplateId, tempParam, removePushFlag, strategyId);
        if (!checkPushRet) {
            log.error("引擎策略发PUSH内容参数验证不通过");
            return 0;
        }
    }

    // 4. 分布式流控检查
    boolean pushFlcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
    if (pushFlcLockRet) {
        return -999;
    }

    // 5. 构建Push请求
    PushBaseRequest<SendPushRequest> request = new PushBaseRequest<>();
    SendPushRequest sendPushRequest = new SendPushRequest();
    sendPushRequest.setTemplateId(pushTemplateId);
    if (tempParam != null) {
        sendPushRequest.setPushDataList(Collections.singletonList(new PushUserData(crowdDetailDo.getUserId().toString(), tempParam)));
    }

    // 6. 调用Push服务
    PushResponse<SendPushInfo> pushResponse = eventDispatchService.sendPush(request);
```

**功能**: Push消息推送
**外部服务**: Push推送服务
**必需参数**: `template_id`(模板ID)
**可选参数**: `template_params`(模板参数)
**特殊处理**: 标签过滤、模板内容验证、分布式流控
**返回值**: 成功返回1，失败返回0，流控返回-999

<h4 id="INCREASE_AMOUNT">6.3.5 提额渠道 (INCREASE_AMOUNT)</h4>
```java
case INCREASE_AMOUNT:
    // 1. 参数验证
    if (!detailInfo.containsKey("increase_type")
            || !detailInfo.containsKey("increase_amt")
            || !detailInfo.containsKey("amt_start_time")
            || !detailInfo.containsKey("amt_end_time")) {
        log.info("提额参数错误");
        return 0;
    }

    // 2. 参数提取和处理
    BigDecimal increaseAmt = new BigDecimal(detailInfo.get("increase_amt").toString());
    String startTime = detailInfo.get("amt_start_time").toString();
    String endTime = detailInfo.get("amt_end_time").toString();

    // 3. 构建提额参数
    IncreaseAmtParamDto increaseAmtParamDto = new IncreaseAmtParamDto();
    increaseAmtParamDto.setAmount(increaseAmt.multiply(new BigDecimal("100")).intValue()); // 转换为分
    increaseAmtParamDto.setStartTime(startTime);
    increaseAmtParamDto.setEndTime(endTime);

    // 4. 参数有效性验证
    if (!increaseAmtParamDto.isValid()) {
        log.info("提额参数错误");
        return 0;
    }

    // 5. 调用提额服务
    ImmutableTriple<Integer, EventPushBatchDo, Boolean> dispatchResult = eventDispatchService
            .sendIncreaseAmtEvent(dispatchDto, crowdDetailDo.getApp(), crowdDetailDo.getInnerApp(), crowdDetailDo, null, null, null);

    // 6. 处理流控结果
    if (Objects.equals(0, dispatchResult.getLeft()) && Objects.equals(true, dispatchResult.getRight())) {
        return -999; // 流控拦截
    }
    return dispatchResult.getLeft();
```

**功能**: 用户额度提升
**外部服务**: 额度管理服务
**必需参数**: `increase_type`(提额类型), `increase_amt`(提额金额), `amt_start_time`(开始时间), `amt_end_time`(结束时间)
**特殊处理**: 金额单位转换(元转分)、参数有效性验证
**返回值**: 成功返回发送数量，失败返回0，流控返回-999

<h4 id="VOICE_CHANNELS">6.3.6 电销渠道 (VOICE/VOICE_NEW)</h4>
```java
case VOICE:
case VOICE_NEW:
    // 1. 参数验证
    String paramKey = (channelEnum == StrategyMarketChannelEnum.VOICE) ? "user_type" : "policy_id";
    if (null == detailInfo.get(paramKey)) {
        log.error("发送电销缺少必须参数");
        return 0;
    }

    // 2. 新电销特殊参数处理
    Object typeId = null;
    Integer decisionPower = null;
    if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW) {
        if (!detailInfo.containsKey("name_type_id") || null == detailInfo.get("name_type_id")) {
            log.error("发送电销缺少必须参数name_type_id");
            return 0;
        }
        typeId = detailInfo.get("name_type_id");

        // 决策权重参数处理
        Object dpValue = detailInfo.get("decision_power");
        if (dpValue != null && !dpValue.toString().isEmpty()) {
            decisionPower = Integer.parseInt(dpValue.toString());
        }
    }

    // 3. 分布式流控检查
    boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
    if (flcLockRet) {
        return -999;
    }

    // 4. 根据电销类型调用不同服务
    if (channelEnum == StrategyMarketChannelEnum.VOICE_NEW) {
        // 新电销：构建TelePushArgs并发送到MQ
        TelePushArgs policyDetail = telePushService.getTelePushArgs(userType, Collections.singletonList(crowdDetailDo), dispatchDto, crowdDetailDo.getApp());
        policyDetail.setBatchNumber(batchNum);

        // 设置决策权重
        if (decisionPower != null) {
            for (TelePushArgs.UserData userData : policyDetail.getData()) {
                TelePushArgs.ExtData extData = TelePushArgs.ExtData.builder()
                        .decision_power(decisionPower)
                        .build();
                userData.addExtData(extData);
            }
        }

        TelePushResp telePushResp = eventDispatchService.sendTeleNew(policyDetail);
    } else {
        // 老电销：构建TeleSaveBatchRequest
        TeleSaveBatchRequest teleSaveBatchRequest = new TeleSaveBatchRequest();
        TeleSaveBatchArgs teleSaveBatchArgs = new TeleSaveBatchArgs();
        teleSaveBatchArgs.setCreditIdArr(Collections.singletonList(crowdDetailDo.getUserId()));
        teleSaveBatchArgs.setUserType(userType);
        teleSaveBatchArgs.setFlowNo(batchNum);

        TeleSaveBatchResp teleSaveBatchResp = eventDispatchService.sendTele(teleSaveBatchRequest);
    }
```

**功能**: 电销触达
**外部服务**: 电销系统MQ
**VOICE必需参数**: `user_type`(用户类型)
**VOICE_NEW必需参数**: `policy_id`(策略ID), `name_type_id`(名单类型ID)
**VOICE_NEW可选参数**: `decision_power`(决策权重)
**特殊处理**: 新老电销不同的参数和调用方式、决策权重设置、分布式流控
**返回值**: 成功返回1，失败返回0，流控返回-999

<h4 id="COUPON_CHANNELS">6.3.7 优惠券渠道 (SALE_TICKET/X_DAY_INTEREST_FREE)</h4>
```java
case SALE_TICKET:
case X_DAY_INTEREST_FREE:   // X天免息也是一种优惠券
    // 1. 参数验证
    if (detailInfo.get("batch") == null) {
        log.error("无优惠券的信息");
        return 0;
    }

    // 2. 解析优惠券批次信息
    List<PredictDecisionDto.DetailCouponDto> couponDtos = JsonUtil.toList(JsonUtil.toJson(detailInfo.get("batch")),
            PredictDecisionDto.DetailCouponDto.class);
    if (CollectionUtils.isEmpty(couponDtos)) {
        return -1;
    }

    // 3. 构建用户信息
    CouponSendBatchReq.User user = new CouponSendBatchReq.User(crowdDetailDo.getUserId(), crowdDetailDo.getMobile(), crowdDetailDo.getMobile());

    // 4. 批量发送优惠券
    int ret = 0;
    for (PredictDecisionDto.DetailCouponDto couponDto : couponDtos) {
        // 4.1 重新初始化批次对象
        dispatchDetail = new UserDispatchDetailDo();
        eventPushBatchDo = new EventPushBatchDo();
        batchNum = serialNumberUtil.batchNum();

        // 4.2 分布式流控检查
        boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
        if (flcLockRet) {
            continue; // 流控拦截，继续下一个优惠券
        }

        // 4.3 构建优惠券发送请求
        CouponSendBatchReq couponSendBatchReq = new CouponSendBatchReq();
        couponSendBatchReq.setActivityId(couponDto.getActivity_id());
        couponSendBatchReq.setBatchNum(batchNum);
        couponSendBatchReq.setApp(crowdDetailDo.getApp());
        couponSendBatchReq.setInnerApp(crowdDetailDo.getInnerApp());
        couponSendBatchReq.setAllBatchCount(1);
        couponSendBatchReq.setUserList(Collections.singletonList(user));

        // 4.4 调用优惠券服务
        BaseCouponResponse<Object> couponRet = eventDispatchService.sendCoupon(couponSendBatchReq);
        if (couponRet.isSuccess()) {
            ret = ret + 1; // 累计成功数量
        }
    }
    return ret;
```

**功能**: 优惠券发放(包括X天免息券)
**外部服务**: 优惠券系统
**必需参数**: `batch`(优惠券批次信息数组)
**批次信息结构**: `activity_id`(活动ID)等
**特殊处理**: 支持批量发券、每个券独立流控检查、累计成功数量
**返回值**: 成功发送的优惠券数量，无券返回-1

<h4 id="SPECIAL_CHANNELS">6.3.8 特殊渠道处理</h4>

**6.3.8.1 不营销渠道 (NONE)**
```java
case NONE:
    // 创建空白分组记录
    UserBlankGroupDetailDo userBlankGroupDetailDo = new UserBlankGroupDetailDo();
    userBlankGroupDetailDo.setUserId(crowdDetailDo.getUserId());
    userBlankGroupDetailDo.setMobile(crowdDetailDo.getMobile());
    userBlankGroupDetailDo.setStrategyId(strategyId);
    userBlankGroupDetailDo.setMarketChannel(channelEnum.getCode());
    userBlankGroupDetailDo.setStatus(UserDispatchDetailStatusEnum.SUCCESS.getStatus());

    // 保存空白分组记录
    userBlankGroupDetailService.add(userBlankGroupDetailDo, dispatchDto.getStrategyRulerEnum());
    return -1; // 表示无需发送
```

**功能**: 不营销分组记录
**处理逻辑**: 记录用户进入不营销分组的情况，用于AB测试对比
**返回值**: -1(无需发送)

**6.3.8.2 延迟决策渠道 (RE_DECISION)**
```java
case RE_DECISION:
    // 1. 参数验证
    if (MapUtils.isEmpty(detailInfo) || Objects.isNull(detailInfo.get("reinput_delay_second"))) {
        log.warn("业务引擎-延迟决策,下发异常");
        return 0;
    }

    // 2. 延迟时间验证
    int reInputDelaySecond = Integer.parseInt(detailInfo.get("reinput_delay_second").toString());
    if (reInputDelaySecond <= 0 || reInputDelaySecond > 60 * 60 * 24) { // 最大24小时
        log.warn("业务引擎-延迟决策,延迟时间异常");
        return 0;
    }

    // 3. 触发延迟决策
    reDecisionService.reDecision(groupName, reInputDelaySecond, bizEventVO);
    return -1;
```

**功能**: 延迟重新决策
**外部服务**: 延迟决策服务
**必需参数**: `reinput_delay_second`(延迟秒数，最大86400秒)
**处理逻辑**: 将事件重新投入决策引擎，延迟指定时间后再次决策
**返回值**: -1(无需发送)

<h4 id="FLOW_CONTROL_LOGIC">6.3.9 通用流控处理逻辑</h4>
所有需要外部服务调用的渠道都包含以下通用处理：

**1. 分布式流控检查**
```java
boolean flcLockRet = dispatchFlcService.dispatchFlcLock(dispatchDto, eventPushBatchDo, dispatchDetail, batchNum, crowdDetailDo, this);
if (flcLockRet) {
    return -999; // 流控拦截
}
```

**2. 批次记录更新**
```java
// 更新发送结果到批次表
eventPushBatchDo.setSendCode(respCode);
eventPushBatchDo.setSendMsg(respMsg);
eventPushBatchDo.setStatus(sendStatus);
if (eventPushBatchDo.getId() != null && eventPushBatchDo.getId() > 0) {
    getEventPushBatchRepository().updateById(eventPushBatchDo.getDetailTableNo(), eventPushBatchDo);
}
```

**3. 失败状态处理**
```java
// 发送失败时更新用户触达明细状态
if (!Objects.equals(isRespSucced, true)) {
    if (dispatchDetail.getId() != null && dispatchDetail.getId() > 0) {
        dispatchDetail.setStatus(0); // 设置为失败状态
        getUserDispatchDetailRepository().updateById(dispatchDto.getDetailTableNo(), dispatchDetail);
    }
}
```

<h4 id="RETURN_VALUES">6.3.10 返回值说明</h4>
- **正整数**: 成功发送的数量(如1表示发送1条，多券场景可能返回多个)
- **0**: 发送失败(参数错误、外部服务调用失败等)
- **-1**: 无需发送(NONE渠道、RE_DECISION渠道等)
- **-999**: 流控拦截(分布式锁获取失败)

<h2 id="YK5Ij">7. 核心数据表结构</h2>
<h3 id="RVdEG">7.1 策略核心表</h3>
<h4 id="p0LhN">7.1.1 strategy表</h4>
```sql
-- 策略主表
id: 策略ID
name: 策略名称
type: 策略类型(0-事件, 1-事件引擎)
send_ruler: 执行规则(0-单次, 1-例行, 2-事件, 3-循环)
status: 策略状态(0-已发布, 1-执行中, 2-成功, 3-失败, 4-暂停, 5-结束)
engine_code: 引擎代码(引擎策略专用)
dispatch_config: 触达配置(时间窗口等)
flow_ctrl_id: 流控规则ID
```

<h4 id="IAX9w">7.1.2 strategy_market_event表</h4>
```sql
-- 策略事件配置表
id: 主键ID
strategy_id: 策略ID
event_name: 事件英文名
time_type: 时间类型(1-立即, 2-延迟)
time_value: 延迟数值
time_unit: 时间单位(1-分钟, 2-小时)
dispatch_min_user_num: 每日最小触达用户数
dispatch_max_user_num: 每日最大触达用户数
```

<h4 id="WPqXf">7.1.3 strategy_group表</h4>
```sql
-- 策略分组表
id: 分组ID
strategy_id: 策略ID
name: 分组名称
group_config: 分组配置(JSON格式，包含AB测试规则)
is_executable: 是否可执行
```

<h4 id="ZmGma">7.1.4 strategy_market_channel表</h4>
```sql
-- 策略渠道表
id: 渠道ID
strategy_id: 策略ID
strategy_group_id: 分组ID
market_channel: 渠道类型(1-短信, 2-电销, 3-优惠券, 5-Push等)
template_id: 模板ID
app: 应用标识
send_time: 发送时间
ext_info: 扩展信息
```

<h3 id="qvMU0">7.2 事件配置表</h3>
<h4 id="C6hgl">7.2.1 strategy_market_sub_event表</h4>
```sql
-- 策略子事件表
id: 主键ID
strategy_id: 策略ID
market_event_id: 策略营销节点ID
event_name: 事件英文名
operate_type: 条件操作符(eq、ge等)
event_value: 标签值
expression: 表达式
relationship: 关系(1-且, 2-或)
```

<h4 id="TeJad">7.2.2 strategy_market_event_condition表</h4>
```sql
-- 策略事件条件表
id: 主键ID
strategy_id: 策略ID
time_type: 时间类型
time_value: 时间值
label_name: 标签名称
operate_type: 操作类型
condition_value: 条件值
expression: 表达式
```

<h3 id="EG8wL">7.3 配置管理表</h3>
<h4 id="Z02iJ">7.3.1 mq_event_field_mapping_config表</h4>
```sql
-- MQ事件字段映射配置表
topic: MQ主题
consumer: 消费者名称
tag: 消息标签
field_list: 字段映射列表(JSON)
parse: 解析器类型
```

<h2 id="Km4ca">8. 外部服务依赖</h2>
<h3 id="HbgJi">8.1 短信服务 (SmsClient)</h3>
+ **接口**: `SmsSingleSendRequester`
+ **功能**: 单条短信发送
+ **配置**: 模板ID、签名等

<h3 id="SyeO3">8.2 电销服务 (TelemarketingClient)</h3>
+ **接口**: `TelePushArgs`
+ **功能**: 电销数据推送到MQ
+ **配置**: 策略ID、用户数据等

<h3 id="FKmjh">8.3 优惠券服务 (CouponClient)</h3>
+ **接口**: `CouponSendBatchReq`
+ **功能**: 批量发放优惠券
+ **配置**: 券模板、发放规则等

<h3 id="H8SmY">8.4 Push服务 (PushClient)</h3>
+ **接口**: `PushBaseRequest`
+ **功能**: Push消息推送
+ **配置**: 推送模板、目标用户等

<h3 id="l2dbu">8.5 ADS数仓服务 (AdsClient)</h3>
+ **接口**: 实时标签查询
+ **功能**: 获取用户实时标签数据
+ **配置**: 标签名称、查询条件等

<h3 id="AVXYB">8.6 决策引擎服务</h3>
+ **功能**: 获取营销决策结果
+ **返回**: 营销动作列表、渠道配置等

<h2 id="lRgoR">9. 配置中心依赖</h2>
<h3 id="qWD6q">9.1 Apollo配置</h3>
+ **引擎灰度规则**: 控制引擎策略的灰度发布
+ **流控开关**: 各种流控规则的开关配置
+ **服务地址**: 外部服务的地址配置

<h3 id="eVNam">9.2 Redis缓存</h3>
+ **策略缓存**: 策略配置信息缓存
+ **用户计数**: 用户发送次数统计
+ **流控计数**: 各维度流控计数

<h2 id="XZjsB">10. 监控和日志</h2>
<h3 id="MI71B">10.1 关键监控指标</h3>
+ **发送成功率**: 各渠道发送成功率统计
+ **流控拦截率**: 流控规则拦截比例
+ **引擎调用延迟**: 决策引擎调用耗时
+ **外部服务可用性**: 各外部服务的可用性监控

<h3 id="zyiZb">10.2 关键日志点</h3>
+ **事件接收**: MQ消息接收日志
+ **预筛结果**: 预筛各阶段的通过情况
+ **复筛结果**: 复筛标签查询和匹配结果
+ **发送结果**: 各渠道发送结果日志
+ **异常处理**: 各种异常情况的详细日志

<h2 id="wDOCZ">11. 性能优化点</h2>
<h3 id="Sd8xg">11.1 缓存优化</h3>
+ **策略配置缓存**: 减少数据库查询
+ **标签数据缓存**: 减少ADS服务调用
+ **模板内容缓存**: 减少模板查询

<h3 id="wKPrb">11.2 异步处理</h3>
+ **MQ异步发送**: 电销等渠道使用MQ异步发送
+ **批量处理**: 支持批量用户处理
+ **并行处理**: 多渠道并行发送

<h3 id="pkhbg">11.3 流控保护</h3>
+ **多维度流控**: 用户级、策略级、渠道级流控
+ **熔断机制**: 外部服务异常时的熔断保护
+ **限流算法**: 令牌桶、滑动窗口等限流算法

<h2 id="S66uc">12. 总结</h2>
T0引擎触达链路是一个复杂的实时营销系统，从MQ消息消费开始，经过预筛、复筛、引擎决策等多个阶段，最终通过`marketingSend`方法实现多渠道的营销触达。整个链路涉及多个数据表、外部服务和配置中心，需要完善的监控和异常处理机制来保证系统的稳定性和可靠性。

关键特点：

1. **实时性**: 支持实时事件触发和处理
2. **多渠道**: 支持短信、电销、优惠券、Push等多种触达渠道
3. **智能化**: 集成决策引擎，支持智能营销决策
4. **可配置**: 丰富的配置项，支持灵活的策略配置
5. **高可用**: 完善的流控、熔断和监控机制

<h2 id="eBNuw">11. 流控机制详细分析</h2>
<h3 id="n1Qbf">11.1 流控概述</h3>
流控（Flow Control）是T0引擎触达链路中的核心保护机制，用于防止用户被过度营销，保护用户体验和业务合规性。流控机制贯穿整个触达链路，包括事件级流控、触达级流控和分布式流控。

<h3 id="rVr9i">11.2 流控架构图</h3>
```mermaid
graph TD
    A[用户事件] --> B[事件级流控]
    B --> C{是否通过}
    C -->|否| D[拦截并记录]
    C -->|是| E[预筛阶段]
    E --> F[复筛阶段]
    F --> G[触达级流控]
    G --> H{是否通过}
    H -->|否| I[拦截并记录]
    H -->|是| J[分布式流控]
    J --> K{是否通过}
    K -->|否| L[拦截并记录]
    K -->|是| M[执行营销触达]
```

<h3 id="QKaHi">11.3 事件级流控</h3>
<h4 id="CIaxO">11.3.1 入口位置</h4>
**位置**: `MqConsumeServiceImpl.isReject()`

<h4 id="oxXZ5">11.3.2 流控逻辑</h4>
```java
// 代码位置: MqConsumeServiceImpl.isReject()
if (StringUtils.isNotEmpty(bizEventMessageVO.getBizEventType()) && userId != null && userId > 0) {
    EventFlcConfig eventFlcConfig = appConfigService.getEventFlcConfig();
    if (eventFlcConfig != null) {
        Integer limitSeconds = eventFlcConfig.getLimitSeconds(bizEventMessageVO.getBizEventType());
        if (limitSeconds != null) {
            if (limitSeconds <= 0) {
                return true; // 完全拦截该事件
            }
            String limitKey = String.format("eventflc:%s:%s", bizEventMessageVO.getBizEventType(), userId);
            boolean ret = redisUtils.lock(limitKey, "1", limitSeconds);
            if (!ret) {
                return true; // 在限制时间内，拦截重复事件
            }
        }
    }
}
```

<h4 id="tYcu2">11.3.3 配置来源</h4>
+ **Apollo配置**: `EventFlcConfig` - 事件级流控配置
+ **Redis锁**: 使用Redis分布式锁实现时间窗口控制
+ **配置格式**:

```json
{
  "事件名称": 限制秒数,
  "Notify_LifeRights": 300,
  "Notify_XDayInterestFree": 600
}
```

<h3 id="X2Toy">11.4 触达级流控</h3>
<h4 id="tbUN2">11.4.1 入口位置</h4>
**位置**: `StrategyEventDispatchServiceImpl.dispatch()` -> `DispatchFlcService.dispatchFlc()`

<h4 id="CPgQo">11.4.2 流控开关控制</h4>
```java
// 代码位置: DispatchFlcService.dispatchFlc()
StrategyMarketChannelEnum marketChannelEnum = StrategyMarketChannelEnum.getInstance(bizEventVO.getMarketChannel());
boolean switchFlag = appConfigService.getSingleDispatchFlcSwitch(marketChannelEnum);
if (!Objects.equals(true, switchFlag)) {
    log.info("触达流控开关未打开, 策略id:{}, 用户id:{}, marketChannel = {}",
             bizEventVO.getStrategyId(), bizEventVO.getAppUserId(), marketChannelEnum.getCode());
    return false; // 流控开关关闭，直接通过
}
```

**配置来源**: Apollo配置 - `singleDispatchFlc.{渠道代码}`

<h4 id="MH6sR">11.4.3 流控规则查询和执行</h4>
```java
// 查询策略分组、渠道配置
Triple<StrategyGroupDo, StrategyMarketChannelDo, StrategyExecLogDo> triple = getStrategyConfig(bizEventVO.getStrategyGroupId(), marketChannelEnum);
StrategyMarketChannelDo channelDo = triple.getMiddle();

// 执行流控检查
List<Integer> sucStatus = Arrays.asList(-1, 1);
List<CrowdDetailDo> crowdDetailList = abstractStrategyEventDispatchService.flowCtrl(
    bizEventVO.getMessageId(),
    Optional.ofNullable(bizEventVO.getTriggerDatetime()).orElse(LocalDateTime.now()),
    channelDo, crowdDetail, sucStatus, bizEventVO.getBizEventType()
);

if (CollectionUtils.isEmpty(crowdDetailList)) {
    log.warn("触达流控, 用户已被流控拦截, 终止下发, 用户ID:{}, 渠道Id:{}, 策略id:{}",
             bizEventVO.getAppUserId(), bizEventVO.getMarketChannel(), bizEventVO.getStrategyId());
    return true; // 被流控拦截
}
```

<h3 id="Bci93">11.5 分布式流控</h3>
<h4 id="WCXkY">11.5.1 入口位置</h4>
**位置**: `DispatchFlcService.distributedFlc()`

<h4 id="jBxg1">11.5.2 分布式锁机制</h4>
```java
// 代码位置: DispatchFlcService.distributedFlc()
boolean isStartFlc = appConfigService.getSingleDispatchFlcLockSwitch(channelEnum);
if (!isStartFlc) {
    log.info("分布式流控:开关未打开, 不流控, userId = {}, channelEnum = {}", userId, channelEnum.getDescription());
    return false;
}

// 尝试获取分布式锁
boolean lockRet = tryLock(userId, value);
if (!lockRet) {
    log.warn("分布式流控:获取锁失败, userId = {}, channelEnum = {}", userId, channelEnum.getDescription());
    return true; // 获取锁失败，被流控
}
```

<h4 id="ecp8X">11.5.3 分布式锁实现</h4>
```java
// 代码位置: DispatchFlcService.tryLock()
private boolean tryLock(Long userId, String value) {
    final int lockSeconds = 60 * 6; // 锁定6分钟
    final long sleepMills = appConfigService.getDispatchFlcLockPerWaitMills();
    String lockKey = getLockKey(userId);
    boolean isIgnore = appConfigService.getSingleIgnoreDispatchFlcLockSwitch();

    do {
        if (isIgnore) {
            log.info("分布式流控:忽略加锁,直接返回成功, userId = {}", userId);
            return true;
        }
        lockRet = redisUtils.lock(lockKey, value, lockSeconds);
        if (!lockRet) {
            ThreadUtil.sleep(sleepMills); // 等待重试
        }
    } while (!lockRet && 重试条件);

    return lockRet;
}
```

<h3 id="ejanG">11.6 流控规则配置</h3>
<h4 id="zpRE8">11.6.1 流控规则表结构 (flow_ctrl)</h4>
```sql
-- 流控规则表
id: 规则ID
type: 规则类型(1-策略, 2-渠道, 3-多策略, 4-业务线)
name: 规则名称
description: 规则描述
status: 规则状态(0-初始化, 1-生效中, 2-已关闭)
effective_strategy: 生效策略(0-全部，多个逗号分隔)
effective_channel: 生效渠道(0-全部，多个逗号分隔)
limit_days: 触达限制天数
limit_times: 触达限制次数
priority: 优先级(1-渠道特殊, 2-渠道兜底, 3-策略特殊, 4-策略兜底)
strategy_type: 策略类型(1-离线策略, 2-事件策略)
biz_type: 业务类型(old-cust, new-cust)
```

<h4 id="qx89K">11.6.2 流控规则类型</h4>
```java
// 代码位置: FlowCtrlTypeEnum
public enum FlowCtrlTypeEnum {
    CHANNEL(2, 1, "渠道"),        // 渠道级流控
    STRATEGY(1, 2, "策略"),       // 策略级流控
    MULTI_STRATEGY(3, 3, "多策略共享"), // 多策略共享流控
    BIZ_LINE(4, 4, "业务线");     // 业务线级流控
}
```

<h3 id="DYZiU">11.7 流控核心逻辑</h3>
<h4 id="sEngT">11.7.1 流控规则获取</h4>
```java
// 代码位置: FlowCtrlCoreServiceImpl.getFlowCtrlRule()
public List<FlowCtrlDo> getFlowCtrlRule(Long strategyId, Integer channel, String bizType) {
    List<FlowCtrlDo> flowCtrlConfig = cacheflowCtrlSerivce.getFlowCtrlConfig(channel, strategyId, bizType);
    // 按优先级排序：1-渠道特殊 > 2-渠道兜底 > 3-策略特殊 > 4-策略兜底
    return flowCtrlConfig.stream()
            .sorted(Comparator.comparing(FlowCtrlDo::getPriority))
            .collect(Collectors.toList());
}
```

<h4 id="AnL5y">11.7.2 用户触达次数查询</h4>
```java
// 代码位置: UserDispatchDetailServiceImpl.getUserIndexNew()
public List<UserDispatchIndexDto> getUserIndexNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple,
                                                  List<Long> userIdList, List<Integer> statusList) {
    // 查询用户历史触达次数
    Map<Long, Integer> indexMap = this.queryIndexNew(tableNameNo, triple, userIdList, statusList);

    // 构建用户触达指标
    List<UserDispatchIndexDto> userDispatchIndexDtoList = userIdList.stream().map(userId -> {
        UserDispatchIndexDto userDispatchIndexDto = new UserDispatchIndexDto();
        userDispatchIndexDto.setUserId(userId);
        userDispatchIndexDto.setCount(Optional.ofNullable(indexMap.get(userId)).orElse(0));
        return userDispatchIndexDto;
    }).collect(Collectors.toList());

    return userDispatchIndexDtoList;
}
```

<h4 id="ICtxd">11.7.3 流控判断逻辑</h4>
```java
// 代码位置: FlowCtrlCoreServiceImpl.interception()
private boolean interception(FlowCtrlDo rule, UserDispatchIndexDto indexDto) {
    // 判断用户触达次数是否超过限制
    return Objects.nonNull(rule.getLimitTimes()) && indexDto.getCount() >= rule.getLimitTimes();
}
```

<h3 id="Y9LZS">11.8 用户触达次数统计</h3>
<h4 id="QijWO">11.8.1 数据来源</h4>
1. **本地数据库**: `user_dispatch_detail_{YYYYMM}` 表
2. **ADS数仓**: 历史触达数据查询
3. **特征平台**: 新版流控接口

<h4 id="BSN4Q">11.8.2 查询策略</h4>
```java
// 代码位置: UserDispatchDetailServiceImpl.queryIndexNew()
private Map<Long, Integer> queryIndexNew(String tableNameNo, Triple<Long, Integer, FlowCtrlDo> triple,
                                         List<Long> userIdList, List<Integer> statusList) {
    FlowCtrlTypeEnum flowCtrlTypeEnum = FlowCtrlTypeEnum.getInstance(triple.getRight().getType());
    Map<Long, Integer> indexMap = new HashMap<>();

    for (List<Long> list : Lists.partition(userIdList, strategyConfig.getDetailBatchSize())) {
        if (WhitelistSwitchUtil.commonGraySwitchByApollo("" + triple.getLeft(), "newFlowCtrlRuleSwitch")) {
            // 使用特征平台新接口
            Map<Long, Integer> adsIndexMap = this.getFeatureIndexMap(flowCtrlTypeEnum, triple, list);
            adsIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));
        } else {
            // 查询本地数据库
            Map<Long, Integer> localIndexMap = this.getLocalIndexMapNew(tableNameNo, triple, list, statusList);
            localIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));

            if (triple.getRight().getLimitDays() <= 1) {
                continue; // 只查当天数据
            }

            // 查询ADS数仓历史数据
            Map<Long, Integer> adsIndexMap = this.getAdsIndexMap(flowCtrlTypeEnum, triple, list);
            adsIndexMap.forEach((userId, count) -> indexMap.computeIfPresent(userId, (k, v) -> v + count));
        }
    }

    return indexMap;
}
```

<h4 id="XuhDw">11.8.3 SQL查询示例</h4>
```sql
-- 查询用户触达次数 (策略级流控)
SELECT user_id, count(1) AS count
FROM user_dispatch_detail_202501
WHERE strategy_id = #{strategyId}
  AND market_channel = #{channel}
  AND dispatch_time BETWEEN #{startDate} AND #{endDate}
  AND (dispatch_type IS NULL OR dispatch_type = 'MKT')
  AND user_id IN (#{userIds})
  AND status IN (#{statusList})
GROUP BY user_id;

-- 查询用户触达次数 (渠道级流控)
SELECT user_id, count(1) AS count
FROM user_dispatch_detail_202501
WHERE market_channel = #{channel}
  AND dispatch_time BETWEEN #{startDate} AND #{endDate}
  AND (dispatch_type IS NULL OR dispatch_type = 'MKT')
  AND user_id IN (#{userIds})
  AND status IN (#{statusList})
GROUP BY user_id;
```

<h3 id="VNCHt">11.9 流控拦截日志</h3>
<h4 id="IQd8t">11.9.1 拦截日志记录</h4>
```java
// 代码位置: FlowCtrlCoreServiceImpl.saveInterceptionLog()
private void saveInterceptionLog(FlowCtrlDto flowCtrlDto, List<ImmutablePair<Long, Long>> flowCtrlRefuseList) {
    flowCtrlInterceptionLogService.saveInterceptionLog(flowCtrlDto, flowCtrlRefuseList);
}
```

<h4 id="fsyXx">11.9.2 拦截日志表结构 (flow_ctrl_interception_log)</h4>
```sql
-- 流控拦截日志表
id: 日志ID
flow_ctrl_id: 流控规则ID
user_id: 用户ID
strategy_id: 策略ID
market_channel: 渠道类型
interception_time: 拦截时间
message_id: 消息ID
trigger_datetime: 触发时间
biz_event_type: 事件类型
```

<h3 id="WaZWB">11.10 流控配置管理</h3>
<h4 id="h9ihC">11.10.1 Apollo配置项</h4>
```properties
# 触达流控开关 (按渠道)
singleDispatchFlc.1=true    # 短信流控开关
singleDispatchFlc.2=true    # 电销流控开关
singleDispatchFlc.3=true    # 优惠券流控开关
singleDispatchFlc.5=true    # Push流控开关

# 分布式流控开关 (按渠道)
singleDispatchFlc.lock.1=true    # 短信分布式流控
singleDispatchFlc.lock.2=true    # 电销分布式流控
singleDispatchFlc.lock.3=true    # 优惠券分布式流控
singleDispatchFlc.lock.5=true    # Push分布式流控

# 分布式流控配置
singleDispatchFlc.lock.ignore=false           # 忽略分布式锁开关
singleDispatchFlc.lock.perWaitMills=100       # 锁等待时间(毫秒)

# 事件级流控配置
eventFlc.config={"Notify_LifeRights":300,"Notify_XDayInterestFree":600}
```

<h4 id="BkBpj">11.10.2 流控规则优先级</h4>
1. **渠道规则-特殊规则** (priority=1): 针对特定渠道的特殊流控规则
2. **渠道规则-兜底规则** (priority=2): 渠道级别的通用流控规则
3. **策略规则-特殊规则** (priority=3): 针对特定策略的特殊流控规则
4. **策略规则-兜底规则** (priority=4): 策略级别的通用流控规则

<h3 id="wp9hZ">11.11 流控监控指标</h3>
<h4 id="chhtB">11.11.1 关键监控指标</h4>
+ **流控拦截率**: 各维度流控规则的拦截比例
+ **流控拦截数量**: 每日/每小时流控拦截的用户数量
+ **流控规则命中率**: 各流控规则的命中情况
+ **分布式锁获取成功率**: 分布式锁的获取成功率
+ **流控查询耗时**: 用户触达次数查询的耗时

<h4 id="heU9Y">11.11.2 流控告警</h4>
+ **流控拦截率过高**: 当流控拦截率超过阈值时告警
+ **分布式锁获取失败**: 分布式锁获取失败率过高时告警
+ **流控查询超时**: 用户触达次数查询超时告警

<h3 id="K8cI7">11.12 流控性能优化</h3>
<h4 id="sIWZS">11.12.1 缓存优化</h4>
+ **流控规则缓存**: 将流控规则缓存到Redis，减少数据库查询
+ **用户触达次数缓存**: 缓存用户当日触达次数，减少重复查询
+ **分布式锁优化**: 使用Redis Lua脚本优化分布式锁性能

<h4 id="mu9id">11.12.2 查询优化</h4>
+ **分表查询**: 按月分表存储用户触达明细，提高查询性能
+ **索引优化**: 在user_id、strategy_id、market_channel等字段上建立复合索引
+ **批量查询**: 批量查询用户触达次数，减少数据库连接开销

<h4 id="BqCOs">11.12.3 异步处理</h4>
+ **异步记录拦截日志**: 异步记录流控拦截日志，不影响主流程性能
+ **异步更新统计**: 异步更新流控统计数据

<h2 id="iGOC0">12. 性能优化点</h2>
<h3 id="waBoH">12.1 缓存优化</h3>
+ **策略配置缓存**: 减少数据库查询
+ **标签数据缓存**: 减少ADS服务调用
+ **模板内容缓存**: 减少模板查询
+ **流控规则缓存**: 缓存流控规则配置

<h3 id="wwjUz">12.2 异步处理</h3>
+ **MQ异步发送**: 电销等渠道使用MQ异步发送
+ **批量处理**: 支持批量用户处理
+ **并行处理**: 多渠道并行发送
+ **异步日志记录**: 异步记录流控拦截日志

<h3 id="eDTV8">12.3 流控保护</h3>
+ **多维度流控**: 用户级、策略级、渠道级、业务线级流控
+ **熔断机制**: 外部服务异常时的熔断保护
+ **分布式锁**: 防止并发触达的分布式锁机制
+ **优先级控制**: 流控规则按优先级执行

<h2 id="fT1I0">13. 总结</h2>
T0引擎触达链路是一个复杂的实时营销系统，从MQ消息消费开始，经过预筛、复筛、引擎决策等多个阶段，最终通过`marketingSend`方法实现多渠道的营销触达。整个链路涉及多个数据表、外部服务和配置中心，需要完善的监控和异常处理机制来保证系统的稳定性和可靠性。

<h3 id="K0t98">13.1 关键特点</h3>
1. **实时性**: 支持实时事件触发和处理
2. **多渠道**: 支持短信、电销、优惠券、Push等多种触达渠道
3. **智能化**: 集成决策引擎，支持智能营销决策
4. **可配置**: 丰富的配置项，支持灵活的策略配置
5. **高可用**: 完善的流控、熔断和监控机制
6. **多层流控**: 事件级、触达级、分布式三层流控保护

<h3 id="G3bVq">13.2 流控机制总结</h3>
流控是T0引擎触达链路的核心保护机制，通过三层流控确保用户不被过度营销：

<h4 id="IDHAo">13.2.1 三层流控架构</h4>
1. **事件级流控**: 在MQ消息消费阶段，防止同一用户短时间内重复触发相同事件
2. **触达级流控**: 在触达执行阶段，根据流控规则限制用户触达频次
3. **分布式流控**: 使用分布式锁防止并发场景下的重复触达

<h4 id="QTpdB">13.2.2 流控规则体系</h4>
+ **多维度**: 支持策略级、渠道级、多策略共享、业务线级流控
+ **多时间窗口**: 支持日、周、月等不同时间窗口的触达限制
+ **优先级控制**: 流控规则按优先级执行，确保特殊规则优先生效
+ **灵活配置**: 通过Apollo配置中心实现流控开关的动态控制

<h4 id="b0b67">13.2.3 流控判断逻辑</h4>
```plain
用户是否被流控 = 用户历史触达次数 >= 流控规则限制次数
```

其中用户历史触达次数来源于：

+ 本地数据库：当日触达记录
+ ADS数仓：历史触达数据
+ 特征平台：新版流控接口

<h4 id="kbqhO">13.2.4 流控数据表</h4>
+ **flow_ctrl**: 流控规则配置表
+ **user_dispatch_detail_{YYYYMM}**: 用户触达明细表（按月分表）
+ **flow_ctrl_interception_log**: 流控拦截日志表
+ **user_send_counter**: 用户发送计数表

<h3 id="xWqQC">13.3 技术亮点</h3>
1. **分布式锁机制**: 使用Redis分布式锁防止并发触达
2. **按月分表**: 用户触达明细按月分表，提高查询性能
3. **多数据源**: 结合本地数据库、ADS数仓、特征平台查询用户触达次数
4. **优先级排序**: 流控规则按优先级排序执行
5. **开关控制**: 通过Apollo配置实现流控开关的动态控制
6. **异步处理**: 异步记录流控拦截日志，不影响主流程性能

<h3 id="Z3DVU">13.4 监控和运维</h3>
1. **关键指标**: 流控拦截率、分布式锁成功率、查询耗时等
2. **告警机制**: 流控拦截率过高、分布式锁失败等异常告警
3. **日志记录**: 完整的流控拦截日志，便于问题排查
4. **性能优化**: 缓存优化、索引优化、批量查询等性能优化手段

这套流控机制确保了T0引擎触达系统的稳定性和用户体验，是整个营销触达链路的重要保障。

