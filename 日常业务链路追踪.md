# 日常业务链路追踪分析报告

## 1. 概述

本文档基于线上执行日志 `7a1fa642-454e-4d27-b5b5-ea3b67c57e60.json` 分析，串联整个XYF-CDP系统的业务流程，展现从事件接收到触达执行的完整链路。

## 2. 日志概要信息

### 2.1 基础信息
- **时间**: 2025-06-27 08:29:53
- **TraceId**: xyf-cdp-0a140f4b-486384-1798128/38059d2a447f641e
- **用户ID**: 1119724730
- **手机号**: 18534461136
- **事件类型**: Start (用户启动事件)
- **应用**: xyf01
- **内部应用**: xyf01_yqg
- **渠道来源**: XYF01-API-YQG01

### 2.2 处理结果
- **预筛状态**: 部分策略通过，部分策略因人群包筛选失败
- **策略数量**: 8个策略 [321,956,1029,1241,963,1458,2428,2432]
- **失败原因**: 离线人群包筛选不通过

## 3. 完整业务流程链路

### 3.1 整体流程图

```mermaid
graph TD
    A[业务系统事件] --> B[RabbitMQ消息队列]
    B --> C[BizEventMqService消息消费]
    C --> D[MqConsumeServiceImpl.bizEventProcess]
    D --> E[事件流控检查isReject]
    E --> F[StrategyEventDispatchServiceImpl.prescreen]
    F --> G[策略匹配与初始化]
    G --> H[用户信息查询CIS]
    H --> I[AB测试分组]
    I --> J[策略分组匹配]
    J --> K[引擎灰度判定]
    K --> L[人群包筛选]
    L --> M{筛选结果}
    M -->|通过| N[延迟消息发送]
    M -->|失败| O[决策结果记录]
    N --> P[复筛阶段rescreen]
    P --> Q[触达执行dispatch]

    style A fill:#e1f5fe
    style F fill:#fff3e0
    style L fill:#fce4ec
    style M fill:#ffeb3b
    style O fill:#ffcdd2
```

### 3.2 详细执行步骤

#### 3.2.1 消息接收与路由
**代码位置**: `BizEventMqService.java:280`
```java
mqConsumeService.bizEventProcess(messageId, bizEventMessageVO);
```

**执行日志**:
```
2025-06-27 08:29:53.177 事件节点消息消费-高优先级, messageId：202506270829531470884548
2025-06-27 08:29:53.178 事件节点消息消费, messageId:202506270829531470884548, 消息内容:{"biz_event_type":"Start","mobile":"18534461136","app":"xyf01"...}
```

**业务逻辑**:
1. RabbitMQ接收业务系统推送的Start事件
2. 消息去重验证(Redis)
3. 解析消息内容为BizEventMessageVO对象
4. 调用业务处理方法

#### 3.2.2 事件流控检查
**代码位置**: `MqConsumeServiceImpl.java:474`
```java
if (isReject(bizEventMessageVO)) {
    log.info("事件命中流控丢弃, event:{}, userId:{}", bizEventMessageVO.getBizEventType(), userId);
    return;
}
```

**执行日志**:
```
2025-06-27 08:29:53.179 开始进行事件流控逻辑, userId:1119724730, 事件名称:Start
```

**业务逻辑**:
1. 检查事件类型是否在流控白名单
2. 验证用户是否触发频次限制
3. Apollo配置的流控规则校验

#### 3.2.3 策略预筛阶段
**代码位置**: `StrategyEventDispatchServiceImpl.java:205`
```java
public void prescreen(String messageId, BizEventMessageVO bizEventMessageVO)
```

**执行日志**:
```
2025-06-27 08:29:53.179 实时策略-预筛开始,事件:Start,用户ID:null,消息ID:202506270829531470884548
2025-06-27 08:29:53.180 消息ID=202506270829531470884548, 事件:Start, 用户ID:null, 需要处理的策略IDs:[321,956,1029,1241,963,1458,2428,2432]
```

**业务逻辑**:
1. 根据事件类型匹配相关策略
2. 策略有效期校验
3. 策略状态检查(启用/禁用)

#### 3.2.4 用户信息查询与补全
**代码位置**: `UserCenterClient.java`
```java
getUserByMobile from cis, mobile:18534461136, app:xyf01
```

**执行日志**:
```
2025-06-27 08:29:53.183 cis service named queryUserNoByMobileAndApp, url:http://cis-query.xinfei.io/standard/queryUserNoByMobileAndApp
2025-06-27 08:29:53.188 cis service [queryRegisterInfoByUserNo], url:http://cis-query.xinfei.io/standard/queryRegisterInfoByUserNo, userNo:1119724730
```

**业务逻辑**:
1. 通过手机号查询用户ID
2. 查询用户注册信息
3. 补全用户基础属性(注册时间、渠道等)

#### 3.2.5 AB测试分组
**代码位置**: `RandomNumClient.java`
```java
AbClient request:requestId=b309817dfb514220b4769faef76f49ca, bizKey=xinkeyunying2025,bizId=1119724730
```

**执行日志**:
```
2025-06-27 08:29:53.189 AbClient response:requestId=b309817dfb514220b4769faef76f49ca,abResult={"bizId":"1119724730","bizKey":"xinkeyunying2025","group":"group2","randomNum":76}
```

**业务逻辑**:
1. 基于用户ID和业务场景生成随机数
2. 确保同一用户在相同场景下分组一致
3. 用于策略AB测试和灰度发布

#### 3.2.6 策略分组匹配
**代码位置**: `StrategyGroupServiceImpl.java`
```java
策略分组匹配，匹配人数：1，符合人数：0
策略分组匹配，匹配人数：1，符合人数：1
```

**执行日志**:
```
2025-06-27 08:29:53.189 策略分组匹配，匹配人数：1，符合人数：0
2025-06-27 08:29:53.190 策略分组匹配，匹配人数：1，符合人数：1
```

**业务逻辑**:
1. 根据策略配置的分组条件进行匹配
2. 支持多维度分组(渠道、用户属性等)
3. 只有匹配的分组才会继续执行

#### 3.2.7 引擎灰度判定
**代码位置**: `StrategyEventDispatchServiceImpl.java:249`
```java
log.info("引擎策略版本灰度流程判定, messageId:{}, 策略ID:{}, 用户id:{}", messageId, marketEvent.getStrategyId(), bizEventMessageVO.getCreditUserId());
```

**执行日志**:
```
2025-06-27 08:29:53.190 引擎策略版本灰度流程判定, messageId:202506270829531470884548, 策略ID:321, 用户id:1119724730
```

**业务逻辑**:
1. 判断策略是否为引擎策略
2. 引擎版本灰度流量控制
3. 决定是否调用决策引擎

#### 3.2.8 人群包筛选(关键失败点)
**代码位置**: `StrategyEventDispatchServiceImpl.java:421`
```java
Pair<Boolean, CrowdDetailDo> crowdPackVerify = this.crowdPackVerify(eventContext, crowdContent);
if (!Boolean.TRUE.equals(crowdPackVerify.getLeft())) {
    throw new StrategyException(String.format("离线人群包筛选不通过,用户ID:%s", eventContext.getBizEventVO().getAppUserId()));
}
```

**执行日志**:
```
2025-06-27 08:29:53.204 CrowdInfoService isCheckByInsightPlatform true, crowdPackIds=[199, 197]
2025-06-27 08:29:53.226 CrowdInfoService checkByInsightPlatform request={"crowdIds":[199,197],"hitAll":false,"uniqueKey":"1119724730"} response={"code":200,"data":{"hitResult":false,"missingLatestRunCycleCrowds":[],"unableJudgeCrowds":[]}}
2025-06-27 08:29:53.226 CrowdInfoService checkByInsightPlatform false, crowdPackIds=[199, 197] userNo=1119724730
```

**业务逻辑**:
1. 查询策略配置的人群包ID [199, 197]
2. 调用洞察平台接口验证用户是否在人群包中
3. 用户1119724730不在指定人群包中，筛选失败

#### 3.2.9 延迟消息处理(成功策略)
**代码位置**: `StrategyEventDispatchServiceImpl.java:489`
```java
private void delayHandler(StrategyEventCheckContext eventContext)
```

**执行日志**:
```
2025-06-27 08:29:53.194 producer.send, exchange=exchange_biz_event_delay_topic, queueName=queue_biz_event_delay_ll_xyf_cdp, bindingKey=key_biz_event_delay_ll_xyf_cdp, messageId=202506270829531470884548_00000321
2025-06-27 08:29:53.195 发送延迟消息低优先级, 消息id:202506270829531470884548_00000321, 延迟时间:1800
```

**业务逻辑**:
1. 计算延迟时间(1800秒 = 30分钟)
2. 发送到延迟队列等待复筛
3. 延迟消息包含完整的事件上下文

#### 3.2.10 决策结果记录(失败策略)
**代码位置**: `BizEventMqConfig.java`
```java
发送决策结果消息成功 消息id:202506270829531470884548_00000956
```

**执行日志**:
```
2025-06-27 08:29:53.232 发送决策结果消息成功 消息id:202506270829531470884548_00000956，消息内容:{"decisionResult":false,"failCode":503,"failReason":"离线人群包筛选不通过"}
```

**业务逻辑**:
1. 记录策略执行失败结果
2. 失败码503表示人群包筛选不通过
3. 用于后续数据分析和监控

## 4. 关键技术组件分析

### 4.1 消息队列架构
- **高优先级队列**: 实时事件处理
- **延迟队列**: 复筛阶段处理
- **决策结果队列**: 结果记录和统计

### 4.2 外部服务依赖
- **CIS用户中心**: 用户信息查询
- **随机数服务**: AB测试分组
- **洞察平台**: 人群包验证
- **Apollo配置**: 流控规则和开关

### 4.3 数据流转
1. **事件数据**: 业务系统 → MQ → CDP系统
2. **用户数据**: CIS查询 → 本地缓存
3. **决策数据**: 策略引擎 → 触达执行
4. **结果数据**: 执行结果 → 数据仓库

## 5. 性能与监控指标

### 5.1 处理时效
- **消息消费**: 毫秒级响应
- **预筛阶段**: 15ms (策略321)
- **人群包查询**: 22ms (洞察平台调用)
- **总体耗时**: 约50ms

### 5.2 关键监控点
- **消息堆积**: MQ队列深度
- **处理成功率**: 策略通过率
- **外部服务**: CIS/洞察平台响应时间
- **流控触发**: 事件流控统计

## 6. 异常处理机制

### 6.1 人群包筛选失败
- **异常类型**: StrategyException
- **处理方式**: 记录失败原因，发送决策结果
- **影响范围**: 单个策略，不影响其他策略

### 6.2 外部服务异常
- **重试机制**: 配置化重试次数
- **降级策略**: 缓存兜底数据
- **监控告警**: 异常率阈值告警

## 7. 业务价值分析

### 7.1 实时性保障
- 事件驱动的实时处理架构
- 毫秒级响应用户行为
- 支持个性化营销触达

### 7.2 精准营销
- 多维度用户分群
- AB测试支持
- 人群包精准匹配

### 7.3 系统稳定性
- 完善的流控机制
- 异常隔离处理
- 全链路监控追踪

## 9. 多链路分析

### 9.1 为什么出现同一请求ID下的多个链路？

**主要原因**: 同一个业务事件触发了多个策略的并行处理，每个策略都会生成不同的子链路TraceId。

**TraceId结构分析**:
- **主TraceId**: `xyf-cdp-0a140f4b-486384-1798128` (业务事件处理的主链路)
- **子TraceId**:
  - `38059d2a447f641e` - 策略预筛阶段的主要处理链路 (08:29:53)
  - `784c846bcb6f74e2` - 延迟消息发送链路 (08:29:53)
  - `c7d7dbfd3cdf79c7` - 决策结果发送链路 (08:29:53)
  - `790028469219a775` - 策略1458复筛链路 (08:34:54, 延迟5分钟)
  - `779ef12da1104fd5` - 策略2428引擎复筛链路 (08:44:54, 延迟15分钟)
  - `60be5d9b234cf8b1` - 策略2432引擎复筛链路 (08:59:54, 延迟30分钟)
  - `2b25078662fab9dc` - 策略321引擎复筛链路 (08:59:54, 延迟30分钟)
  - `f2a1179e44a7ba7b` - 策略963复筛链路 (09:09:54, 延迟40分钟)
  - `fbab4be49cf1316e` - 策略963触达执行链路 (09:09:54, 延迟40分钟)

### 9.2 其他关键链路分析

#### 9.2.1 策略1458复筛链路 (790028469219a775)
**时间**: 2025-06-27 08:34:54 (延迟5分钟后)
**策略**: 1458 (策略类型0-普通策略)
**关键步骤**:
1. **延迟消息消费**: 从中优先级队列接收策略1458的消息
2. **复筛开始**: 实时策略复筛阶段
3. **标签查询**: 调用特征平台查询用户标签
   - f_is_airline_travel_user: 0 (非航空旅行用户)
   - not_finish_borrow: 1 (有未完成借款)
   - is_user_forbid_apply: 0 (未禁止申请)
   - risk_black_list: 0 (非风险黑名单)
4. **特征平台调用**: 灰度策略开关通过，调用特征平台
5. **复筛失败**: 标签条件筛选不通过 (not_finish_borrow == 0 条件不满足)

#### 9.2.2 策略2428引擎复筛链路 (779ef12da1104fd5)
**时间**: 2025-06-27 08:44:54 (延迟15分钟后)
**策略**: 2428 (策略类型1-引擎策略)
**引擎名称**: API_T0_mkt
**关键步骤**:
1. **延迟消息消费**: 从低优先级队列接收策略2428的消息
2. **引擎复筛开始**: 策略引擎实时策略复筛
3. **决策引擎调用**: 调用决策引擎获取触达方案
   - 引擎返回: 两个营销组 not_apichannel(spc) + not_api_channel(tel)
   - 决策结果: 无具体触达动作(dispatch为空)
4. **标签查询**: 调用特征平台查询用户标签
   - f_is_airline_travel_user: 0 (非航空旅行用户)
   - market_black_list: 0 (非营销黑名单)
5. **复筛成功**: 引擎复筛完成，耗时307ms，记录决策结果

#### 9.2.3 策略2432引擎复筛链路 (60be5d9b234cf8b1)
**时间**: 2025-06-27 08:59:54 (延迟30分钟后)
**策略**: 2432 (策略类型1-引擎策略)
**引擎名称**: Tel_notLoan_MOT_Login
**关键步骤**:
1. **延迟消息消费**: 从低优先级队列接收策略2432的消息
2. **引擎复筛开始**: 策略引擎实时策略复筛
3. **决策引擎调用**: 调用决策引擎获取触达方案
   - 引擎返回: 短信模板smstpl712140 + Push模板pushtpl200097
4. **标签查询**: 调用特征平台查询用户标签
   - f_is_airline_travel_user: 0
   - is_user_forbid_apply: 0
   - market_black_list: 0
   - risk_black_list: 0
5. **触达失败**: 短信模板详情获取异常，无短信模板内容

#### 9.2.4 策略321引擎复筛链路 (2b25078662fab9dc)
**时间**: 2025-06-27 08:59:54 (延迟30分钟后)
**策略**: 321 (策略类型1-引擎策略)
**引擎名称**: Tel_notCredit_MOT_Login
**关键步骤**:
1. **引擎复筛**: rescreenWithEngine耗时325ms
2. **引擎决策**: 调用决策引擎进行复筛判断
3. **处理结果**: 引擎复筛完成，用户ID:1119724730

#### 9.2.5 策略963复筛链路 (f2a1179e44a7ba7b)
**时间**: 2025-06-27 09:09:54 (延迟40分钟后)
**策略**: 963 (策略类型0-普通策略)
**关键步骤**:
1. **延迟消息消费**: 从低优先级队列接收策略963的消息
2. **复筛开始**: 实时策略复筛阶段
3. **标签查询**: 调用特征平台查询用户标签
   - f_is_airline_travel_user: 0 (非航空旅行用户)
   - is_start_act_borrow: 0 (未启动借款行为)
   - user_borrow_channel: XYF01-API-YQG01 (借款渠道)
   - is_user_forbid_apply: 0 (未禁止申请)
   - market_black_list: 0 (非营销黑名单)
   - user_register_channel: XYF01-API-YQG01 (注册渠道)
   - risk_black_list: 0 (非风险黑名单)
4. **特征平台调用**: 灰度策略开关通过，标签查询耗时162ms
5. **复筛通过**: 进入触达执行阶段

#### 9.2.6 策略963触达执行链路 (fbab4be49cf1316e)
**时间**: 2025-06-27 09:09:54
**策略**: 963
**关键步骤**:
1. **流控检查**: 查询用户历史触达次数
   - 流控规则580: 每1天1次
   - 查询结果: 用户无历史触达记录
2. **分布式锁**: dispatchFlcLock继续下发
3. **触达发送**: TelePushMqProducer发送到电销系统
   - 批次号: 1029072904794759169
   - 模板ID: 54
   - 策略类型: realtime
4. **发送成功**: 接口耗时26ms，触达结束耗时45ms

#### 9.2.7 决策结果记录链路 (c7d7dbfd3cdf79c7)
**策略**: 956 (失败策略)
**失败原因**: 离线人群包筛选不通过
**记录内容**:
```json
{
  "decisionResult": false,
  "failCode": 503,
  "failReason": "离线人群包筛选不通过",
  "strategyId": 956,
  "strategyType": 0
}
```

### 9.3 完整业务流程时序图

```mermaid
sequenceDiagram
    participant BS as 业务系统
    participant MQ as RabbitMQ
    participant CDP as XYF-CDP
    participant CIS as 用户中心
    participant IP as 洞察平台
    participant FP as 特征平台
    participant DE as 决策引擎
    participant TEL as 电销系统

    Note over BS,TEL: 08:29:53 - 预筛阶段
    BS->>MQ: Start事件
    MQ->>CDP: 消息消费(38059d2a447f641e)
    CDP->>CIS: 查询用户信息
    CDP->>IP: 人群包验证[199,197]
    IP-->>CDP: 验证失败(部分策略)
    CDP->>MQ: 延迟消息(784c846bcb6f74e2)
    CDP->>MQ: 决策结果(c7d7dbfd3cdf79c7)

    Note over BS,TEL: 08:34:54 - 策略1458复筛(延迟5分钟)
    MQ->>CDP: 延迟消息消费(790028469219a775)
    CDP->>FP: 标签查询
    CDP-->>CDP: 复筛失败(标签条件不满足)

    Note over BS,TEL: 08:44:54 - 策略2428引擎复筛(延迟15分钟)
    MQ->>CDP: 延迟消息消费(779ef12da1104fd5)
    CDP->>DE: 调用决策引擎
    DE-->>CDP: 返回营销组(无触达动作)
    CDP->>FP: 标签查询
    CDP->>CDP: 复筛成功，记录决策结果

    Note over BS,TEL: 08:59:54 - 引擎策略复筛(延迟30分钟)
    MQ->>CDP: 策略2432消费(60be5d9b234cf8b1)
    CDP->>DE: 调用决策引擎
    DE-->>CDP: 返回触达方案
    CDP->>FP: 标签查询
    CDP-->>CDP: 短信模板异常，触达失败

    MQ->>CDP: 策略321消费(2b25078662fab9dc)
    CDP->>DE: 引擎复筛
    DE-->>CDP: 复筛完成

    Note over BS,TEL: 09:09:54 - 策略963复筛与触达(延迟40分钟)
    MQ->>CDP: 延迟消息消费(f2a1179e44a7ba7b)
    CDP->>FP: 标签查询
    CDP->>CIS: 用户信息查询
    CDP->>CDP: 流控检查(fbab4be49cf1316e)
    CDP->>TEL: 触达发送
    TEL-->>CDP: 发送成功
```

### 9.4 策略执行结果汇总

| 策略ID | 策略类型 | 执行结果 | 失败原因 | 处理链路 | 延迟时间 |
|--------|----------|----------|----------|----------|----------|
| 321 | 1(引擎策略) | 引擎复筛完成 | - | 784c846bcb6f74e2 + 2b25078662fab9dc | 30分钟 |
| 956 | 0(普通策略) | 失败 | 人群包筛选不通过 | c7d7dbfd3cdf79c7 | 立即 |
| 963 | 0(普通策略) | 成功触达 | - | f2a1179e44a7ba7b + fbab4be49cf1316e | 40分钟 |
| 1029 | 0(普通策略) | 失败 | 人群包筛选不通过 | c7d7dbfd3cdf79c7 | 立即 |
| 1241 | 0(普通策略) | 失败 | 人群包筛选不通过 | c7d7dbfd3cdf79c7 | 立即 |
| 1458 | 0(普通策略) | 复筛失败 | 标签条件不满足 | 790028469219a775 | 5分钟 |
| 2428 | 1(引擎策略) | 引擎复筛成功 | - | 784c846bcb6f74e2 + 779ef12da1104fd5 | 15分钟 |
| 2432 | 1(引擎策略) | 触达失败 | 短信模板异常 | 60be5d9b234cf8b1 | 30分钟 |

### 9.5 延迟时间分析

**延迟时间规律**:
- **立即处理**: 人群包筛选失败的策略直接记录决策结果
- **5分钟延迟**: 策略1458 (中优先级队列)
- **15分钟延迟**: 策略2428 (低优先级队列)
- **30分钟延迟**: 引擎策略321、2432 (低优先级队列)
- **40分钟延迟**: 策略963 (低优先级队列)

**队列优先级**:
- **高优先级**: 实时事件消费
- **中优先级**: 部分普通策略延迟处理
- **低优先级**: 引擎策略和其他普通策略延迟处理

**重要发现**:
- 策略2428并没有"消失"，而是在08:44:54进行了引擎复筛
- 不同策略的延迟时间不同，可能与策略配置或系统负载有关
- 引擎策略通过决策引擎返回营销组，但不一定有具体的触达动作

## 10. 优化建议

### 10.1 性能优化
1. **缓存优化**: 用户信息本地缓存
2. **批量处理**: 人群包查询批量化
3. **异步化**: 非关键路径异步处理

### 10.2 可靠性提升
1. **重试机制**: 外部服务调用重试
2. **熔断降级**: 服务异常时的降级策略
3. **数据一致性**: 分布式事务保障

### 10.3 监控完善
1. **业务监控**: 策略执行成功率
2. **技术监控**: 系统资源使用率
3. **告警机制**: 异常情况及时通知

### 10.4 链路追踪优化
1. **TraceId规范**: 建立清晰的主子链路关系
2. **日志聚合**: 按业务事件聚合多个子链路
3. **可视化监控**: 实时展示多策略执行状态